/**
 * Test the transaction fix
 */

import { transactionService } from '@/services/database/transactionService';
import { inspectLoyaltyTransactions } from './databaseTest';

export const testTransactionFix = async () => {
  console.log('🧪 Testing transaction fix...');
  
  try {
    // First inspect the table structure
    console.log('1. Inspecting loyalty_transactions table...');
    const inspection = await inspectLoyaltyTransactions();
    console.log('Inspection result:', inspection);
    
    // Test loading transactions
    console.log('2. Testing transaction loading...');
    const result = await transactionService.getTransactions({
      limit: 5
    });
    
    if (result.error) {
      console.log('❌ Transaction loading failed:', result.error.message);
      return { success: false, error: result.error.message };
    }
    
    console.log('✅ Transaction loading succeeded!');
    console.log('📊 Loaded transactions:', result.data?.length || 0);
    
    if (result.data && result.data.length > 0) {
      console.log('📋 Sample transaction:', result.data[0]);
    }
    
    return { 
      success: true, 
      transactionCount: result.data?.length || 0,
      sampleTransaction: result.data?.[0] || null
    };
    
  } catch (error) {
    console.log('❌ Test failed:', error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    };
  }
};
