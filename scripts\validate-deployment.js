#!/usr/bin/env node

/**
 * Ocean Soul Sparkles Mobile App - Deployment Validation Script
 * Validates production readiness before deployment
 */

const fs = require('fs');
const path = require('path');

console.log('🌊 Ocean Soul Sparkles - Deployment Validation');
console.log('='.repeat(50));

let validationPassed = true;
const issues = [];
const warnings = [];

// Colors for console output
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m'
};

function logError(message) {
  console.log(`${colors.red}❌ ${message}${colors.reset}`);
  issues.push(message);
  validationPassed = false;
}

function logWarning(message) {
  console.log(`${colors.yellow}⚠️ ${message}${colors.reset}`);
  warnings.push(message);
}

function logSuccess(message) {
  console.log(`${colors.green}✅ ${message}${colors.reset}`);
}

function logInfo(message) {
  console.log(`${colors.blue}ℹ️ ${message}${colors.reset}`);
}

// Validation 1: Check if .env.production exists
console.log('\n📋 Checking production environment configuration...');
if (!fs.existsSync('.env.production')) {
  logError('.env.production file not found');
} else {
  logSuccess('.env.production file exists');
  
  // Check production environment variables
  const envContent = fs.readFileSync('.env.production', 'utf8');
  
  const requiredVars = [
    'EXPO_PUBLIC_SUPABASE_URL',
    'EXPO_PUBLIC_SUPABASE_ANON_KEY',
    'EXPO_PUBLIC_ENVIRONMENT',
    'EXPO_PUBLIC_APP_VERSION'
  ];
  
  requiredVars.forEach(varName => {
    if (envContent.includes(`${varName}=`)) {
      logSuccess(`${varName} configured`);
    } else {
      logError(`${varName} missing in .env.production`);
    }
  });
  
  // Check if environment is set to production
  if (envContent.includes('EXPO_PUBLIC_ENVIRONMENT=production')) {
    logSuccess('Environment set to production');
  } else {
    logError('EXPO_PUBLIC_ENVIRONMENT not set to production');
  }
  
  // Check for placeholder values
  if (envContent.includes('your-production-') || envContent.includes('PRODUCTION-')) {
    logWarning('Placeholder values detected in .env.production - replace with actual values');
  }
}

// Validation 2: Check package.json
console.log('\n📦 Checking package.json configuration...');
if (!fs.existsSync('package.json')) {
  logError('package.json not found');
} else {
  const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
  
  // Check required dependencies
  const requiredDeps = [
    'expo',
    'expo-notifications',
    'expo-device',
    'expo-constants',
    '@supabase/supabase-js',
    'react-native'
  ];
  
  requiredDeps.forEach(dep => {
    if (packageJson.dependencies && packageJson.dependencies[dep]) {
      logSuccess(`${dep} dependency found`);
    } else {
      logError(`${dep} dependency missing`);
    }
  });
  
  // Check app version
  if (packageJson.version) {
    logSuccess(`App version: ${packageJson.version}`);
  } else {
    logWarning('App version not specified in package.json');
  }
}

// Validation 3: Check app.config.js
console.log('\n⚙️ Checking app configuration...');
if (!fs.existsSync('app.config.js')) {
  logError('app.config.js not found');
} else {
  logSuccess('app.config.js exists');
  
  const configContent = fs.readFileSync('app.config.js', 'utf8');
  
  // Check for required configuration
  const requiredConfigs = [
    'expo-notifications',
    'expo-splash-screen',
    'bundleIdentifier',
    'package'
  ];
  
  requiredConfigs.forEach(config => {
    if (configContent.includes(config)) {
      logSuccess(`${config} configuration found`);
    } else {
      logWarning(`${config} configuration may be missing`);
    }
  });
}

// Validation 4: Check TypeScript compilation
console.log('\n🔍 Checking TypeScript compilation...');
try {
  const { execSync } = require('child_process');
  execSync('npx tsc --noEmit', { stdio: 'pipe' });
  logSuccess('TypeScript compilation successful');
} catch (error) {
  logError('TypeScript compilation failed');
  logInfo('Run "npx tsc --noEmit" to see detailed errors');
}

// Validation 5: Check for critical files
console.log('\n📁 Checking critical files...');
const criticalFiles = [
  'src/services/database/supabase.ts',
  'src/services/notifications/pushNotificationService.ts',
  'src/services/adminPortal/realTimeDataSyncService.ts',
  'src/utils/androidNotificationChannels.ts',
  'src/utils/iOSNotificationManager.ts'
];

criticalFiles.forEach(file => {
  if (fs.existsSync(file)) {
    logSuccess(`${file} exists`);
  } else {
    logError(`Critical file missing: ${file}`);
  }
});

// Validation 6: Check deployment scripts
console.log('\n🚀 Checking deployment scripts...');
if (fs.existsSync('scripts/deploy-production.sh')) {
  logSuccess('Production deployment script exists');
} else {
  logWarning('Production deployment script not found');
}

// Validation 7: Check documentation
console.log('\n📚 Checking documentation...');
const docFiles = [
  'PRODUCTION_DEPLOYMENT_GUIDE.md',
  'FINAL_DEPLOYMENT_CHECKLIST.md'
];

docFiles.forEach(file => {
  if (fs.existsSync(file)) {
    logSuccess(`${file} exists`);
  } else {
    logWarning(`Documentation file missing: ${file}`);
  }
});

// Final validation summary
console.log('\n' + '='.repeat(50));
console.log('📊 VALIDATION SUMMARY');
console.log('='.repeat(50));

if (validationPassed && issues.length === 0) {
  console.log(`${colors.green}🎉 VALIDATION PASSED! 🎉${colors.reset}`);
  console.log(`${colors.green}✅ Ocean Soul Sparkles app is ready for production deployment!${colors.reset}`);
} else {
  console.log(`${colors.red}❌ VALIDATION FAILED${colors.reset}`);
  console.log(`${colors.red}Critical issues must be resolved before deployment.${colors.reset}`);
}

console.log(`\n📊 Results:`);
console.log(`   Issues: ${issues.length}`);
console.log(`   Warnings: ${warnings.length}`);

if (issues.length > 0) {
  console.log(`\n${colors.red}🚨 Critical Issues:${colors.reset}`);
  issues.forEach(issue => console.log(`   - ${issue}`));
}

if (warnings.length > 0) {
  console.log(`\n${colors.yellow}⚠️ Warnings:${colors.reset}`);
  warnings.forEach(warning => console.log(`   - ${warning}`));
}

console.log('\n💡 Next Steps:');
if (validationPassed && issues.length === 0) {
  console.log('   1. 🔧 Replace placeholder values in .env.production');
  console.log('   2. 🏗️ Run production build: ./scripts/deploy-production.sh');
  console.log('   3. 🧪 Test builds on physical devices');
  console.log('   4. 📱 Submit to app stores');
  console.log('   5. 🎉 Celebrate successful deployment!');
} else {
  console.log('   1. 🔧 Fix all critical issues listed above');
  console.log('   2. 📋 Address warnings if possible');
  console.log('   3. 🔄 Re-run validation: node scripts/validate-deployment.js');
  console.log('   4. 📞 Contact development team if issues persist');
}

console.log(`\n${colors.blue}🌊 Ocean Soul Sparkles - Ready to make waves! 🌊${colors.reset}`);

// Exit with appropriate code
process.exit(validationPassed && issues.length === 0 ? 0 : 1);
