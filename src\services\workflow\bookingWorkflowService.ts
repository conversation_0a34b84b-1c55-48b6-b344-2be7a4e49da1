/**
 * Ocean Soul Sparkles Mobile App - Booking Workflow Service
 * Orchestrates the complete automated booking-to-quote workflow
 */

import { bookingService } from '@/services/database/bookingService';
import { quoteService } from '@/services/database/quoteService';
import { customerService } from '@/services/database/customerService';
import { serviceService } from '@/services/database/serviceService';
import { websiteBookingService, WebsiteBookingData } from '@/services/booking/websiteBookingService';
import { distancePricingService } from '@/services/pricing/distancePricingService';
import { quoteEmailService } from '@/services/email/quoteEmailService';
import { Booking, Quote, Customer, Service, DatabaseResponse } from '@/types/database';

export interface WorkflowStep {
  step: string;
  status: 'pending' | 'in_progress' | 'completed' | 'failed' | 'skipped';
  message: string;
  timestamp: string;
  data?: any;
  error?: string;
}

export interface WorkflowResult {
  success: boolean;
  booking?: Booking;
  quote?: Quote;
  customer?: Customer;
  service?: Service;
  steps: WorkflowStep[];
  requiresManualReview: boolean;
  reviewReasons: string[];
  error?: string;
}

export interface AutoQuoteConfig {
  enabled: boolean;
  maxDistanceKm: number;
  requiresApproval: boolean;
  autoSendEmail: boolean;
  staffId?: string;
}

export class BookingWorkflowService {
  private static instance: BookingWorkflowService;

  private constructor() {}

  public static getInstance(): BookingWorkflowService {
    if (!BookingWorkflowService.instance) {
      BookingWorkflowService.instance = new BookingWorkflowService();
    }
    return BookingWorkflowService.instance;
  }

  /**
   * Process complete workflow from website booking to quote
   */
  async processWebsiteBookingWorkflow(
    websiteData: WebsiteBookingData,
    autoQuoteConfig: AutoQuoteConfig
  ): Promise<WorkflowResult> {
    const steps: WorkflowStep[] = [];
    let booking: Booking | undefined;
    let quote: Quote | undefined;
    let customer: Customer | undefined;
    let service: Service | undefined;
    let requiresManualReview = false;
    const reviewReasons: string[] = [];

    try {
      console.log('🔄 Starting automated booking workflow...');

      // Step 1: Process website booking
      steps.push(this.createStep('website_booking', 'in_progress', 'Processing website booking data...'));
      
      const bookingResult = await websiteBookingService.processWebsiteBooking(websiteData);
      
      if (!bookingResult.success) {
        steps[steps.length - 1] = this.updateStep(steps[steps.length - 1], 'failed', bookingResult.error || 'Failed to process website booking');
        return this.buildFailureResult(steps, bookingResult.error || 'Website booking processing failed');
      }

      booking = bookingResult.booking;
      customer = bookingResult.customer;
      service = bookingResult.service;

      if (bookingResult.requiresReview) {
        requiresManualReview = true;
        reviewReasons.push(...(bookingResult.reviewReasons || []));
      }

      steps[steps.length - 1] = this.updateStep(steps[steps.length - 1], 'completed', 'Website booking processed successfully');

      // Step 2: Calculate distance-based pricing
      if (autoQuoteConfig.enabled && booking && customer && service) {
        steps.push(this.createStep('distance_pricing', 'in_progress', 'Calculating distance-based pricing...'));

        const pricingResult = await distancePricingService.calculateBookingPricing(booking, service, customer);

        if (pricingResult.success) {
          // Check if distance exceeds maximum allowed for auto-quote
          if (pricingResult.distance_km > autoQuoteConfig.maxDistanceKm) {
            requiresManualReview = true;
            reviewReasons.push(`Distance (${pricingResult.distance_km}km) exceeds auto-quote limit (${autoQuoteConfig.maxDistanceKm}km)`);
            steps[steps.length - 1] = this.updateStep(steps[steps.length - 1], 'completed', `Distance pricing calculated: ${pricingResult.distance_km}km - requires manual review`);
          } else {
            steps[steps.length - 1] = this.updateStep(steps[steps.length - 1], 'completed', `Distance pricing calculated: $${pricingResult.total_price.toFixed(2)} (${pricingResult.distance_km}km)`);
          }
        } else {
          requiresManualReview = true;
          reviewReasons.push('Distance pricing calculation failed');
          steps[steps.length - 1] = this.updateStep(steps[steps.length - 1], 'failed', pricingResult.error || 'Distance pricing failed');
        }

        // Step 3: Auto-generate quote if conditions are met
        if (autoQuoteConfig.enabled && !requiresManualReview && autoQuoteConfig.staffId) {
          steps.push(this.createStep('quote_generation', 'in_progress', 'Generating automated quote...'));

          const quoteResult = await this.generateAutomatedQuote(
            booking,
            customer,
            service,
            pricingResult,
            autoQuoteConfig.staffId
          );

          if (quoteResult.success && quoteResult.data) {
            quote = quoteResult.data;
            steps[steps.length - 1] = this.updateStep(steps[steps.length - 1], 'completed', `Quote generated: $${quote.estimated_total.toFixed(2)}`);

            // Step 4: Send email notification
            if (autoQuoteConfig.autoSendEmail) {
              steps.push(this.createStep('email_notification', 'in_progress', 'Sending quote email notification...'));

              const emailResult = await quoteEmailService.sendQuoteEmail(quote);

              if (emailResult.success) {
                steps[steps.length - 1] = this.updateStep(steps[steps.length - 1], 'completed', 'Quote email sent successfully');
              } else {
                steps[steps.length - 1] = this.updateStep(steps[steps.length - 1], 'failed', emailResult.error || 'Email notification failed');
                reviewReasons.push('Email notification failed');
              }
            } else {
              steps.push(this.createStep('email_notification', 'skipped', 'Email notification disabled in configuration'));
            }

            // Step 5: Update booking status
            steps.push(this.createStep('booking_update', 'in_progress', 'Updating booking status...'));

            const updateResult = await bookingService.updateBooking(booking.id, {
              status: 'pending',
              total_amount: quote.estimated_total,
              notes: `${booking.notes || ''}\n\n🤖 Automated quote generated: $${quote.estimated_total.toFixed(2)} - ${new Date().toISOString()}`,
            });

            if (updateResult.error) {
              steps[steps.length - 1] = this.updateStep(steps[steps.length - 1], 'failed', 'Failed to update booking status');
              reviewReasons.push('Booking status update failed');
            } else {
              steps[steps.length - 1] = this.updateStep(steps[steps.length - 1], 'completed', 'Booking status updated');
            }

          } else {
            steps[steps.length - 1] = this.updateStep(steps[steps.length - 1], 'failed', quoteResult.error?.message || 'Quote generation failed');
            requiresManualReview = true;
            reviewReasons.push('Automated quote generation failed');
          }
        } else {
          steps.push(this.createStep('quote_generation', 'skipped', 'Auto-quote disabled or requires manual review'));
        }
      } else {
        steps.push(this.createStep('distance_pricing', 'skipped', 'Auto-quote disabled'));
        steps.push(this.createStep('quote_generation', 'skipped', 'Auto-quote disabled'));
        steps.push(this.createStep('email_notification', 'skipped', 'Auto-quote disabled'));
        steps.push(this.createStep('booking_update', 'skipped', 'Auto-quote disabled'));
      }

      console.log('✅ Booking workflow completed successfully');

      return {
        success: true,
        booking,
        quote,
        customer,
        service,
        steps,
        requiresManualReview,
        reviewReasons,
      };

    } catch (error) {
      console.error('❌ Booking workflow error:', error);
      
      // Mark current step as failed
      if (steps.length > 0) {
        const lastStep = steps[steps.length - 1];
        if (lastStep.status === 'in_progress') {
          steps[steps.length - 1] = this.updateStep(lastStep, 'failed', error instanceof Error ? error.message : 'Unknown error');
        }
      }

      return this.buildFailureResult(steps, error instanceof Error ? error.message : 'Workflow failed');
    }
  }

  /**
   * Generate automated quote with calculated pricing
   */
  private async generateAutomatedQuote(
    booking: Booking,
    customer: Customer,
    service: Service,
    pricingResult: any,
    staffId: string
  ): Promise<DatabaseResponse<Quote>> {
    try {
      const quoteDescription = this.buildAutomatedQuoteDescription(service, pricingResult);

      return await quoteService.createQuote({
        customer_id: customer.id,
        customer_first_name: customer.full_name?.split(' ')[0] || '',
        customer_last_name: customer.full_name?.split(' ').slice(1).join(' ') || '',
        customer_email: customer.email,
        customer_phone: customer.phone,
        service_name: `Automated Quote for ${service.name}`,
        service_description: quoteDescription,
        estimated_total: pricingResult.total_price,
        status: 'sent',
        expires_at: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
        notes: '🤖 Automatically generated quote based on distance pricing',
      });

    } catch (error) {
      console.error('❌ Automated quote generation error:', error);
      return { 
        data: null, 
        error: error instanceof Error ? error : new Error('Failed to generate automated quote') 
      };
    }
  }

  /**
   * Build description for automated quote
   */
  private buildAutomatedQuoteDescription(service: Service, pricingResult: any): string {
    const lines = [`AUTOMATED QUOTE FOR ${service.name.toUpperCase()}`];
    lines.push('');
    lines.push('This quote has been automatically generated based on:');
    lines.push(`• Service: ${service.name}`);
    lines.push(`• Distance: ${pricingResult.distance_km}km`);
    lines.push(`• Pricing Tier: ${pricingResult.pricing_tier.name}`);
    lines.push('');
    lines.push('PRICING BREAKDOWN:');
    lines.push(`• Base Service: $${pricingResult.breakdown.service_base.toFixed(2)}`);
    
    if (pricingResult.breakdown.distance_adjustment > 0) {
      lines.push(`• Distance Adjustment: $${pricingResult.breakdown.distance_adjustment.toFixed(2)}`);
    }
    
    if (pricingResult.breakdown.travel_fee > 0) {
      lines.push(`• Travel Fee: $${pricingResult.breakdown.travel_fee.toFixed(2)}`);
    }
    
    lines.push('');
    lines.push(`TOTAL AMOUNT: $${pricingResult.total_price.toFixed(2)}`);
    lines.push('');
    lines.push('This quote is valid for 30 days from the date of issue.');
    lines.push('Please contact us to confirm your booking or if you have any questions.');

    return lines.join('\n');
  }

  /**
   * Process manual booking review workflow
   */
  async processManualReviewWorkflow(
    bookingId: string,
    reviewData: {
      approved: boolean;
      customPrice?: number;
      notes?: string;
      staffId: string;
    }
  ): Promise<WorkflowResult> {
    const steps: WorkflowStep[] = [];

    try {
      console.log('👤 Starting manual review workflow...');

      // Load booking data
      steps.push(this.createStep('load_booking', 'in_progress', 'Loading booking data...'));
      
      const bookingResult = await bookingService.getBookingById(bookingId);
      if (bookingResult.error || !bookingResult.data) {
        steps[steps.length - 1] = this.updateStep(steps[steps.length - 1], 'failed', 'Booking not found');
        return this.buildFailureResult(steps, 'Booking not found');
      }

      const booking = bookingResult.data;
      steps[steps.length - 1] = this.updateStep(steps[steps.length - 1], 'completed', 'Booking data loaded');

      if (reviewData.approved) {
        // Generate quote with manual review
        steps.push(this.createStep('manual_quote', 'in_progress', 'Generating reviewed quote...'));

        const finalPrice = reviewData.customPrice || booking.total_amount || 0;
        
        const quoteResult = await quoteService.createQuote({
          customer_id: booking.customer_id,
          created_by: reviewData.staffId,
          original_booking_id: booking.id,
          service_name: `Reviewed Quote for ${booking.service?.name || 'Service'}`,
          service_description: `MANUALLY REVIEWED QUOTE\n\nTotal Amount: $${finalPrice.toFixed(2)}\n\n${reviewData.notes || 'Quote approved after manual review.'}`,
          estimated_total: finalPrice,
          status: 'sent',
          expires_at: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
          notes: reviewData.notes,
        });

        if (quoteResult.error || !quoteResult.data) {
          steps[steps.length - 1] = this.updateStep(steps[steps.length - 1], 'failed', 'Failed to create quote');
          return this.buildFailureResult(steps, 'Quote creation failed');
        }

        steps[steps.length - 1] = this.updateStep(steps[steps.length - 1], 'completed', `Quote created: $${finalPrice.toFixed(2)}`);

        // Send email notification
        steps.push(this.createStep('email_notification', 'in_progress', 'Sending quote email...'));
        
        const emailResult = await quoteEmailService.sendQuoteEmail(quoteResult.data);
        
        if (emailResult.success) {
          steps[steps.length - 1] = this.updateStep(steps[steps.length - 1], 'completed', 'Quote email sent');
        } else {
          steps[steps.length - 1] = this.updateStep(steps[steps.length - 1], 'failed', 'Email notification failed');
        }

        return {
          success: true,
          booking,
          quote: quoteResult.data,
          steps,
          requiresManualReview: false,
          reviewReasons: [],
        };

      } else {
        // Reject booking
        steps.push(this.createStep('booking_rejection', 'in_progress', 'Processing booking rejection...'));

        const updateResult = await bookingService.updateBooking(booking.id, {
          status: 'cancelled',
          notes: `${booking.notes || ''}\n\n❌ Booking rejected during manual review: ${reviewData.notes || 'No reason provided'} - ${new Date().toISOString()}`,
        });

        if (updateResult.error) {
          steps[steps.length - 1] = this.updateStep(steps[steps.length - 1], 'failed', 'Failed to update booking status');
          return this.buildFailureResult(steps, 'Failed to reject booking');
        }

        steps[steps.length - 1] = this.updateStep(steps[steps.length - 1], 'completed', 'Booking rejected');

        return {
          success: true,
          booking: updateResult.data || booking,
          steps,
          requiresManualReview: false,
          reviewReasons: [],
        };
      }

    } catch (error) {
      console.error('❌ Manual review workflow error:', error);
      return this.buildFailureResult(steps, error instanceof Error ? error.message : 'Manual review failed');
    }
  }

  /**
   * Get workflow status for a booking
   */
  async getBookingWorkflowStatus(bookingId: string): Promise<{
    booking?: Booking;
    hasQuote: boolean;
    quoteStatus?: string;
    requiresReview: boolean;
    workflowStage: 'new' | 'pending_quote' | 'quoted' | 'confirmed' | 'completed' | 'cancelled';
  }> {
    try {
      const bookingResult = await bookingService.getBookingById(bookingId);
      if (bookingResult.error || !bookingResult.data) {
        throw new Error('Booking not found');
      }

      const booking = bookingResult.data;

      // Check for existing quotes
      const quotesResult = await quoteService.getQuotes({
        filters: { booking_id: bookingId },
        limit: 1,
      });

      const hasQuote = quotesResult.data && quotesResult.data.length > 0;
      const quote = hasQuote ? quotesResult.data![0] : undefined;

      // Determine workflow stage
      let workflowStage: 'new' | 'pending_quote' | 'quoted' | 'confirmed' | 'completed' | 'cancelled' = 'new';
      
      if (booking.status === 'cancelled') {
        workflowStage = 'cancelled';
      } else if (booking.status === 'completed') {
        workflowStage = 'completed';
      } else if (booking.status === 'confirmed') {
        workflowStage = 'confirmed';
      } else if (hasQuote) {
        workflowStage = 'quoted';
      } else if (booking.status === 'pending') {
        workflowStage = 'pending_quote';
      }

      // Check if requires manual review
      const requiresReview = 
        booking.notes?.includes('requires manual review') ||
        booking.notes?.includes('special requests') ||
        !booking.total_amount ||
        booking.total_amount <= 0;

      return {
        booking,
        hasQuote,
        quoteStatus: quote?.status,
        requiresReview,
        workflowStage,
      };

    } catch (error) {
      console.error('❌ Failed to get workflow status:', error);
      throw error;
    }
  }

  /**
   * Helper methods
   */
  private createStep(step: string, status: WorkflowStep['status'], message: string): WorkflowStep {
    return {
      step,
      status,
      message,
      timestamp: new Date().toISOString(),
    };
  }

  private updateStep(step: WorkflowStep, status: WorkflowStep['status'], message: string, error?: string): WorkflowStep {
    return {
      ...step,
      status,
      message,
      timestamp: new Date().toISOString(),
      error,
    };
  }

  private buildFailureResult(steps: WorkflowStep[], error: string): WorkflowResult {
    return {
      success: false,
      steps,
      requiresManualReview: true,
      reviewReasons: [error],
      error,
    };
  }
}

// Export singleton instance
export const bookingWorkflowService = BookingWorkflowService.getInstance();
