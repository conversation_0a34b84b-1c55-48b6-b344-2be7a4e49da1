import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  TouchableOpacity,
  ActivityIndicator,
  RefreshControl,
  Alert,
  FlatList
} from 'react-native';
import { Quote } from '@/types/database';
import { quoteService } from '@/services/database/quoteService';
import QuoteDetailScreen from './QuoteDetailScreen';

const QuotesScreen: React.FC = () => {
  const [quotes, setQuotes] = useState<Quote[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedQuote, setSelectedQuote] = useState<Quote | null>(null);
  const [showQuoteDetail, setShowQuoteDetail] = useState(false);
  const isMountedRef = useRef(true);

  useEffect(() => {
    isMountedRef.current = true;
    loadQuotes();

    return () => {
      isMountedRef.current = false;
    };
  }, []);

  const loadQuotes = async (isRefresh = false) => {
    try {
      if (!isMountedRef.current) return;

      if (isRefresh) {
        setRefreshing(true);
      } else {
        setLoading(true);
      }
      setError(null);

      console.log('📋 Loading quotes...');

      const result = await quoteService.getQuotes({
        order_by: 'created_at',
        order_direction: 'desc',
        limit: 50
      });

      if (!isMountedRef.current) return;

      if (result.error) {
        throw new Error(result.error.message);
      }

      setQuotes(result.data || []);
      console.log(`✅ Loaded ${result.data?.length || 0} quotes`);
    } catch (err) {
      console.error('❌ Failed to load quotes:', err);

      if (isMountedRef.current) {
        setError('Failed to load quotes. Please try again.');
        Alert.alert('Error', 'Failed to load quotes. Please try again.');
      }
    } finally {
      if (isMountedRef.current) {
        setLoading(false);
        setRefreshing(false);
      }
    }
  };

  const handleCreateNewQuote = () => {
    Alert.alert(
      'Create New Quote',
      'Quote creation functionality will be implemented in the next phase.',
      [{ text: 'OK' }]
    );
  };

  const testQuoteSystem = async () => {
    Alert.alert(
      'Quote System Test',
      'This will test quote creation, approval, and conversion workflows. Continue?',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Run Test', onPress: performQuoteTests },
      ]
    );
  };

  const performQuoteTests = async () => {
    let testResults: string[] = [];
    let testQuoteId: string | null = null;

    try {
      // Test 1: CREATE - Create a test quote
      console.log('🧪 Testing quote creation...');
      const createResult = await quoteService.createQuote({
        customer_id: 'test-customer-id', // This will likely fail, but we can test validation
        title: 'Test Quote - System Validation',
        description: 'Test quote created for system validation',
        total_amount: 150.00,
        status: 'draft',
        notes: 'Test quote for validation purposes',
      });

      if (createResult.error) {
        testResults.push('❌ CREATE: Failed - ' + createResult.error.message);
      } else if (createResult.data) {
        testQuoteId = createResult.data.id;
        testResults.push('✅ CREATE: Success - Quote created');
      }

      // Test 2: READ - Get quotes list
      console.log('🧪 Testing quote retrieval...');
      const listResult = await quoteService.getQuotes({
        order_by: 'created_at',
        order_direction: 'desc',
        limit: 10,
      });

      if (listResult.error) {
        testResults.push('❌ READ: Failed - ' + listResult.error.message);
      } else {
        testResults.push(`✅ READ: Success - ${listResult.data?.length || 0} quotes retrieved`);
      }

      // Test 3: UPDATE STATUS - Test quote approval
      if (testQuoteId) {
        console.log('🧪 Testing quote status update...');
        const statusResult = await quoteService.updateQuoteStatus(testQuoteId, 'sent');

        if (statusResult.error) {
          testResults.push('❌ STATUS UPDATE: Failed - ' + statusResult.error.message);
        } else {
          testResults.push('✅ STATUS UPDATE: Success - Quote status updated');
        }
      }

      // Test 4: DELETE - Clean up test quote
      if (testQuoteId) {
        console.log('🧪 Testing quote deletion...');
        const deleteResult = await quoteService.deleteQuote(testQuoteId);

        if (deleteResult.error) {
          testResults.push('❌ DELETE: Failed - ' + deleteResult.error.message);
        } else {
          testResults.push('✅ DELETE: Success - Test quote cleaned up');
        }
      }

    } catch (error) {
      testResults.push('❌ GENERAL: Unexpected error - ' + error);
    }

    // Show test results
    const resultsText = testResults.join('\n');
    Alert.alert(
      'Quote System Test Results 📋',
      resultsText,
      [
        {
          text: 'OK',
          onPress: () => loadQuotes(), // Refresh the quotes list
        },
      ]
    );

    console.log('🧪 Quote System Tests completed:', testResults);
  };

  const handleQuotePress = (quote: Quote) => {
    setSelectedQuote(quote);
    setShowQuoteDetail(true);
  };

  const handleQuoteUpdated = () => {
    loadQuotes(); // Refresh the quotes list
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-AU', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const getStatusColor = (status: Quote['status']) => {
    switch (status) {
      case 'accepted': return '#4CAF50';
      case 'sent': return '#2196F3';
      case 'declined': return '#f44336';
      case 'expired': return '#9E9E9E';
      case 'draft':
      default: return '#FF9800';
    }
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#FF9A8B" />
          <Text style={styles.loadingText}>Loading quotes...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>📋 Quotes</Text>
        <Text style={styles.subtitle}>Service Estimates</Text>
      </View>

      <View style={styles.section}>
        <View style={styles.buttonRow}>
          <TouchableOpacity
            style={styles.testButton}
            onPress={testQuoteSystem}
          >
            <Text style={styles.testButtonText}>🧪 Test System</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.newQuoteButton}
            onPress={handleCreateNewQuote}
          >
            <Text style={styles.newQuoteText}>➕ Create New Quote</Text>
          </TouchableOpacity>
        </View>
      </View>

      {error && (
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>{error}</Text>
          <TouchableOpacity
            style={styles.retryButton}
            onPress={() => loadQuotes()}
          >
            <Text style={styles.retryButtonText}>Retry</Text>
          </TouchableOpacity>
        </View>
      )}

      {quotes.length === 0 && !loading && !error ? (
        <View style={styles.emptyContainer}>
          <Text style={styles.emptyTitle}>No Quotes Yet</Text>
          <Text style={styles.emptyMessage}>
            Create your first quote to get started with customer estimates.
          </Text>
        </View>
      ) : (
        <FlatList
          data={quotes}
          renderItem={({ item: quote }) => (
            <TouchableOpacity
              style={styles.quoteCard}
              onPress={() => handleQuotePress(quote)}
            >
              <View style={styles.quoteHeader}>
                <Text style={styles.customerName}>
                  {quote.customer?.full_name || 'Unknown Customer'}
                </Text>
                <Text style={styles.quoteAmount}>${quote.estimated_total.toFixed(2)}</Text>
              </View>
              <Text style={styles.quoteTitle}>{quote.title}</Text>
              {quote.description && (
                <Text style={styles.quoteDescription} numberOfLines={2}>
                  {quote.description}
                </Text>
              )}
              <View style={styles.quoteFooter}>
                <Text style={styles.quoteDate}>{formatDate(quote.created_at)}</Text>
                <View style={[styles.statusBadge, { backgroundColor: getStatusColor(quote.status) }]}>
                  <Text style={styles.statusText}>{quote.status.toUpperCase()}</Text>
                </View>
              </View>
            </TouchableOpacity>
          )}
          keyExtractor={(item) => item.id}
          contentContainerStyle={styles.listContainer}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={() => loadQuotes(true)}
              colors={['#FF9A8B']}
              tintColor="#FF9A8B"
            />
          }
          showsVerticalScrollIndicator={false}
        />
      )}

      <QuoteDetailScreen
        visible={showQuoteDetail}
        quote={selectedQuote}
        onClose={() => {
          setShowQuoteDetail(false);
          setSelectedQuote(null);
        }}
        onQuoteUpdated={handleQuoteUpdated}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa'
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#666',
  },
  header: {
    paddingHorizontal: 16,
    paddingTop: 16,
    marginBottom: 16
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4
  },
  subtitle: {
    fontSize: 16,
    color: '#666'
  },
  section: {
    paddingHorizontal: 16,
    marginBottom: 16
  },
  buttonRow: {
    flexDirection: 'row',
    gap: 12,
  },
  testButton: {
    backgroundColor: '#f0f8ff',
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#e6f3ff',
    flex: 1,
  },
  testButtonText: {
    color: '#0066cc',
    fontSize: 14,
    fontWeight: '600',
  },
  newQuoteButton: {
    backgroundColor: '#FF9A8B',
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
    flex: 2,
  },
  newQuoteText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600'
  },
  errorContainer: {
    margin: 16,
    padding: 16,
    backgroundColor: '#ffebee',
    borderRadius: 8,
    alignItems: 'center',
  },
  errorText: {
    fontSize: 16,
    color: '#c62828',
    marginBottom: 12,
    textAlign: 'center',
  },
  retryButton: {
    backgroundColor: '#FF9A8B',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 6,
  },
  retryButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
  },
  emptyMessage: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    lineHeight: 22,
  },
  listContainer: {
    paddingHorizontal: 16,
    paddingBottom: 16,
  },
  quoteCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderLeftWidth: 4,
    borderLeftColor: '#FF9A8B',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  quoteHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8
  },
  customerName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    flex: 1,
    marginRight: 8,
  },
  quoteAmount: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FF9A8B'
  },
  quoteTitle: {
    fontSize: 15,
    fontWeight: '500',
    color: '#333',
    marginBottom: 4
  },
  quoteDescription: {
    fontSize: 14,
    color: '#666',
    marginBottom: 12,
    lineHeight: 18,
  },
  quoteFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center'
  },
  quoteDate: {
    fontSize: 12,
    color: '#999'
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12
  },
  statusText: {
    fontSize: 12,
    color: '#fff',
    fontWeight: '600'
  },
});

export default QuotesScreen;
