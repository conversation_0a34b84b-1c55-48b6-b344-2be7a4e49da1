/**
 * Ocean Soul Sparkles Mobile App - CRUD Operations Validator
 * Validates Create, Read, Update, Delete operations work seamlessly with admin portal
 */

import { bookingService } from '@/services/database/bookingService';
import { customerService } from '@/services/database/customerService';
import { serviceService } from '@/services/database/serviceService';
import { quoteService } from '@/services/database/quoteService';
import { staffService } from '@/services/database/staffService';
import { Booking, Customer, Service, Quote, AdminUser } from '@/types/database';

export interface CRUDValidationResult {
  test: string;
  entity: 'booking' | 'customer' | 'service' | 'quote' | 'staff';
  operation: 'create' | 'read' | 'update' | 'delete' | 'list';
  status: 'pass' | 'fail' | 'warning';
  message: string;
  duration: number;
  details?: any;
  error?: string;
}

export interface CRUDOperationSummary {
  entity: string;
  create: boolean;
  read: boolean;
  update: boolean;
  delete: boolean;
  list: boolean;
  overall: boolean;
}

export class CRUDValidator {
  private static instance: CRUDValidator;
  private testEntities: { id: string; type: string }[] = [];

  private constructor() {}

  public static getInstance(): CRUDValidator {
    if (!CRUDValidator.instance) {
      CRUDValidator.instance = new CRUDValidator();
    }
    return CRUDValidator.instance;
  }

  /**
   * Validate booking CRUD operations
   */
  async validateBookingCRUD(): Promise<CRUDValidationResult[]> {
    const results: CRUDValidationResult[] = [];
    let testBookingId: string | null = null;

    try {
      console.log('📅 Validating booking CRUD operations...');

      // Test CREATE
      const createStartTime = Date.now();
      const testBookingData: Partial<Booking> = {
        customer_id: 'crud-test-customer-' + Date.now(),
        service_id: 'crud-test-service-' + Date.now(),
        booking_date: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        start_time: '14:00',
        end_time: '15:00',
        status: 'pending',
        notes: `CRUD validation test booking - ${new Date().toISOString()}`,
      };

      const createResult = await bookingService.createBooking(testBookingData);
      
      results.push({
        test: 'Booking CREATE',
        entity: 'booking',
        operation: 'create',
        status: createResult.error ? 'fail' : 'pass',
        message: createResult.error 
          ? `Create failed: ${createResult.error.message}`
          : 'Booking created successfully',
        duration: Date.now() - createStartTime,
        details: {
          bookingId: createResult.data?.id,
          testData: testBookingData,
        },
        error: createResult.error?.message,
      });

      if (createResult.data) {
        testBookingId = createResult.data.id;
        this.testEntities.push({ id: testBookingId, type: 'booking' });
      }

      // Test READ (if create succeeded)
      if (testBookingId) {
        const readStartTime = Date.now();
        const readResult = await bookingService.getBookingById(testBookingId);
        
        results.push({
          test: 'Booking READ',
          entity: 'booking',
          operation: 'read',
          status: readResult.error ? 'fail' : 'pass',
          message: readResult.error 
            ? `Read failed: ${readResult.error.message}`
            : 'Booking read successfully',
          duration: Date.now() - readStartTime,
          details: {
            bookingId: testBookingId,
            dataMatches: readResult.data?.notes === testBookingData.notes,
          },
          error: readResult.error?.message,
        });
      }

      // Test LIST
      const listStartTime = Date.now();
      const listResult = await bookingService.getBookings({ limit: 5 });
      
      results.push({
        test: 'Booking LIST',
        entity: 'booking',
        operation: 'list',
        status: listResult.error ? 'fail' : 'pass',
        message: listResult.error 
          ? `List failed: ${listResult.error.message}`
          : `Listed ${listResult.data?.length || 0} bookings`,
        duration: Date.now() - listStartTime,
        details: {
          recordCount: listResult.data?.length || 0,
          includesTestRecord: testBookingId ? 
            listResult.data?.some(b => b.id === testBookingId) : false,
        },
        error: listResult.error?.message,
      });

      // Test UPDATE (if create succeeded)
      if (testBookingId) {
        const updateStartTime = Date.now();
        const updateResult = await bookingService.updateBooking(testBookingId, {
          status: 'confirmed',
          notes: `${testBookingData.notes}\nUpdated during CRUD validation`,
        });
        
        results.push({
          test: 'Booking UPDATE',
          entity: 'booking',
          operation: 'update',
          status: updateResult.error ? 'fail' : 'pass',
          message: updateResult.error 
            ? `Update failed: ${updateResult.error.message}`
            : 'Booking updated successfully',
          duration: Date.now() - updateStartTime,
          details: {
            bookingId: testBookingId,
            statusChanged: updateResult.data?.status === 'confirmed',
          },
          error: updateResult.error?.message,
        });
      }

      // Test DELETE (cleanup)
      if (testBookingId) {
        const deleteStartTime = Date.now();
        const deleteResult = await bookingService.deleteBooking(testBookingId);
        
        results.push({
          test: 'Booking DELETE',
          entity: 'booking',
          operation: 'delete',
          status: deleteResult.error ? 'fail' : 'pass',
          message: deleteResult.error 
            ? `Delete failed: ${deleteResult.error.message}`
            : 'Booking deleted successfully',
          duration: Date.now() - deleteStartTime,
          details: {
            bookingId: testBookingId,
          },
          error: deleteResult.error?.message,
        });

        if (!deleteResult.error) {
          this.testEntities = this.testEntities.filter(e => e.id !== testBookingId);
        }
      }

    } catch (error) {
      results.push({
        test: 'Booking CRUD Exception',
        entity: 'booking',
        operation: 'create',
        status: 'fail',
        message: 'CRUD validation threw exception',
        duration: 0,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }

    return results;
  }

  /**
   * Validate customer CRUD operations
   */
  async validateCustomerCRUD(): Promise<CRUDValidationResult[]> {
    const results: CRUDValidationResult[] = [];
    let testCustomerId: string | null = null;

    try {
      console.log('👤 Validating customer CRUD operations...');

      // Test CREATE
      const createStartTime = Date.now();
      const testCustomerData: Partial<Customer> = {
        email: `crud-test-${Date.now()}@oceansoulsparkles.com.au`,
        full_name: 'CRUD Test Customer',
        phone: '+61 400 000 000',
        address: '123 CRUD Test Street, Sydney, NSW 2000',
        notes: `CRUD validation test customer - ${new Date().toISOString()}`,
      };

      const createResult = await customerService.createCustomer(testCustomerData);
      
      results.push({
        test: 'Customer CREATE',
        entity: 'customer',
        operation: 'create',
        status: createResult.error ? 'fail' : 'pass',
        message: createResult.error 
          ? `Create failed: ${createResult.error.message}`
          : 'Customer created successfully',
        duration: Date.now() - createStartTime,
        details: {
          customerId: createResult.data?.id,
          email: testCustomerData.email,
        },
        error: createResult.error?.message,
      });

      if (createResult.data) {
        testCustomerId = createResult.data.id;
        this.testEntities.push({ id: testCustomerId, type: 'customer' });
      }

      // Test READ
      if (testCustomerId) {
        const readStartTime = Date.now();
        const readResult = await customerService.getCustomerById(testCustomerId);
        
        results.push({
          test: 'Customer READ',
          entity: 'customer',
          operation: 'read',
          status: readResult.error ? 'fail' : 'pass',
          message: readResult.error 
            ? `Read failed: ${readResult.error.message}`
            : 'Customer read successfully',
          duration: Date.now() - readStartTime,
          details: {
            customerId: testCustomerId,
            emailMatches: readResult.data?.email === testCustomerData.email,
          },
          error: readResult.error?.message,
        });
      }

      // Test LIST
      const listStartTime = Date.now();
      const listResult = await customerService.getCustomers({ limit: 5 });
      
      results.push({
        test: 'Customer LIST',
        entity: 'customer',
        operation: 'list',
        status: listResult.error ? 'fail' : 'pass',
        message: listResult.error 
          ? `List failed: ${listResult.error.message}`
          : `Listed ${listResult.data?.length || 0} customers`,
        duration: Date.now() - listStartTime,
        details: {
          recordCount: listResult.data?.length || 0,
          includesTestRecord: testCustomerId ? 
            listResult.data?.some(c => c.id === testCustomerId) : false,
        },
        error: listResult.error?.message,
      });

      // Test UPDATE
      if (testCustomerId) {
        const updateStartTime = Date.now();
        const updateResult = await customerService.updateCustomer(testCustomerId, {
          phone: '+61 400 111 222',
          notes: `${testCustomerData.notes}\nUpdated during CRUD validation`,
        });
        
        results.push({
          test: 'Customer UPDATE',
          entity: 'customer',
          operation: 'update',
          status: updateResult.error ? 'fail' : 'pass',
          message: updateResult.error 
            ? `Update failed: ${updateResult.error.message}`
            : 'Customer updated successfully',
          duration: Date.now() - updateStartTime,
          details: {
            customerId: testCustomerId,
            phoneChanged: updateResult.data?.phone === '+61 400 111 222',
          },
          error: updateResult.error?.message,
        });
      }

      // Test DELETE (cleanup)
      if (testCustomerId) {
        const deleteStartTime = Date.now();
        const deleteResult = await customerService.deleteCustomer(testCustomerId);
        
        results.push({
          test: 'Customer DELETE',
          entity: 'customer',
          operation: 'delete',
          status: deleteResult.error ? 'fail' : 'pass',
          message: deleteResult.error 
            ? `Delete failed: ${deleteResult.error.message}`
            : 'Customer deleted successfully',
          duration: Date.now() - deleteStartTime,
          details: {
            customerId: testCustomerId,
          },
          error: deleteResult.error?.message,
        });

        if (!deleteResult.error) {
          this.testEntities = this.testEntities.filter(e => e.id !== testCustomerId);
        }
      }

    } catch (error) {
      results.push({
        test: 'Customer CRUD Exception',
        entity: 'customer',
        operation: 'create',
        status: 'fail',
        message: 'CRUD validation threw exception',
        duration: 0,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }

    return results;
  }

  /**
   * Validate quote CRUD operations
   */
  async validateQuoteCRUD(): Promise<CRUDValidationResult[]> {
    const results: CRUDValidationResult[] = [];
    let testQuoteId: string | null = null;

    try {
      console.log('💰 Validating quote CRUD operations...');

      // Test CREATE
      const createStartTime = Date.now();
      const testQuoteData: Partial<Quote> = {
        customer_id: 'crud-test-customer-quote-' + Date.now(),
        customer_first_name: 'Test',
        customer_last_name: 'Customer',
        customer_email: '<EMAIL>',
        customer_phone: '+61123456789',
        service_name: 'CRUD Test Quote',
        service_description: `CRUD validation test quote created at ${new Date().toISOString()}`,
        estimated_total: 200.00,
        status: 'draft',
        expires_at: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
        notes: 'CRUD validation test',
      };

      const createResult = await quoteService.createQuote(testQuoteData);
      
      results.push({
        test: 'Quote CREATE',
        entity: 'quote',
        operation: 'create',
        status: createResult.error ? 'fail' : 'pass',
        message: createResult.error 
          ? `Create failed: ${createResult.error.message}`
          : 'Quote created successfully',
        duration: Date.now() - createStartTime,
        details: {
          quoteId: createResult.data?.id,
          amount: testQuoteData.estimated_total,
        },
        error: createResult.error?.message,
      });

      if (createResult.data) {
        testQuoteId = createResult.data.id;
        this.testEntities.push({ id: testQuoteId, type: 'quote' });
      }

      // Test READ
      if (testQuoteId) {
        const readStartTime = Date.now();
        const readResult = await quoteService.getQuoteById(testQuoteId);
        
        results.push({
          test: 'Quote READ',
          entity: 'quote',
          operation: 'read',
          status: readResult.error ? 'fail' : 'pass',
          message: readResult.error 
            ? `Read failed: ${readResult.error.message}`
            : 'Quote read successfully',
          duration: Date.now() - readStartTime,
          details: {
            quoteId: testQuoteId,
            amountMatches: readResult.data?.estimated_total === testQuoteData.estimated_total,
          },
          error: readResult.error?.message,
        });
      }

      // Test LIST
      const listStartTime = Date.now();
      const listResult = await quoteService.getQuotes({ limit: 5 });
      
      results.push({
        test: 'Quote LIST',
        entity: 'quote',
        operation: 'list',
        status: listResult.error ? 'fail' : 'pass',
        message: listResult.error 
          ? `List failed: ${listResult.error.message}`
          : `Listed ${listResult.data?.length || 0} quotes`,
        duration: Date.now() - listStartTime,
        details: {
          recordCount: listResult.data?.length || 0,
          includesTestRecord: testQuoteId ? 
            listResult.data?.some(q => q.id === testQuoteId) : false,
        },
        error: listResult.error?.message,
      });

      // Test UPDATE
      if (testQuoteId) {
        const updateStartTime = Date.now();
        const updateResult = await quoteService.updateQuote(testQuoteId, {
          status: 'sent',
          estimated_total: 250.00,
          notes: `${testQuoteData.notes}\nUpdated during CRUD validation`,
        });
        
        results.push({
          test: 'Quote UPDATE',
          entity: 'quote',
          operation: 'update',
          status: updateResult.error ? 'fail' : 'pass',
          message: updateResult.error 
            ? `Update failed: ${updateResult.error.message}`
            : 'Quote updated successfully',
          duration: Date.now() - updateStartTime,
          details: {
            quoteId: testQuoteId,
            statusChanged: updateResult.data?.status === 'sent',
            amountChanged: updateResult.data?.estimated_total === 250.00,
          },
          error: updateResult.error?.message,
        });
      }

      // Test DELETE (cleanup)
      if (testQuoteId) {
        const deleteStartTime = Date.now();
        const deleteResult = await quoteService.deleteQuote(testQuoteId);
        
        results.push({
          test: 'Quote DELETE',
          entity: 'quote',
          operation: 'delete',
          status: deleteResult.error ? 'fail' : 'pass',
          message: deleteResult.error 
            ? `Delete failed: ${deleteResult.error.message}`
            : 'Quote deleted successfully',
          duration: Date.now() - deleteStartTime,
          details: {
            quoteId: testQuoteId,
          },
          error: deleteResult.error?.message,
        });

        if (!deleteResult.error) {
          this.testEntities = this.testEntities.filter(e => e.id !== testQuoteId);
        }
      }

    } catch (error) {
      results.push({
        test: 'Quote CRUD Exception',
        entity: 'quote',
        operation: 'create',
        status: 'fail',
        message: 'CRUD validation threw exception',
        duration: 0,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }

    return results;
  }

  /**
   * Run comprehensive CRUD validation
   */
  async runComprehensiveCRUDValidation(): Promise<CRUDValidationResult[]> {
    console.log('🧪 Running comprehensive CRUD operations validation...');

    this.testEntities = []; // Reset test entities

    const [bookingResults, customerResults, quoteResults] = await Promise.all([
      this.validateBookingCRUD(),
      this.validateCustomerCRUD(),
      this.validateQuoteCRUD(),
    ]);

    const allResults = [...bookingResults, ...customerResults, ...quoteResults];
    const passedTests = allResults.filter(r => r.status === 'pass').length;
    const totalTests = allResults.length;

    console.log(`✅ CRUD validation completed: ${passedTests}/${totalTests} operations passed`);

    return allResults;
  }

  /**
   * Get CRUD operations summary by entity
   */
  getCRUDSummary(results: CRUDValidationResult[]): CRUDOperationSummary[] {
    const entities = ['booking', 'customer', 'quote'];
    
    return entities.map(entity => {
      const entityResults = results.filter(r => r.entity === entity);
      
      return {
        entity,
        create: entityResults.some(r => r.operation === 'create' && r.status === 'pass'),
        read: entityResults.some(r => r.operation === 'read' && r.status === 'pass'),
        update: entityResults.some(r => r.operation === 'update' && r.status === 'pass'),
        delete: entityResults.some(r => r.operation === 'delete' && r.status === 'pass'),
        list: entityResults.some(r => r.operation === 'list' && r.status === 'pass'),
        overall: entityResults.filter(r => r.status === 'pass').length >= 4, // At least 4/5 operations working
      };
    });
  }

  /**
   * Cleanup any remaining test entities
   */
  async cleanupTestEntities(): Promise<void> {
    console.log(`🧹 Cleaning up ${this.testEntities.length} test entities...`);

    for (const entity of this.testEntities) {
      try {
        switch (entity.type) {
          case 'booking':
            await bookingService.deleteBooking(entity.id);
            break;
          case 'customer':
            await customerService.deleteCustomer(entity.id);
            break;
          case 'quote':
            await quoteService.deleteQuote(entity.id);
            break;
        }
      } catch (error) {
        console.warn(`⚠️ Failed to cleanup ${entity.type} ${entity.id}:`, error);
      }
    }

    this.testEntities = [];
    console.log('✅ Test entity cleanup completed');
  }
}

// Export singleton instance
export const crudValidator = CRUDValidator.getInstance();
