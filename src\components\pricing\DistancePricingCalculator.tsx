/**
 * Ocean Soul Sparkles Mobile App - Distance Pricing Calculator Component
 * Interactive UI for calculating distance-based pricing with location input
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  Alert,
  ActivityIndicator,
  ScrollView,
} from 'react-native';
import { distancePricingService, PricingCalculationResult, LocationData } from '@/services/pricing/distancePricingService';
import { locationCaptureService, GeocodeResult } from '@/services/location/locationCaptureService';
import { Service } from '@/types/database';

interface DistancePricingCalculatorProps {
  service: Service;
  initialLocation?: LocationData;
  onPricingCalculated?: (result: PricingCalculationResult) => void;
  onLocationChanged?: (location: LocationData) => void;
  showLocationInput?: boolean;
  showPricingBreakdown?: boolean;
}

const DistancePricingCalculator: React.FC<DistancePricingCalculatorProps> = ({
  service,
  initialLocation,
  onPricingCalculated,
  onLocationChanged,
  showLocationInput = true,
  showPricingBreakdown = true,
}) => {
  const [location, setLocation] = useState<LocationData | null>(initialLocation || null);
  const [pricingResult, setPricingResult] = useState<PricingCalculationResult | null>(null);
  const [loading, setLoading] = useState(false);
  const [locationLoading, setLocationLoading] = useState(false);
  const [addressInput, setAddressInput] = useState('');
  const [showManualInput, setShowManualInput] = useState(false);

  useEffect(() => {
    if (initialLocation) {
      setLocation(initialLocation);
      calculatePricing(initialLocation);
    }
  }, [initialLocation]);

  useEffect(() => {
    if (location && onLocationChanged) {
      onLocationChanged(location);
    }
  }, [location, onLocationChanged]);

  /**
   * Calculate pricing based on current location
   */
  const calculatePricing = async (locationData: LocationData = location) => {
    if (!locationData || !service) return;

    try {
      setLoading(true);
      console.log('💰 Calculating distance-based pricing...');

      const result = await distancePricingService.calculatePricing(service, locationData);
      setPricingResult(result);

      if (onPricingCalculated) {
        onPricingCalculated(result);
      }

      if (!result.success) {
        Alert.alert('Pricing Error', result.error || 'Failed to calculate pricing');
      }

    } catch (error) {
      console.error('❌ Pricing calculation error:', error);
      Alert.alert('Error', 'Failed to calculate pricing. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  /**
   * Get current device location
   */
  const getCurrentLocation = async () => {
    try {
      setLocationLoading(true);
      console.log('📍 Getting current location...');

      const result = await locationCaptureService.getCurrentLocation();
      
      if (result.success && result.location) {
        setLocation(result.location);
        setAddressInput(result.location.address);
        await calculatePricing(result.location);
        Alert.alert('Location Found', 'Using your current location for pricing calculation.');
      } else {
        Alert.alert(
          'Location Error', 
          result.error || 'Could not get your current location. Please enter address manually.',
          [
            { text: 'OK' },
            { text: 'Enter Manually', onPress: () => setShowManualInput(true) },
          ]
        );
      }

    } catch (error) {
      console.error('❌ Get location error:', error);
      Alert.alert('Error', 'Failed to get location. Please enter address manually.');
      setShowManualInput(true);
    } finally {
      setLocationLoading(false);
    }
  };

  /**
   * Geocode manually entered address
   */
  const geocodeManualAddress = async () => {
    if (!addressInput.trim()) {
      Alert.alert('Error', 'Please enter an address');
      return;
    }

    try {
      setLocationLoading(true);
      console.log('🗺️ Geocoding address:', addressInput);

      const result = await locationCaptureService.geocodeAddress(addressInput);
      
      if (result.success && result.location) {
        setLocation(result.location);
        await calculatePricing(result.location);
        setShowManualInput(false);
        Alert.alert('Address Found', 'Address geocoded successfully for pricing calculation.');
      } else {
        Alert.alert('Address Error', result.error || 'Could not find the address. Please check and try again.');
      }

    } catch (error) {
      console.error('❌ Geocoding error:', error);
      Alert.alert('Error', 'Failed to find address. Please check and try again.');
    } finally {
      setLocationLoading(false);
    }
  };

  /**
   * Clear location and reset pricing
   */
  const clearLocation = () => {
    setLocation(null);
    setPricingResult(null);
    setAddressInput('');
    setShowManualInput(false);
  };

  /**
   * Get pricing tier color
   */
  const getPricingTierColor = () => {
    if (!pricingResult) return '#6b7280';
    
    switch (pricingResult.pricing_tier.name) {
      case 'Local': return '#10b981';
      case 'Metro': return '#3b82f6';
      case 'Regional': return '#f59e0b';
      case 'Extended': return '#ef4444';
      default: return '#6b7280';
    }
  };

  return (
    <View style={styles.container}>
      {/* Service Info */}
      <View style={styles.serviceInfo}>
        <Text style={styles.serviceName}>{service.name}</Text>
        <Text style={styles.basePrice}>Base Price: ${service.base_price?.toFixed(2) || '0.00'}</Text>
      </View>

      {/* Location Input Section */}
      {showLocationInput && (
        <View style={styles.locationSection}>
          <Text style={styles.sectionTitle}>Customer Location</Text>
          
          {!location && !showManualInput && (
            <View style={styles.locationButtons}>
              <TouchableOpacity
                style={[styles.button, styles.primaryButton]}
                onPress={getCurrentLocation}
                disabled={locationLoading}
              >
                {locationLoading ? (
                  <ActivityIndicator size="small" color="#fff" />
                ) : (
                  <Text style={styles.buttonText}>📍 Use Current Location</Text>
                )}
              </TouchableOpacity>
              
              <TouchableOpacity
                style={[styles.button, styles.secondaryButton]}
                onPress={() => setShowManualInput(true)}
              >
                <Text style={styles.secondaryButtonText}>✏️ Enter Address</Text>
              </TouchableOpacity>
            </View>
          )}

          {showManualInput && (
            <View style={styles.manualInput}>
              <TextInput
                style={styles.addressInput}
                placeholder="Enter customer address..."
                value={addressInput}
                onChangeText={setAddressInput}
                multiline
                numberOfLines={2}
              />
              
              <View style={styles.inputButtons}>
                <TouchableOpacity
                  style={[styles.button, styles.primaryButton]}
                  onPress={geocodeManualAddress}
                  disabled={locationLoading}
                >
                  {locationLoading ? (
                    <ActivityIndicator size="small" color="#fff" />
                  ) : (
                    <Text style={styles.buttonText}>Calculate</Text>
                  )}
                </TouchableOpacity>
                
                <TouchableOpacity
                  style={[styles.button, styles.secondaryButton]}
                  onPress={() => setShowManualInput(false)}
                >
                  <Text style={styles.secondaryButtonText}>Cancel</Text>
                </TouchableOpacity>
              </View>
            </View>
          )}

          {location && (
            <View style={styles.locationDisplay}>
              <Text style={styles.locationText}>📍 {location.address}</Text>
              <TouchableOpacity onPress={clearLocation} style={styles.clearButton}>
                <Text style={styles.clearButtonText}>✕ Change Location</Text>
              </TouchableOpacity>
            </View>
          )}
        </View>
      )}

      {/* Pricing Result Section */}
      {pricingResult && (
        <View style={styles.pricingSection}>
          <Text style={styles.sectionTitle}>Pricing Calculation</Text>
          
          {loading ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color="#3b82f6" />
              <Text style={styles.loadingText}>Calculating pricing...</Text>
            </View>
          ) : pricingResult.success ? (
            <>
              {/* Total Price Display */}
              <View style={[styles.totalPriceContainer, { borderColor: getPricingTierColor() }]}>
                <Text style={styles.totalPriceLabel}>Total Price</Text>
                <Text style={[styles.totalPrice, { color: getPricingTierColor() }]}>
                  ${pricingResult.total_price.toFixed(2)}
                </Text>
                <Text style={styles.pricingTier}>
                  {pricingResult.pricing_tier.name} Zone ({pricingResult.distance_km}km)
                </Text>
              </View>

              {/* Pricing Breakdown */}
              {showPricingBreakdown && (
                <View style={styles.breakdownContainer}>
                  <Text style={styles.breakdownTitle}>Price Breakdown</Text>
                  
                  <View style={styles.breakdownItem}>
                    <Text style={styles.breakdownLabel}>Service Base</Text>
                    <Text style={styles.breakdownValue}>
                      ${pricingResult.breakdown.service_base.toFixed(2)}
                    </Text>
                  </View>

                  {pricingResult.breakdown.distance_adjustment > 0 && (
                    <View style={styles.breakdownItem}>
                      <Text style={styles.breakdownLabel}>
                        Distance Adjustment ({pricingResult.pricing_tier.name})
                      </Text>
                      <Text style={styles.breakdownValue}>
                        +${pricingResult.breakdown.distance_adjustment.toFixed(2)}
                      </Text>
                    </View>
                  )}

                  {pricingResult.breakdown.travel_fee > 0 && (
                    <View style={styles.breakdownItem}>
                      <Text style={styles.breakdownLabel}>Travel Fee</Text>
                      <Text style={styles.breakdownValue}>
                        +${pricingResult.breakdown.travel_fee.toFixed(2)}
                      </Text>
                    </View>
                  )}

                  <View style={[styles.breakdownItem, styles.breakdownTotal]}>
                    <Text style={styles.breakdownTotalLabel}>Total</Text>
                    <Text style={styles.breakdownTotalValue}>
                      ${pricingResult.breakdown.total.toFixed(2)}
                    </Text>
                  </View>
                </View>
              )}

              {/* Pricing Tier Info */}
              <View style={styles.tierInfo}>
                <Text style={styles.tierDescription}>
                  {pricingResult.pricing_tier.description}
                </Text>
              </View>
            </>
          ) : (
            <View style={styles.errorContainer}>
              <Text style={styles.errorText}>
                ❌ {pricingResult.error || 'Pricing calculation failed'}
              </Text>
              <TouchableOpacity
                style={[styles.button, styles.primaryButton]}
                onPress={() => calculatePricing()}
              >
                <Text style={styles.buttonText}>Retry</Text>
              </TouchableOpacity>
            </View>
          )}
        </View>
      )}

      {/* Recalculate Button */}
      {location && pricingResult && (
        <TouchableOpacity
          style={[styles.button, styles.primaryButton, styles.recalculateButton]}
          onPress={() => calculatePricing()}
          disabled={loading}
        >
          {loading ? (
            <ActivityIndicator size="small" color="#fff" />
          ) : (
            <Text style={styles.buttonText}>🔄 Recalculate Pricing</Text>
          )}
        </TouchableOpacity>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 16,
    margin: 8,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  serviceInfo: {
    marginBottom: 16,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  serviceName: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginBottom: 4,
  },
  basePrice: {
    fontSize: 14,
    color: '#666',
  },
  locationSection: {
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 12,
  },
  locationButtons: {
    gap: 8,
  },
  button: {
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 6,
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'center',
  },
  primaryButton: {
    backgroundColor: '#3b82f6',
  },
  secondaryButton: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: '#d1d5db',
  },
  buttonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
  },
  secondaryButtonText: {
    color: '#374151',
    fontSize: 14,
    fontWeight: '600',
  },
  manualInput: {
    gap: 12,
  },
  addressInput: {
    borderWidth: 1,
    borderColor: '#d1d5db',
    borderRadius: 6,
    padding: 12,
    fontSize: 14,
    minHeight: 60,
    textAlignVertical: 'top',
  },
  inputButtons: {
    flexDirection: 'row',
    gap: 8,
  },
  locationDisplay: {
    backgroundColor: '#f3f4f6',
    borderRadius: 6,
    padding: 12,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  locationText: {
    fontSize: 14,
    color: '#374151',
    flex: 1,
  },
  clearButton: {
    padding: 4,
  },
  clearButtonText: {
    fontSize: 12,
    color: '#ef4444',
    fontWeight: '500',
  },
  pricingSection: {
    marginBottom: 16,
  },
  loadingContainer: {
    alignItems: 'center',
    padding: 20,
  },
  loadingText: {
    marginTop: 8,
    fontSize: 14,
    color: '#666',
  },
  totalPriceContainer: {
    borderWidth: 2,
    borderRadius: 8,
    padding: 16,
    alignItems: 'center',
    marginBottom: 16,
  },
  totalPriceLabel: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  totalPrice: {
    fontSize: 28,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  pricingTier: {
    fontSize: 12,
    color: '#666',
  },
  breakdownContainer: {
    backgroundColor: '#f9fafb',
    borderRadius: 6,
    padding: 12,
    marginBottom: 12,
  },
  breakdownTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
  },
  breakdownItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 4,
  },
  breakdownLabel: {
    fontSize: 13,
    color: '#666',
    flex: 1,
  },
  breakdownValue: {
    fontSize: 13,
    color: '#333',
    fontWeight: '500',
  },
  breakdownTotal: {
    borderTopWidth: 1,
    borderTopColor: '#e5e7eb',
    marginTop: 8,
    paddingTop: 8,
  },
  breakdownTotalLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
    flex: 1,
  },
  breakdownTotalValue: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333',
  },
  tierInfo: {
    backgroundColor: '#eff6ff',
    borderRadius: 6,
    padding: 12,
    marginBottom: 12,
  },
  tierDescription: {
    fontSize: 12,
    color: '#1e40af',
    textAlign: 'center',
  },
  errorContainer: {
    alignItems: 'center',
    padding: 16,
  },
  errorText: {
    fontSize: 14,
    color: '#ef4444',
    textAlign: 'center',
    marginBottom: 12,
  },
  recalculateButton: {
    marginTop: 8,
  },
});

export default DistancePricingCalculator;
