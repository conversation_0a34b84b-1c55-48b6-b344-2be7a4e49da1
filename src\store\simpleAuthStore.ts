/**
 * Ocean Soul Sparkles Mobile App - Simple Authentication Store
 * Simplified Zustand store for testing
 */

import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';

interface SimpleAuthState {
  // State
  isLoading: boolean;
  isAuthenticated: boolean;
  
  // Actions
  setLoading: (loading: boolean) => void;
  initialize: () => Promise<void>;
}

export const useAuth = create<SimpleAuthState>()(
  persist(
    (set, get) => ({
      // Initial state
      isLoading: false,
      isAuthenticated: false,

      // Setters
      setLoading: (isLoading) => set({ isLoading }),

      // Initialize auth state
      initialize: async () => {
        set({ isLoading: true });
        
        try {
          // Simulate initialization
          await new Promise(resolve => setTimeout(resolve, 1000));
          
          set({ 
            isLoading: false,
            isAuthenticated: false, // For now, always false
          });
        } catch (error) {
          console.error('Auth initialization error:', error);
          set({ 
            isLoading: false,
            isAuthenticated: false,
          });
        }
      },
    }),
    {
      name: 'ocean-soul-auth',
      storage: createJSONStorage(() => AsyncStorage),
      // Only persist non-sensitive data
      partialize: (state) => ({
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
);

// Auth helper functions
export const useSimpleAuth = () => {
  const store = useAuth();
  return {
    isLoading: store.isLoading,
    isAuthenticated: store.isAuthenticated,
    initialize: store.initialize,
  };
};

export default useAuth;
