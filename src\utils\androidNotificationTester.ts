/**
 * Ocean Soul Sparkles Mobile App - Android Notification Tester
 * Tests Android notification channels and push notification functionality
 */

import * as Notifications from 'expo-notifications';
import { Platform } from 'react-native';
import { androidNotificationChannelManager, NOTIFICATION_CHANNELS } from './androidNotificationChannels';

interface NotificationTestResult {
  testName: string;
  success: boolean;
  message: string;
  details?: any;
}

class AndroidNotificationTester {
  
  /**
   * Run comprehensive Android notification tests
   */
  async runAllTests(): Promise<NotificationTestResult[]> {
    const results: NotificationTestResult[] = [];

    if (Platform.OS !== 'android') {
      results.push({
        testName: 'Platform Check',
        success: false,
        message: 'Tests can only run on Android platform',
      });
      return results;
    }

    // Test 1: Channel initialization
    results.push(await this.testChannelInitialization());

    // Test 2: Channel creation verification
    results.push(await this.testChannelCreation());

    // Test 3: Local notification with channels
    results.push(await this.testLocalNotificationWithChannel());

    // Test 4: Channel mapping
    results.push(await this.testChannelMapping());

    // Test 5: Permission status
    results.push(await this.testNotificationPermissions());

    return results;
  }

  /**
   * Test channel initialization
   */
  private async testChannelInitialization(): Promise<NotificationTestResult> {
    try {
      await androidNotificationChannelManager.initializeChannels();
      
      const isInitialized = androidNotificationChannelManager.isChannelsInitialized();
      const createdChannels = androidNotificationChannelManager.getCreatedChannels();
      
      return {
        testName: 'Channel Initialization',
        success: isInitialized && createdChannels.length > 0,
        message: isInitialized 
          ? `Successfully initialized ${createdChannels.length} channels`
          : 'Failed to initialize channels',
        details: { createdChannels },
      };
    } catch (error) {
      return {
        testName: 'Channel Initialization',
        success: false,
        message: `Initialization failed: ${error}`,
      };
    }
  }

  /**
   * Test channel creation verification
   */
  private async testChannelCreation(): Promise<NotificationTestResult> {
    try {
      const expectedChannels = Object.keys(NOTIFICATION_CHANNELS);
      const createdChannels = androidNotificationChannelManager.getCreatedChannels();
      
      const missingChannels = expectedChannels.filter(
        channelKey => !createdChannels.includes(NOTIFICATION_CHANNELS[channelKey as keyof typeof NOTIFICATION_CHANNELS].id)
      );
      
      return {
        testName: 'Channel Creation Verification',
        success: missingChannels.length === 0,
        message: missingChannels.length === 0
          ? `All ${expectedChannels.length} channels created successfully`
          : `Missing channels: ${missingChannels.join(', ')}`,
        details: {
          expected: expectedChannels.length,
          created: createdChannels.length,
          missingChannels,
        },
      };
    } catch (error) {
      return {
        testName: 'Channel Creation Verification',
        success: false,
        message: `Verification failed: ${error}`,
      };
    }
  }

  /**
   * Test local notification with specific channel
   */
  private async testLocalNotificationWithChannel(): Promise<NotificationTestResult> {
    try {
      const channelId = NOTIFICATION_CHANNELS.NEW_BOOKINGS.id;
      
      const notificationId = await Notifications.scheduleNotificationAsync({
        content: {
          title: '🧪 Test Notification',
          body: 'Testing Android notification channel functionality',
          data: { test: true },
        },
        trigger: null, // Send immediately
        identifier: `test_notification_${Date.now()}`,
      });

      return {
        testName: 'Local Notification with Channel',
        success: !!notificationId,
        message: notificationId 
          ? `Test notification sent with ID: ${notificationId}`
          : 'Failed to send test notification',
        details: { notificationId, channelId },
      };
    } catch (error) {
      return {
        testName: 'Local Notification with Channel',
        success: false,
        message: `Test notification failed: ${error}`,
      };
    }
  }

  /**
   * Test channel mapping for different notification types
   */
  private async testChannelMapping(): Promise<NotificationTestResult> {
    try {
      const testCases = [
        { type: 'new_booking', expectedChannel: NOTIFICATION_CHANNELS.NEW_BOOKINGS.id },
        { type: 'staff_message', expectedChannel: NOTIFICATION_CHANNELS.STAFF_MESSAGES.id },
        { type: 'urgent_booking', expectedChannel: NOTIFICATION_CHANNELS.URGENT_BOOKINGS.id },
        { type: 'availability_request', expectedChannel: NOTIFICATION_CHANNELS.AVAILABILITY_REQUESTS.id },
        { type: 'unknown_type', expectedChannel: NOTIFICATION_CHANNELS.GENERAL_UPDATES.id },
      ];

      const results = testCases.map(testCase => {
        const actualChannel = androidNotificationChannelManager.getChannelForNotificationType(testCase.type);
        return {
          type: testCase.type,
          expected: testCase.expectedChannel,
          actual: actualChannel,
          correct: actualChannel === testCase.expectedChannel,
        };
      });

      const allCorrect = results.every(result => result.correct);
      const incorrectMappings = results.filter(result => !result.correct);

      return {
        testName: 'Channel Mapping',
        success: allCorrect,
        message: allCorrect
          ? `All ${testCases.length} channel mappings are correct`
          : `${incorrectMappings.length} incorrect mappings found`,
        details: { results, incorrectMappings },
      };
    } catch (error) {
      return {
        testName: 'Channel Mapping',
        success: false,
        message: `Channel mapping test failed: ${error}`,
      };
    }
  }

  /**
   * Test notification permissions
   */
  private async testNotificationPermissions(): Promise<NotificationTestResult> {
    try {
      const { status } = await Notifications.getPermissionsAsync();
      
      return {
        testName: 'Notification Permissions',
        success: status === 'granted',
        message: `Permission status: ${status}`,
        details: { status },
      };
    } catch (error) {
      return {
        testName: 'Notification Permissions',
        success: false,
        message: `Permission check failed: ${error}`,
      };
    }
  }

  /**
   * Send test notifications to all channels
   */
  async sendTestNotificationsToAllChannels(): Promise<NotificationTestResult[]> {
    const results: NotificationTestResult[] = [];

    if (Platform.OS !== 'android') {
      results.push({
        testName: 'Platform Check',
        success: false,
        message: 'Test notifications can only be sent on Android',
      });
      return results;
    }

    const channels = Object.values(NOTIFICATION_CHANNELS);
    
    for (const channel of channels) {
      try {
        const notificationId = await Notifications.scheduleNotificationAsync({
          content: {
            title: `🧪 Test: ${channel.name}`,
            body: `Testing ${channel.description}`,
            data: { 
              test: true, 
              channelId: channel.id,
              importance: channel.importance,
            },
          },
          trigger: null, // Send immediately
          identifier: `test_${channel.id}_${Date.now()}`,
        });

        results.push({
          testName: `Test Notification: ${channel.name}`,
          success: !!notificationId,
          message: notificationId 
            ? `Sent to channel ${channel.id}`
            : `Failed to send to channel ${channel.id}`,
          details: { channelId: channel.id, notificationId },
        });

        // Small delay between notifications
        await new Promise(resolve => setTimeout(resolve, 500));

      } catch (error) {
        results.push({
          testName: `Test Notification: ${channel.name}`,
          success: false,
          message: `Failed: ${error}`,
          details: { channelId: channel.id },
        });
      }
    }

    return results;
  }

  /**
   * Get channel statistics and health
   */
  getChannelHealth(): {
    isHealthy: boolean;
    stats: any;
    recommendations: string[];
  } {
    const stats = androidNotificationChannelManager.getChannelStats();
    const recommendations: string[] = [];
    
    let isHealthy = true;

    // Check if channels are initialized
    if (!androidNotificationChannelManager.isChannelsInitialized()) {
      isHealthy = false;
      recommendations.push('Initialize notification channels before sending notifications');
    }

    // Check if all expected channels are created
    const expectedChannelCount = Object.keys(NOTIFICATION_CHANNELS).length;
    if (stats.createdChannels.length < expectedChannelCount) {
      isHealthy = false;
      recommendations.push(`Missing ${expectedChannelCount - stats.createdChannels.length} notification channels`);
    }

    // Check importance distribution
    const highImportanceCount = stats.channelsByImportance[Notifications.AndroidImportance.HIGH.toString()] || 0;
    if (highImportanceCount > 3) {
      recommendations.push('Consider reducing high-importance channels to avoid notification fatigue');
    }

    if (recommendations.length === 0) {
      recommendations.push('All notification channels are properly configured');
    }

    return {
      isHealthy,
      stats,
      recommendations,
    };
  }
}

// Export singleton instance
export const androidNotificationTester = new AndroidNotificationTester();
