#!/bin/bash

# Ocean Soul Sparkles Mobile App - Production Deployment Script
# 🚀 PRODUCTION DEPLOYMENT AUTOMATION
# Generated: 2025-01-28

set -e  # Exit on any error

echo "🌊 Ocean Soul Sparkles Mobile App - Production Deployment"
echo "=========================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
APP_NAME="Ocean Soul Sparkles"
VERSION="1.0.0"
BUILD_NUMBER="1"
RELEASE_CHANNEL="production"

echo -e "${BLUE}📋 Deployment Configuration:${NC}"
echo "  App Name: $APP_NAME"
echo "  Version: $VERSION"
echo "  Build Number: $BUILD_NUMBER"
echo "  Release Channel: $RELEASE_CHANNEL"
echo ""

# Step 1: Environment Setup
echo -e "${YELLOW}🔧 Step 1: Setting up production environment...${NC}"

# Check if .env.production exists
if [ ! -f ".env.production" ]; then
    echo -e "${RED}❌ Error: .env.production file not found!${NC}"
    echo "Please create .env.production with production environment variables."
    exit 1
fi

# Copy production environment
cp .env.production .env
echo -e "${GREEN}✅ Production environment configured${NC}"

# Step 2: Dependencies and Validation
echo -e "${YELLOW}🔧 Step 2: Installing dependencies and validating...${NC}"

# Install dependencies
npm install
echo -e "${GREEN}✅ Dependencies installed${NC}"

# Run TypeScript check
npx tsc --noEmit
echo -e "${GREEN}✅ TypeScript validation passed${NC}"

# Run linting
npm run lint --silent || echo -e "${YELLOW}⚠️ Linting warnings detected${NC}"

# Step 3: Pre-build Validation
echo -e "${YELLOW}🧪 Step 3: Running pre-deployment validation...${NC}"

# Run deployment readiness check (if available)
if [ -f "scripts/validate-deployment.js" ]; then
    node scripts/validate-deployment.js
    echo -e "${GREEN}✅ Deployment validation passed${NC}"
else
    echo -e "${YELLOW}⚠️ Deployment validation script not found, skipping...${NC}"
fi

# Step 4: Build for Production
echo -e "${YELLOW}🏗️ Step 4: Building for production...${NC}"

# Clear Expo cache
npx expo r -c
echo -e "${GREEN}✅ Expo cache cleared${NC}"

# Build for iOS
echo -e "${BLUE}📱 Building for iOS...${NC}"
npx expo build:ios \
    --release-channel $RELEASE_CHANNEL \
    --no-publish \
    --clear-cache \
    --type archive

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ iOS build completed successfully${NC}"
else
    echo -e "${RED}❌ iOS build failed${NC}"
    exit 1
fi

# Build for Android
echo -e "${BLUE}🤖 Building for Android...${NC}"
npx expo build:android \
    --release-channel $RELEASE_CHANNEL \
    --no-publish \
    --clear-cache \
    --type app-bundle

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Android build completed successfully${NC}"
else
    echo -e "${RED}❌ Android build failed${NC}"
    exit 1
fi

# Step 5: Post-build Validation
echo -e "${YELLOW}🔍 Step 5: Post-build validation...${NC}"

# Check build artifacts
echo "Checking build artifacts..."
if npx expo build:status | grep -q "finished"; then
    echo -e "${GREEN}✅ Build artifacts validated${NC}"
else
    echo -e "${YELLOW}⚠️ Build status check inconclusive${NC}"
fi

# Step 6: Deployment Summary
echo -e "${YELLOW}📊 Step 6: Deployment Summary${NC}"
echo ""
echo -e "${GREEN}🎉 PRODUCTION BUILD COMPLETED SUCCESSFULLY! 🎉${NC}"
echo ""
echo "📱 iOS Build: Ready for App Store submission"
echo "🤖 Android Build: Ready for Google Play submission"
echo ""
echo -e "${BLUE}📋 Next Steps:${NC}"
echo "1. Download build artifacts from Expo"
echo "2. Test builds on physical devices"
echo "3. Submit to app stores"
echo "4. Monitor deployment metrics"
echo ""

# Step 7: Generate Deployment Report
echo -e "${YELLOW}📄 Generating deployment report...${NC}"

cat > deployment-report.md << EOF
# Ocean Soul Sparkles - Production Deployment Report

**Date:** $(date)
**Version:** $VERSION
**Build Number:** $BUILD_NUMBER
**Release Channel:** $RELEASE_CHANNEL

## Build Status
- ✅ iOS Build: Completed
- ✅ Android Build: Completed
- ✅ Environment: Production
- ✅ Dependencies: Updated

## Deployment Artifacts
- iOS: .ipa file ready for App Store
- Android: .aab file ready for Google Play

## Next Steps
1. Download builds from Expo
2. Test on physical devices
3. Submit to app stores
4. Monitor post-deployment

## Deployment Team
- Generated by: Ocean Soul Sparkles Deployment Script
- Environment: Production
- Status: Ready for App Store Submission
EOF

echo -e "${GREEN}✅ Deployment report generated: deployment-report.md${NC}"

echo ""
echo -e "${GREEN}🚀 DEPLOYMENT SCRIPT COMPLETED SUCCESSFULLY! 🚀${NC}"
echo -e "${BLUE}The Ocean Soul Sparkles mobile app is ready for production! 🌊${NC}"
