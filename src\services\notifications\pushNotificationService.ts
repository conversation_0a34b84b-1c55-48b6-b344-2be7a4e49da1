/**
 * Ocean Soul Sparkles Mobile App - Push Notification Service
 * Handles real-time push notifications for staff coordination
 */

import * as Notifications from 'expo-notifications';
import * as Device from 'expo-device';
import { Platform } from 'react-native';
import { supabase } from '@/services/database/supabase';
import { Booking, AdminUser } from '@/types/database';
import { androidNotificationChannelManager } from '@/utils/androidNotificationChannels';
import { iOSNotificationManager } from '@/utils/iOSNotificationManager';

export interface NotificationPayload {
  type: 'new_booking' | 'booking_update' | 'staff_message' | 'availability_request' | 'urgent_booking' | 'emergency';
  bookingId?: string;
  chatThreadId?: string;
  title: string;
  body: string;
  data?: any;
}

export interface StaffNotificationPreferences {
  user_id: string;
  push_token?: string;
  enable_booking_notifications: boolean;
  enable_chat_notifications: boolean;
  quiet_hours: {
    enabled: boolean;
    startTime: string;
    endTime: string;
  };
}

class PushNotificationService {
  private isInitialized = false;
  private currentPushToken: string | null = null;

  /**
   * Safely get authenticated user with proper error handling
   */
  private async getAuthenticatedUser(): Promise<{ user: any | null; error: string | null }> {
    try {
      const { data: { user }, error: authError } = await supabase.auth.getUser();

      if (authError) {
        return { user: null, error: `Authentication error: ${authError.message}` };
      }

      if (!user) {
        return { user: null, error: 'No authenticated user found' };
      }

      if (!user.id || typeof user.id !== 'string') {
        return { user: null, error: 'Invalid user ID format' };
      }

      return { user, error: null };

    } catch (error) {
      return { user: null, error: `Auth state retrieval failed: ${error}` };
    }
  }

  /**
   * Split array into chunks of specified size
   */
  private chunkArray<T>(array: T[], chunkSize: number): T[][] {
    const chunks: T[][] = [];
    for (let i = 0; i < array.length; i += chunkSize) {
      chunks.push(array.slice(i, i + chunkSize));
    }
    return chunks;
  }

  /**
   * Verify push notification delivery receipts
   */
  async verifyPushReceipts(receiptIds: string[]): Promise<void> {
    try {
      if (receiptIds.length === 0) return;

      const response = await fetch('https://exp.host/--/api/v2/push/getReceipts', {
        method: 'POST',
        headers: {
          Accept: 'application/json',
          'Accept-encoding': 'gzip, deflate',
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ ids: receiptIds }),
      });

      if (!response.ok) {
        console.error('❌ Failed to get push receipts:', await response.text());
        return;
      }

      const result = await response.json();
      console.log('📋 Push notification receipts verified:', result);

      // Log any delivery errors
      if (result.data) {
        Object.entries(result.data).forEach(([receiptId, receipt]: [string, any]) => {
          if (receipt.status === 'error') {
            console.error(`❌ Push notification ${receiptId} failed:`, receipt.message);
          } else if (receipt.status === 'ok') {
            console.log(`✅ Push notification ${receiptId} delivered successfully`);
          }
        });
      }

    } catch (error) {
      console.error('❌ Error verifying push receipts:', error);
    }
  }

  /**
   * Initialize push notification service
   */
  async initialize(): Promise<void> {
    try {
      console.log('🔔 Initializing push notification service...');

      // Initialize platform-specific notification features
      await androidNotificationChannelManager.initializeChannels();
      await iOSNotificationManager.initialize();

      // Configure notification behavior (iOS manager will override if needed)
      if (Platform.OS !== 'ios') {
        Notifications.setNotificationHandler({
          handleNotification: async () => ({
            shouldShowAlert: true,
            shouldPlaySound: true,
            shouldSetBadge: true,
          }),
        });
      }

      // Request permissions
      await this.requestPermissions();

      // Register for push notifications
      await this.registerForPushNotifications();

      // Set up iOS notification action handlers
      if (Platform.OS === 'ios') {
        iOSNotificationManager.setupNotificationActionHandlers();
      }

      this.isInitialized = true;
      console.log('✅ Push notification service initialized');

    } catch (error) {
      console.error('❌ Failed to initialize push notifications:', error);
      throw error;
    }
  }

  /**
   * Request notification permissions with platform-specific handling
   */
  private async requestPermissions(): Promise<boolean> {
    if (!Device.isDevice) {
      console.warn('⚠️ Push notifications only work on physical devices');
      return false;
    }

    try {
      const { status: existingStatus } = await Notifications.getPermissionsAsync();
      let finalStatus = existingStatus;

      if (existingStatus !== 'granted') {
        if (Platform.OS === 'ios') {
          // Use iOS-specific permission request
          const iOSSettings = await iOSNotificationManager.requestPermissions({
            allowAlert: true,
            allowBadge: true,
            allowSound: true,
            allowCriticalAlerts: false,
            allowProvisional: false,
            allowAnnouncements: false,
          });

          finalStatus = (iOSSettings.allowAlert && iOSSettings.allowSound) ? 'granted' as any : 'denied' as any;
          console.log('📱 iOS notification settings:', iOSSettings);
        } else {
          // Standard permission request for Android
          const { status } = await Notifications.requestPermissionsAsync();
          finalStatus = status;
        }
      }

      if (finalStatus !== 'granted') {
        console.warn('⚠️ Push notification permissions not granted');
        return false;
      }

      console.log('✅ Push notification permissions granted');
      return true;

    } catch (error) {
      console.error('❌ Failed to request notification permissions:', error);
      return false;
    }
  }

  /**
   * Register device for push notifications
   */
  private async registerForPushNotifications(): Promise<void> {
    try {
      const token = (await Notifications.getExpoPushTokenAsync()).data;
      this.currentPushToken = token;

      // Store token in user profile with proper auth validation
      const { user, error: authError } = await this.getAuthenticatedUser();

      if (authError) {
        console.warn('⚠️', authError);
        // Continue without storing token if auth fails
      } else if (user) {
        try {
          await this.updateStaffNotificationPreferences(user.id, {
            push_token: token,
          });
          console.log('📱 Push token registered and stored for user:', user.id);
        } catch (updateError) {
          console.warn('⚠️ Failed to store push token in user profile:', updateError);
          // Continue execution - token is still registered locally
        }
      }

      console.log('📱 Push token registered locally:', token);

    } catch (error) {
      console.error('❌ Failed to register for push notifications:', error);
      throw error; // Re-throw to indicate initialization failure
    }
  }

  /**
   * Schedule a push notification for future delivery
   */
  async scheduleNotification(
    pushToken: string,
    notification: {
      title: string;
      body: string;
      data?: any;
      type?: string;
    },
    scheduledTime: Date
  ): Promise<boolean> {
    try {
      // Calculate seconds from now until scheduled time
      const secondsFromNow = Math.floor((scheduledTime.getTime() - Date.now()) / 1000);

      if (secondsFromNow <= 0) {
        // If time is in the past or now, send immediately
        return this.sendImmediatePushNotification(pushToken, notification);
      }

      const message: any = {
        to: pushToken,
        sound: 'default',
        title: notification.title,
        body: notification.body,
        data: notification.data || {},
        ttl: secondsFromNow + 60, // Time to live (add 60 seconds buffer)
      };

      // Add Android-specific channel ID
      if (Platform.OS === 'android') {
        const channelId = notification.type
          ? androidNotificationChannelManager.getChannelForNotificationType(notification.type)
          : 'general_updates';
        message.channelId = channelId;
      }

      const response = await fetch('https://exp.host/--/api/v2/push/send', {
        method: 'POST',
        headers: {
          Accept: 'application/json',
          'Accept-encoding': 'gzip, deflate',
          'Content-Type': 'application/json',
        },
        body: JSON.stringify([message]),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('❌ Failed to schedule push notification:', errorText);
        return false;
      }

      const result = await response.json();
      console.log('📅 Push notification scheduled for:', scheduledTime, result);
      return true;

    } catch (error) {
      console.error('❌ Error scheduling push notification:', error);
      return false;
    }
  }

  /**
   * Send notification to specific staff members
   */
  async sendNotificationToStaff(
    staffIds: string[],
    notification: NotificationPayload
  ): Promise<void> {
    try {
      // Get staff notification preferences
      const { data: staffPrefs } = await supabase
        .from('staff_notification_preferences')
        .select('*')
        .in('user_id', staffIds);

      if (!staffPrefs?.length) {
        console.warn('⚠️ No staff notification preferences found');
        return;
      }

      // Filter staff who have notifications enabled
      const eligibleStaff = staffPrefs.filter(pref =>
        pref.push_token &&
        this.shouldSendNotification(pref, notification.type)
      );

      if (!eligibleStaff.length) {
        console.log('ℹ️ No eligible staff for notifications');
        return;
      }

      // Send push notifications to eligible staff with platform-specific features
      const messages = eligibleStaff.map(staff => {
        const baseMessage: any = {
          to: staff.push_token,
          sound: 'default',
          title: notification.title,
          body: notification.body,
          data: notification.data,
        };

        // Add platform-specific features
        if (Platform.OS === 'android') {
          const channelId = androidNotificationChannelManager.getChannelForNotificationType(notification.type);
          baseMessage.channelId = channelId;
        } else if (Platform.OS === 'ios') {
          const categoryId = iOSNotificationManager.getCategoryForNotificationType(notification.type);
          baseMessage.categoryId = categoryId;

          // Add iOS-specific features
          baseMessage.badge = 1;
          baseMessage.threadId = notification.data?.threadId || notification.type;

          // Set interruption level for urgent notifications
          if (notification.type === 'urgent_booking' || notification.type === 'emergency') {
            baseMessage.interruptionLevel = 'critical';
          } else if (notification.type === 'staff_message') {
            baseMessage.interruptionLevel = 'timeSensitive';
          } else {
            baseMessage.interruptionLevel = 'active';
          }
        }

        return baseMessage;
      });

      if (messages.length > 0) {
        try {
          // Send actual push notifications using Expo's push service
          const chunks = this.chunkArray(messages, 100); // Expo limit is 100 per request
          const receipts: string[] = [];

          for (const chunk of chunks) {
            const response = await fetch('https://exp.host/--/api/v2/push/send', {
              method: 'POST',
              headers: {
                Accept: 'application/json',
                'Accept-encoding': 'gzip, deflate',
                'Content-Type': 'application/json',
              },
              body: JSON.stringify(chunk),
            });

            if (!response.ok) {
              const errorText = await response.text();
              console.error('❌ Failed to send push notifications:', errorText);
              throw new Error(`Push notification failed: ${errorText}`);
            }

            const result = await response.json();
            console.log('📤 Push notifications sent successfully:', result);

            // Collect receipt IDs for tracking
            if (result.data) {
              result.data.forEach((receipt: any) => {
                if (receipt.id) {
                  receipts.push(receipt.id);
                }
              });
            }
          }

          // Store receipts for later verification if needed
          if (receipts.length > 0) {
            console.log('📋 Push notification receipts:', receipts);
            // Could store these in database for delivery tracking
          }

        } catch (error) {
          console.error('❌ Error sending push notifications:', error);
          throw error; // Re-throw to allow caller to handle
        }
      } else {
        console.log('ℹ️ No eligible staff for push notifications');
      }

      console.log(`📤 Sent notifications to ${eligibleStaff.length} staff members`);

    } catch (error) {
      console.error('❌ Failed to send staff notifications:', error);
    }
  }

  /**
   * Get all active staff IDs
   */
  async getActiveStaffIds(): Promise<string[]> {
    try {
      const { data: activeStaff } = await supabase
        .from('admin_users')
        .select('id')
        .eq('is_active', true);

      return activeStaff?.map(staff => staff.id) || [];
    } catch (error) {
      console.error('❌ Failed to get active staff IDs:', error);
      return [];
    }
  }

  /**
   * Send notification to all active staff
   */
  async sendNotificationToAllActiveStaff(notification: NotificationPayload): Promise<void> {
    try {
      const activeStaffIds = await this.getActiveStaffIds();

      if (activeStaffIds.length === 0) {
        console.warn('⚠️ No active staff found for notifications');
        return;
      }

      await this.sendNotificationToStaff(activeStaffIds, notification);
    } catch (error) {
      console.error('❌ Failed to send notification to all active staff:', error);
    }
  }

  /**
   * Send new booking notification to all active staff
   */
  async sendNewBookingNotification(booking: Booking): Promise<void> {
    try {
      const notification: NotificationPayload = {
        type: 'new_booking',
        bookingId: booking.id,
        title: '🆕 New Booking Received',
        body: `${booking.customer?.name || 'Customer'} - ${booking.service?.name || 'Service'} on ${new Date(booking.booking_date).toLocaleDateString()}`,
        data: {
          bookingId: booking.id,
          action: 'check_availability',
          screen: 'BookingReview',
        },
      };

      await this.sendNotificationToAllActiveStaff(notification);

    } catch (error) {
      console.error('❌ Failed to send new booking notification:', error);
    }
  }

  /**
   * Check if notification should be sent based on preferences
   */
  private shouldSendNotification(
    preferences: StaffNotificationPreferences,
    notificationType: string
  ): boolean {
    // Check notification type preferences
    if (notificationType === 'new_booking' && !preferences.enable_booking_notifications) {
      return false;
    }
    if (notificationType === 'staff_message' && !preferences.enable_chat_notifications) {
      return false;
    }

    // Check quiet hours
    if (preferences.quiet_hours?.enabled) {
      const now = new Date();
      const currentTime = now.getHours() * 100 + now.getMinutes();
      const startTime = parseInt(preferences.quiet_hours.startTime.replace(':', ''));
      const endTime = parseInt(preferences.quiet_hours.endTime.replace(':', ''));

      if (startTime <= endTime) {
        // Same day quiet hours
        if (currentTime >= startTime && currentTime <= endTime) {
          return false;
        }
      } else {
        // Overnight quiet hours
        if (currentTime >= startTime || currentTime <= endTime) {
          return false;
        }
      }
    }

    return true;
  }

  /**
   * Send immediate push notification to specific token
   */
  async sendImmediatePushNotification(
    pushToken: string,
    notification: {
      title: string;
      body: string;
      data?: any;
      type?: string;
    }
  ): Promise<boolean> {
    try {
      const message: any = {
        to: pushToken,
        sound: 'default',
        title: notification.title,
        body: notification.body,
        data: notification.data || {},
      };

      // Add platform-specific features
      if (Platform.OS === 'android' && notification.type) {
        message.channelId = androidNotificationChannelManager.getChannelForNotificationType(notification.type);
      } else if (Platform.OS === 'ios' && notification.type) {
        message.categoryId = iOSNotificationManager.getCategoryForNotificationType(notification.type);
        message.badge = 1;
        message.threadId = notification.data?.threadId || notification.type;

        // Set interruption level
        if (notification.type === 'urgent_booking' || notification.type === 'emergency') {
          message.interruptionLevel = 'critical';
        } else if (notification.type === 'staff_message') {
          message.interruptionLevel = 'timeSensitive';
        } else {
          message.interruptionLevel = 'active';
        }
      }

      const response = await fetch('https://exp.host/--/api/v2/push/send', {
        method: 'POST',
        headers: {
          Accept: 'application/json',
          'Accept-encoding': 'gzip, deflate',
          'Content-Type': 'application/json',
        },
        body: JSON.stringify([message]),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('❌ Failed to send immediate push notification:', errorText);
        return false;
      }

      const result = await response.json();
      console.log('📤 Immediate push notification sent:', result);
      return true;

    } catch (error) {
      console.error('❌ Error sending immediate push notification:', error);
      return false;
    }
  }

  /**
   * Update staff notification preferences
   */
  async updateStaffNotificationPreferences(
    userId: string,
    preferences: Partial<StaffNotificationPreferences>
  ): Promise<void> {
    try {
      const { error } = await supabase
        .from('staff_notification_preferences')
        .upsert({
          user_id: userId,
          ...preferences,
          updated_at: new Date().toISOString(),
        });

      if (error) throw error;

    } catch (error) {
      console.error('❌ Failed to update notification preferences:', error);
    }
  }
}

export const pushNotificationService = new PushNotificationService();





