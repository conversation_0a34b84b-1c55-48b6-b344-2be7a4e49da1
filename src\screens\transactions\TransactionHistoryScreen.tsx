/**
 * Ocean Soul Sparkles Mobile App - Transaction History Screen
 * Displays past sales with search and filter capabilities
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  FlatList,
  TouchableOpacity,
  TextInput,
  ActivityIndicator,
  Alert,
} from 'react-native';
import { Transaction } from '@/types/database';
import { transactionService } from '@/services/database/transactionService';
import ReceiptModal from '@/components/pos/ReceiptModal';
import { testDatabaseStructure, testTransactionQuery, inspectLoyaltyTransactions } from '@/utils/databaseTest';
import { testTransactionFix } from '@/utils/testTransactionFix';

const TransactionHistoryScreen: React.FC = () => {
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedTransaction, setSelectedTransaction] = useState<Transaction | null>(null);
  const [showReceipt, setShowReceipt] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Helper function to safely call toUpperCase
  const safeToUpperCase = (value: any): string => {
    if (!value) return '';
    if (typeof value !== 'string') return String(value).toUpperCase();
    return value.toUpperCase();
  };

  useEffect(() => {
    loadTransactions();
  }, []);

  useEffect(() => {
    const delayedSearch = setTimeout(() => {
      if (searchQuery.trim()) {
        loadTransactions(searchQuery);
      } else {
        loadTransactions();
      }
    }, 500);

    return () => clearTimeout(delayedSearch);
  }, [searchQuery]);

  const loadTransactions = async (search?: string) => {
    setLoading(true);
    setError(null);

    try {
      console.log('📊 Loading transaction history...');
      
      const result = await transactionService.getTransactions({
        search,
        limit: 50,
        order_by: 'created_at',
        order_direction: 'desc',
      });

      if (result.error) {
        throw new Error(result.error.message);
      }

      setTransactions(result.data || []);
      console.log(`✅ Loaded ${result.data?.length || 0} transactions`);
      
    } catch (err) {
      console.error('❌ Failed to load transactions:', err);
      setError(err instanceof Error ? err.message : 'Failed to load transactions');
    } finally {
      setLoading(false);
    }
  };

  const handleTransactionPress = (transaction: Transaction) => {
    setSelectedTransaction(transaction);
    setShowReceipt(true);
  };

  const handleDebugDatabase = async () => {
    console.log('🔧 Running database debug...');
    try {
      await testDatabaseStructure();
      await testTransactionQuery();
      await inspectLoyaltyTransactions();
      const fixResult = await testTransactionFix();

      if (fixResult.success) {
        Alert.alert(
          'Debug Complete ✅',
          `Transaction fix working! Found ${fixResult.transactionCount} transactions. Check console for details.`
        );
      } else {
        Alert.alert(
          'Debug Complete ⚠️',
          `Transaction fix needs work: ${fixResult.error}. Check console for details.`
        );
      }
    } catch (error) {
      console.error('Debug failed:', error);
      Alert.alert('Debug Failed', 'Check console for errors');
    }
  };

  const handleViewReceipt = (transaction: Transaction) => {
    const receipt = transactionService.generateReceipt(transaction);
    setSelectedTransaction(transaction);
    setShowReceipt(true);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-AU', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const formatPrice = (price: number | undefined | null) => {
    if (price === undefined || price === null || isNaN(price)) {
      return '$0.00';
    }
    return `$${price.toFixed(2)}`;
  };

  const getPaymentMethodIcon = (method: string) => {
    switch (method) {
      case 'cash': return '💵';
      case 'card': return '💳';
      case 'digital_wallet': return '📱';
      default: return '💰';
    }
  };

  const getStatusColor = (status: string | undefined) => {
    if (!status) return '#4CAF50'; // Default to green for loyalty transactions

    switch (status.toLowerCase()) {
      case 'completed': return '#4CAF50';
      case 'earned': return '#4CAF50';
      case 'pending': return '#FF9800';
      case 'failed': return '#F44336';
      case 'refunded': return '#9C27B0';
      case 'redeemed': return '#2196F3';
      default: return '#4CAF50'; // Default to green for loyalty transactions
    }
  };

  const renderTransaction = ({ item }: { item: Transaction }) => (
    <TouchableOpacity
      style={styles.transactionCard}
      onPress={() => handleTransactionPress(item)}
    >
      <View style={styles.transactionHeader}>
        <View style={styles.transactionInfo}>
          <Text style={styles.transactionNumber}>
            #{item.transaction_number || item.id?.slice(-8) || 'N/A'}
          </Text>
          <Text style={styles.transactionDate}>{formatDate(item.created_at)}</Text>
        </View>
        <View style={styles.transactionAmount}>
          <Text style={styles.amount}>
            {item.total_amount ? formatPrice(item.total_amount) : `${item.points || 0} pts`}
          </Text>
          <View style={styles.paymentInfo}>
            <Text style={styles.paymentMethod}>
              {item.payment_method && item.payment_method.trim() ?
                `${getPaymentMethodIcon(item.payment_method)} ${safeToUpperCase(item.payment_method)}` :
                `💳 ${safeToUpperCase(item.transaction_type || 'LOYALTY')}`
              }
            </Text>
          </View>
        </View>
      </View>

      <View style={styles.transactionDetails}>
        {item.customer && (
          <Text style={styles.customer}>👤 {item.customer.full_name}</Text>
        )}
        {item.staff ? (
          <Text style={styles.staff}>
            👨‍💼 {item.staff.first_name} {item.staff.last_name}
          </Text>
        ) : (
          <Text style={styles.staff}>
            👨‍💼 Ocean Soul Sparkles Staff
          </Text>
        )}
        <Text style={styles.itemCount}>
          📦 {item.transaction_items?.length || 'Loyalty'} {item.transaction_items?.length ? 'items' : 'transaction'}
        </Text>
      </View>

      <View style={styles.transactionFooter}>
        <View
          style={[
            styles.statusBadge,
            { backgroundColor: getStatusColor(item.payment_status || item.transaction_type) },
          ]}
        >
          <Text style={styles.statusText}>{safeToUpperCase(item.payment_status || item.transaction_type || 'COMPLETED')}</Text>
        </View>
        <TouchableOpacity
          style={styles.receiptButton}
          onPress={() => handleViewReceipt(item)}
        >
          <Text style={styles.receiptButtonText}>📄 Receipt</Text>
        </TouchableOpacity>
      </View>
    </TouchableOpacity>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyContainer}>
      <Text style={styles.emptyIcon}>📊</Text>
      <Text style={styles.emptyText}>No transactions found</Text>
      <Text style={styles.emptySubtext}>
        {searchQuery 
          ? 'Try adjusting your search terms'
          : 'Completed transactions will appear here'
        }
      </Text>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Transaction History</Text>
      </View>

      {/* Search */}
      <View style={styles.searchContainer}>
        <TextInput
          style={styles.searchInput}
          placeholder="Search transactions..."
          value={searchQuery}
          onChangeText={setSearchQuery}
          clearButtonMode="while-editing"
        />
      </View>

      {/* Content */}
      {error ? (
        <View style={styles.errorContainer}>
          <Text style={styles.errorIcon}>⚠️</Text>
          <Text style={styles.errorTitle}>Failed to Load Transactions</Text>
          <Text style={styles.errorText}>{error}</Text>
          <TouchableOpacity onPress={() => loadTransactions()} style={styles.retryButton}>
            <Text style={styles.retryButtonText}>🔄 Retry</Text>
          </TouchableOpacity>
          <TouchableOpacity onPress={handleDebugDatabase} style={[styles.retryButton, { backgroundColor: '#007AFF', marginTop: 10 }]}>
            <Text style={styles.retryButtonText}>🔧 Debug Database</Text>
          </TouchableOpacity>
          <TouchableOpacity
            onPress={async () => {
              const result = await testTransactionFix();
              if (result.success) {
                Alert.alert('Test Passed ✅', `Found ${result.transactionCount} transactions`);
                // Try to reload transactions after successful test
                loadTransactions();
              } else {
                Alert.alert('Test Failed ❌', result.error || 'Unknown error');
              }
            }}
            style={[styles.retryButton, { backgroundColor: '#10b981', marginTop: 10 }]}
          >
            <Text style={styles.retryButtonText}>🧪 Test Fix</Text>
          </TouchableOpacity>
        </View>
      ) : (
        <FlatList
          data={transactions}
          renderItem={renderTransaction}
          keyExtractor={(item) => item.id}
          contentContainerStyle={styles.listContainer}
          showsVerticalScrollIndicator={false}
          refreshing={loading}
          onRefresh={() => loadTransactions(searchQuery)}
          ListEmptyComponent={!loading ? renderEmptyState : null}
          ListHeaderComponent={
            loading && transactions.length === 0 ? (
              <View style={styles.loadingContainer}>
                <ActivityIndicator size="large" color="#FF9A8B" />
                <Text style={styles.loadingText}>Loading transactions...</Text>
              </View>
            ) : null
          }
        />
      )}

      {/* Receipt Modal */}
      {selectedTransaction && (
        <ReceiptModal
          visible={showReceipt}
          onClose={() => {
            setShowReceipt(false);
            setSelectedTransaction(null);
          }}
          receipt={selectedTransaction ? transactionService.generateReceipt(selectedTransaction) : null}
        />
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    padding: 16,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
  },
  searchContainer: {
    padding: 16,
    backgroundColor: '#fff',
  },
  searchInput: {
    backgroundColor: '#f8f9fa',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  listContainer: {
    padding: 16,
  },
  transactionCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  transactionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  transactionInfo: {
    flex: 1,
  },
  transactionNumber: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginBottom: 4,
  },
  transactionDate: {
    fontSize: 14,
    color: '#666',
  },
  transactionAmount: {
    alignItems: 'flex-end',
  },
  amount: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#FF9A8B',
    marginBottom: 4,
  },
  paymentInfo: {
    alignItems: 'flex-end',
  },
  paymentMethod: {
    fontSize: 12,
    color: '#666',
    fontWeight: '500',
  },
  transactionDetails: {
    marginBottom: 12,
  },
  customer: {
    fontSize: 14,
    color: '#333',
    marginBottom: 2,
  },
  staff: {
    fontSize: 14,
    color: '#666',
    marginBottom: 2,
  },
  itemCount: {
    fontSize: 14,
    color: '#666',
  },
  transactionFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    fontSize: 12,
    color: '#fff',
    fontWeight: '600',
  },
  receiptButton: {
    backgroundColor: '#f8f9fa',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  receiptButtonText: {
    fontSize: 14,
    color: '#666',
    fontWeight: '500',
  },
  loadingContainer: {
    padding: 40,
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#666',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  errorIcon: {
    fontSize: 64,
    marginBottom: 16,
  },
  errorTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
    textAlign: 'center',
  },
  errorText: {
    fontSize: 16,
    color: '#ff4444',
    textAlign: 'center',
    marginBottom: 20,
    lineHeight: 22,
  },
  retryButton: {
    backgroundColor: '#FF9A8B',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  retryButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  emptyContainer: {
    padding: 40,
    alignItems: 'center',
  },
  emptyIcon: {
    fontSize: 64,
    marginBottom: 16,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
  },
  emptySubtext: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    lineHeight: 20,
  },
});

export default TransactionHistoryScreen;
