/**
 * Ocean Soul Sparkles Mobile App - Production Status Dashboard
 * Unified dashboard showing all system validation and production readiness status
 */

import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  ScrollView,
  Modal,
  RefreshControl,
} from 'react-native';
import { productionReadinessValidator, ProductionReadinessReport } from '@/services/validation/productionReadinessValidator';
import { systemHealthMonitor, SystemHealthMetrics } from '@/services/monitoring/systemHealthMonitor';
import { productionDeploymentChecklist, DeploymentChecklistReport } from '@/services/deployment/productionDeploymentChecklist';
import AdminPortalStatusIndicator from './AdminPortalStatusIndicator';
import DatabaseStatusDashboard from './DatabaseStatusDashboard';

interface ProductionStatusDashboardProps {
  autoRefresh?: boolean;
  refreshInterval?: number;
  onStatusChange?: (status: any) => void;
}

const ProductionStatusDashboard: React.FC<ProductionStatusDashboardProps> = ({
  autoRefresh = true,
  refreshInterval = 60000, // 1 minute
  onStatusChange,
}) => {
  const [productionReport, setProductionReport] = useState<ProductionReadinessReport | null>(null);
  const [healthMetrics, setHealthMetrics] = useState<SystemHealthMetrics | null>(null);
  const [deploymentChecklist, setDeploymentChecklist] = useState<DeploymentChecklistReport | null>(null);
  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);
  const [showDetailedReport, setShowDetailedReport] = useState(false);
  const [selectedReport, setSelectedReport] = useState<'production' | 'health' | 'deployment' | null>(null);
  const isMountedRef = useRef(true);

  useEffect(() => {
    isMountedRef.current = true;
    loadAllStatus();

    if (autoRefresh) {
      const interval = setInterval(loadAllStatus, refreshInterval);
      return () => {
        clearInterval(interval);
        isMountedRef.current = false;
      };
    }

    return () => {
      isMountedRef.current = false;
    };
  }, [autoRefresh, refreshInterval]);

  const loadAllStatus = async () => {
    try {
      if (!isMountedRef.current) return;

      setLoading(true);

      const [production, health, deployment] = await Promise.allSettled([
        productionReadinessValidator.validateProductionReadiness(),
        systemHealthMonitor.performHealthCheck(),
        productionDeploymentChecklist.generateDeploymentChecklist(),
      ]);

      if (!isMountedRef.current) return;

      if (production.status === 'fulfilled') {
        setProductionReport(production.value);
      }

      if (health.status === 'fulfilled') {
        setHealthMetrics(health.value);
      }

      if (deployment.status === 'fulfilled') {
        setDeploymentChecklist(deployment.value);
      }

      setLastUpdated(new Date());
      
      if (onStatusChange) {
        onStatusChange({
          production: production.status === 'fulfilled' ? production.value : null,
          health: health.status === 'fulfilled' ? health.value : null,
          deployment: deployment.status === 'fulfilled' ? deployment.value : null,
        });
      }
    } catch (error) {
      console.error('❌ Failed to load production status:', error);
      if (isMountedRef.current) {
        Alert.alert('Error', 'Failed to load production status');
      }
    } finally {
      if (isMountedRef.current) {
        setLoading(false);
        setRefreshing(false);
      }
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadAllStatus();
  };

  const getOverallStatus = () => {
    if (!productionReport || !healthMetrics || !deploymentChecklist) return 'unknown';
    
    if (deploymentChecklist.deploymentReady && 
        productionReport.overall === 'ready' && 
        healthMetrics.overall === 'healthy') {
      return 'ready';
    }
    
    if (deploymentChecklist.criticalBlockers.length > 0 || 
        productionReport.overall === 'not_ready' || 
        healthMetrics.overall === 'critical') {
      return 'not_ready';
    }
    
    return 'warning';
  };

  const getStatusColor = () => {
    const status = getOverallStatus();
    switch (status) {
      case 'ready': return '#10b981';
      case 'warning': return '#f59e0b';
      case 'not_ready': return '#ef4444';
      default: return '#6b7280';
    }
  };

  const getStatusIcon = () => {
    if (loading) return '⏳';
    const status = getOverallStatus();
    switch (status) {
      case 'ready': return '🚀';
      case 'warning': return '⚠️';
      case 'not_ready': return '🚨';
      default: return '❓';
    }
  };

  const getStatusText = () => {
    if (loading) return 'Checking Production Status...';
    const status = getOverallStatus();
    switch (status) {
      case 'ready': return 'Production Ready';
      case 'warning': return 'Production Warning';
      case 'not_ready': return 'Not Production Ready';
      default: return 'Status Unknown';
    }
  };

  const showReport = (reportType: 'production' | 'health' | 'deployment') => {
    setSelectedReport(reportType);
    setShowDetailedReport(true);
  };

  const runFullValidation = async () => {
    Alert.alert(
      'Run Full Production Validation',
      'This will run comprehensive validation of all systems. This may take several minutes.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Run Validation',
          onPress: async () => {
            setLoading(true);
            try {
              await loadAllStatus();
              Alert.alert(
                'Validation Complete',
                `Production Status: ${getOverallStatus().toUpperCase()}\n\nTap "View Reports" to see detailed results.`,
                [
                  { text: 'OK' },
                  {
                    text: 'View Reports',
                    onPress: () => showReport('production'),
                  },
                ]
              );
            } catch (error) {
              Alert.alert('Validation Failed', 'Failed to run full production validation');
            } finally {
              setLoading(false);
            }
          },
        },
      ]
    );
  };

  return (
    <>
      <ScrollView
        style={styles.container}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {/* Overall Status Header */}
        <View style={styles.headerContainer}>
          <View style={styles.statusRow}>
            <View style={[styles.statusDot, { backgroundColor: getStatusColor() }]} />
            <Text style={styles.statusText}>{getStatusText()}</Text>
            {loading && <ActivityIndicator size="small" color={getStatusColor()} />}
          </View>
          
          {productionReport && (
            <Text style={styles.readinessScore}>
              Readiness Score: {productionReport.readinessScore}%
            </Text>
          )}
        </View>

        {/* Quick Status Cards */}
        <View style={styles.statusCardsContainer}>
          <TouchableOpacity 
            style={styles.statusCard}
            onPress={() => showReport('production')}
          >
            <Text style={styles.cardTitle}>Production Readiness</Text>
            <Text style={[styles.cardValue, { color: getStatusColor() }]}>
              {productionReport ? `${productionReport.readinessScore}%` : '---'}
            </Text>
            <Text style={styles.cardSubtitle}>
              {productionReport ? productionReport.overall.replace('_', ' ').toUpperCase() : 'Loading...'}
            </Text>
          </TouchableOpacity>

          <TouchableOpacity 
            style={styles.statusCard}
            onPress={() => showReport('health')}
          >
            <Text style={styles.cardTitle}>System Health</Text>
            <Text style={[styles.cardValue, { color: getStatusColor() }]}>
              {healthMetrics ? `${healthMetrics.healthScore}%` : '---'}
            </Text>
            <Text style={styles.cardSubtitle}>
              {healthMetrics ? healthMetrics.overall.toUpperCase() : 'Loading...'}
            </Text>
          </TouchableOpacity>

          <TouchableOpacity 
            style={styles.statusCard}
            onPress={() => showReport('deployment')}
          >
            <Text style={styles.cardTitle}>Deployment Ready</Text>
            <Text style={[styles.cardValue, { color: getStatusColor() }]}>
              {deploymentChecklist ? (deploymentChecklist.deploymentReady ? '✅' : '❌') : '---'}
            </Text>
            <Text style={styles.cardSubtitle}>
              {deploymentChecklist ? 
                `${deploymentChecklist.criticalBlockers.length} blockers` : 
                'Loading...'}
            </Text>
          </TouchableOpacity>
        </View>

        {/* System Components */}
        <View style={styles.componentsContainer}>
          <Text style={styles.sectionTitle}>System Components</Text>
          
          <AdminPortalStatusIndicator showDetails={true} />
          <DatabaseStatusDashboard showDetails={true} />
          
          {/* Enhanced Workflow Status */}
          <View style={styles.componentCard}>
            <View style={styles.componentHeader}>
              <Text style={styles.componentTitle}>Enhanced Booking Workflow</Text>
              <View style={[styles.componentStatus, { 
                backgroundColor: productionReport?.systemReports.enhancedWorkflow?.overall === 'pass' ? '#10b981' : '#ef4444' 
              }]}>
                <Text style={styles.componentStatusText}>
                  {productionReport?.systemReports.enhancedWorkflow?.overall === 'pass' ? '✅' : '❌'}
                </Text>
              </View>
            </View>
            <Text style={styles.componentDescription}>
              Booking-to-quote workflow with distance pricing and email notifications
            </Text>
          </View>

          {/* Email System Status */}
          <View style={styles.componentCard}>
            <View style={styles.componentHeader}>
              <Text style={styles.componentTitle}>Email Notification System</Text>
              <View style={[styles.componentStatus, { 
                backgroundColor: productionReport?.systemReports.emailNotifications.overall ? '#10b981' : '#ef4444' 
              }]}>
                <Text style={styles.componentStatusText}>
                  {productionReport?.systemReports.emailNotifications.overall ? '✅' : '❌'}
                </Text>
              </View>
            </View>
            <Text style={styles.componentDescription}>
              Automated email notifications for quotes, bookings, and confirmations
            </Text>
          </View>
        </View>

        {/* Action Buttons */}
        <View style={styles.actionsContainer}>
          <TouchableOpacity
            style={[styles.button, styles.refreshButton]}
            onPress={onRefresh}
            disabled={loading}
          >
            <Text style={styles.buttonText}>Refresh Status</Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[styles.button, styles.validateButton]}
            onPress={runFullValidation}
            disabled={loading}
          >
            <Text style={styles.buttonText}>Full Validation</Text>
          </TouchableOpacity>
        </View>

        {/* Critical Issues Alert */}
        {deploymentChecklist && deploymentChecklist.criticalBlockers.length > 0 && (
          <View style={styles.alertContainer}>
            <Text style={styles.alertTitle}>🚨 Critical Issues</Text>
            {deploymentChecklist.criticalBlockers.slice(0, 3).map((blocker, index) => (
              <Text key={index} style={styles.alertText}>• {blocker}</Text>
            ))}
            {deploymentChecklist.criticalBlockers.length > 3 && (
              <Text style={styles.alertText}>
                ... and {deploymentChecklist.criticalBlockers.length - 3} more
              </Text>
            )}
          </View>
        )}

        {lastUpdated && (
          <Text style={styles.lastUpdated}>
            Last updated: {lastUpdated.toLocaleTimeString()}
          </Text>
        )}
      </ScrollView>

      {/* Detailed Report Modal */}
      <Modal
        visible={showDetailedReport}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setShowDetailedReport(false)}
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>
              {selectedReport === 'production' && 'Production Readiness Report'}
              {selectedReport === 'health' && 'System Health Report'}
              {selectedReport === 'deployment' && 'Deployment Checklist'}
            </Text>
            <TouchableOpacity
              style={styles.closeButton}
              onPress={() => setShowDetailedReport(false)}
            >
              <Text style={styles.closeButtonText}>✕</Text>
            </TouchableOpacity>
          </View>
          
          <ScrollView style={styles.modalContent}>
            {selectedReport === 'production' && productionReport && (
              <View>
                <Text style={styles.reportTitle}>
                  Overall Status: {productionReport.overall.replace('_', ' ').toUpperCase()}
                </Text>
                <Text style={styles.reportStats}>
                  Readiness Score: {productionReport.readinessScore}%
                </Text>
                <Text style={styles.reportStats}>
                  Ready Systems: {productionReport.summary.readySystems}/{productionReport.summary.totalSystems}
                </Text>
                
                {productionReport.criticalBlockers.length > 0 && (
                  <View style={styles.criticalSection}>
                    <Text style={styles.sectionTitle}>Critical Blockers</Text>
                    {productionReport.criticalBlockers.map((blocker, index) => (
                      <Text key={index} style={styles.criticalText}>• {blocker}</Text>
                    ))}
                  </View>
                )}

                <View style={styles.recommendationsSection}>
                  <Text style={styles.sectionTitle}>Recommendations</Text>
                  {productionReport.recommendations.map((rec, index) => (
                    <Text key={index} style={styles.recommendationText}>• {rec}</Text>
                  ))}
                </View>
              </View>
            )}

            {selectedReport === 'health' && healthMetrics && (
              <View>
                <Text style={styles.reportTitle}>
                  Health Status: {healthMetrics.overall.toUpperCase()}
                </Text>
                <Text style={styles.reportStats}>
                  Health Score: {healthMetrics.healthScore}%
                </Text>
                <Text style={styles.reportStats}>
                  Uptime: {Math.round(healthMetrics.performance.uptime)} seconds
                </Text>
                <Text style={styles.reportStats}>
                  Response Time: {healthMetrics.performance.averageResponseTime}ms
                </Text>

                {healthMetrics.alerts.length > 0 && (
                  <View style={styles.alertsSection}>
                    <Text style={styles.sectionTitle}>Active Alerts</Text>
                    {healthMetrics.alerts.map((alert, index) => (
                      <Text key={index} style={styles.alertText}>
                        [{alert.severity.toUpperCase()}] {alert.message}
                      </Text>
                    ))}
                  </View>
                )}
              </View>
            )}

            {selectedReport === 'deployment' && deploymentChecklist && (
              <View>
                <Text style={styles.reportTitle}>
                  Deployment Ready: {deploymentChecklist.deploymentReady ? 'YES' : 'NO'}
                </Text>
                <Text style={styles.reportStats}>
                  Readiness Score: {deploymentChecklist.readinessScore}%
                </Text>
                <Text style={styles.reportStats}>
                  Critical Blockers: {deploymentChecklist.criticalBlockers.length}
                </Text>

                {Object.entries(deploymentChecklist.categories).map(([key, category]) => (
                  <View key={key} style={styles.categorySection}>
                    <Text style={styles.categoryTitle}>
                      {category.name}: {category.score}% ({category.passedItems}/{category.totalItems})
                    </Text>
                    <View style={[styles.categoryStatus, { 
                      backgroundColor: category.status === 'pass' ? '#10b981' : 
                                     category.status === 'warning' ? '#f59e0b' : '#ef4444' 
                    }]}>
                      <Text style={styles.categoryStatusText}>{category.status.toUpperCase()}</Text>
                    </View>
                  </View>
                ))}
              </View>
            )}
          </ScrollView>
        </View>
      </Modal>
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  headerContainer: {
    backgroundColor: '#fff',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  statusRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  statusDot: {
    width: 16,
    height: 16,
    borderRadius: 8,
    marginRight: 12,
  },
  statusText: {
    fontSize: 20,
    fontWeight: '600',
    color: '#333',
    flex: 1,
  },
  readinessScore: {
    fontSize: 16,
    color: '#666',
    fontWeight: '500',
  },
  statusCardsContainer: {
    flexDirection: 'row',
    padding: 16,
    gap: 12,
  },
  statusCard: {
    flex: 1,
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 16,
    alignItems: 'center',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  cardTitle: {
    fontSize: 12,
    color: '#666',
    marginBottom: 8,
    textAlign: 'center',
  },
  cardValue: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  cardSubtitle: {
    fontSize: 10,
    color: '#999',
    textAlign: 'center',
  },
  componentsContainer: {
    padding: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginBottom: 16,
  },
  componentCard: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 16,
    marginBottom: 12,
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
  },
  componentHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  componentTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    flex: 1,
  },
  componentStatus: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  componentStatusText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '600',
  },
  componentDescription: {
    fontSize: 14,
    color: '#666',
  },
  actionsContainer: {
    flexDirection: 'row',
    padding: 16,
    gap: 12,
  },
  button: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    alignItems: 'center',
  },
  refreshButton: {
    backgroundColor: '#6b7280',
  },
  validateButton: {
    backgroundColor: '#3b82f6',
  },
  buttonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  alertContainer: {
    backgroundColor: '#fef2f2',
    borderRadius: 8,
    padding: 16,
    margin: 16,
    borderLeftWidth: 4,
    borderLeftColor: '#ef4444',
  },
  alertTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#dc2626',
    marginBottom: 8,
  },
  alertText: {
    fontSize: 14,
    color: '#dc2626',
    marginBottom: 4,
  },
  lastUpdated: {
    fontSize: 12,
    color: '#9ca3af',
    textAlign: 'center',
    padding: 16,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
  },
  closeButton: {
    padding: 8,
  },
  closeButtonText: {
    fontSize: 18,
    color: '#666',
  },
  modalContent: {
    flex: 1,
    padding: 16,
  },
  reportTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 16,
  },
  reportStats: {
    fontSize: 16,
    color: '#666',
    marginBottom: 8,
  },
  criticalSection: {
    backgroundColor: '#fef2f2',
    borderRadius: 8,
    padding: 16,
    marginVertical: 16,
    borderLeftWidth: 4,
    borderLeftColor: '#ef4444',
  },
  criticalText: {
    fontSize: 14,
    color: '#dc2626',
    marginBottom: 4,
  },
  recommendationsSection: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 16,
    marginVertical: 16,
  },
  recommendationText: {
    fontSize: 14,
    color: '#333',
    marginBottom: 4,
  },
  alertsSection: {
    backgroundColor: '#fef3c7',
    borderRadius: 8,
    padding: 16,
    marginVertical: 16,
    borderLeftWidth: 4,
    borderLeftColor: '#f59e0b',
  },
  categorySection: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 16,
    marginVertical: 8,
  },
  categoryTitle: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333',
    flex: 1,
  },
  categoryStatus: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  categoryStatusText: {
    color: '#fff',
    fontSize: 10,
    fontWeight: '600',
  },
});

export default ProductionStatusDashboard;
