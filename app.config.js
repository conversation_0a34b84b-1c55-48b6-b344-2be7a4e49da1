/**
 * Ocean Soul Sparkles Mobile App - Expo Configuration
 * Handles Android 14+ compatibility and permissions
 */

export default {
  expo: {
    name: "Ocean Soul Sparkles",
    slug: "oceansoulsparkles-app",
    version: "1.0.0",
    orientation: "portrait",
    userInterfaceStyle: "automatic",
    splash: {
      resizeMode: "contain",
      backgroundColor: "#FF9A8B"
    },
    assetBundlePatterns: [
      "**/*"
    ],
    ios: {
      supportsTablet: true,
      bundleIdentifier: "com.oceansoulsparkles.app",
      buildNumber: "1",
      infoPlist: {
        NSCameraUsageDescription: "This app uses the camera to scan QR codes and take photos for products.",
        NSMicrophoneUsageDescription: "This app uses the microphone for voice notes and customer communication.",
        NSLocationWhenInUseUsageDescription: "This app uses location to provide location-based services and delivery tracking."
      }
    },
    android: {
      adaptiveIcon: {
        backgroundColor: "#FF9A8B"
      },
      package: "com.oceansoulsparkles.app",
      versionCode: 1,
      compileSdkVersion: 34,
      targetSdkVersion: 34,
      permissions: [
        "CAMERA",
        "RECORD_AUDIO", 
        "ACCESS_FINE_LOCATION",
        "ACCESS_COARSE_LOCATION",
        "INTERNET",
        "ACCESS_NETWORK_STATE",
        "WRITE_EXTERNAL_STORAGE",
        "READ_EXTERNAL_STORAGE",
        "android.permission.DETECT_SCREEN_CAPTURE",
        // Push notification permissions
        "android.permission.RECEIVE_BOOT_COMPLETED",
        "android.permission.VIBRATE",
        "android.permission.WAKE_LOCK",
        "com.google.android.c2dm.permission.RECEIVE",
        // Additional Android 14+ permissions
        "android.permission.POST_NOTIFICATIONS",
        "android.permission.USE_FULL_SCREEN_INTENT"
      ]
    },
    web: {
      bundler: "metro"
    },
    plugins: [
      [
        "expo-splash-screen",
        {
          backgroundColor: "#FF9A8B"
        }
      ],
      [
        "expo-notifications",
        {
          icon: "./assets/notification-icon.png",
          color: "#FF9A8B",
          sounds: ["./assets/notification-sound.wav"],
          mode: "production"
        }
      ],
      // Custom plugin to handle Android 14+ permissions
      [
        function withAndroidPermissions(config) {
          return config;
        }
      ]
    ],
    extra: {
      eas: {
        projectId: "your-eas-project-id"
      }
    }
  }
};
