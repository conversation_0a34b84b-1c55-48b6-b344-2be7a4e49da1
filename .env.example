# Ocean Soul Sparkles Mobile App - Environment Configuration

# Supabase Configuration (shared with admin portal)
EXPO_PUBLIC_SUPABASE_URL=https://ndlgbcsbidyhxbpqzgqp.supabase.co
EXPO_PUBLIC_SUPABASE_ANON_KEY=your-supabase-anon-key-here.

# Square Payment Configuration
EXPO_PUBLIC_SQUARE_APPLICATION_ID=*****************************
EXPO_PUBLIC_SQUARE_LOCATION_ID=LBZPW61WHXG6F
SQUARE_ACCESS_TOKEN=your-square-access-token-here

# App Configuration
EXPO_PUBLIC_APP_NAME=Ocean Soul Sparkles
EXPO_PUBLIC_APP_VERSION=1.0.0
EXPO_PUBLIC_ENVIRONMENT=development

# API Configuration
EXPO_PUBLIC_API_BASE_URL=https://admin.oceansoulsparkles.com.au/api

# Email Configuration
EXPO_PUBLIC_EMAIL_ENABLED=true
EXPO_PUBLIC_EMAIL_FALLBACK_TEMPLATES=true
EXPO_PUBLIC_BUSINESS_EMAIL=<EMAIL>
EXPO_PUBLIC_BUSINESS_PHONE=+61 XXX XXX XXX
EXPO_PUBLIC_BUSINESS_WEBSITE=oceansoulsparkles.com.au

# Feature Flags
EXPO_PUBLIC_ENABLE_OFFLINE_MODE=true
EXPO_PUBLIC_ENABLE_PUSH_NOTIFICATIONS=true
EXPO_PUBLIC_ENABLE_ANALYTICS=false

# Development Configuration
EXPO_PUBLIC_DEBUG_MODE=true
EXPO_PUBLIC_LOG_LEVEL=debug

# Security Configuration
EXPO_PUBLIC_ENCRYPTION_KEY=your-encryption-key-here
EXPO_PUBLIC_JWT_SECRET=your-jwt-secret-here

# Optional: Third-party Services
EXPO_PUBLIC_SENTRY_DSN=your-sentry-dsn-here
EXPO_PUBLIC_ANALYTICS_ID=your-analytics-id-here
