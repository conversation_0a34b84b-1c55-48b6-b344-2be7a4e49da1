/**
 * Ocean Soul Sparkles Mobile App - Expo Version Checker
 * Checks Expo SDK version compatibility and provides upgrade guidance
 */

import Constants from 'expo-constants';
import { Platform } from 'react-native';

interface ExpoVersionInfo {
  currentSDK: string;
  targetSDK: string;
  isCompatible: boolean;
  needsUpgrade: boolean;
  supportedFeatures: string[];
  unsupportedFeatures: string[];
  upgradeInstructions: string[];
}

interface SDKFeatureSupport {
  [key: string]: {
    minSDK: number;
    description: string;
    platforms: ('ios' | 'android' | 'web')[];
  };
}

class ExpoVersionChecker {
  private readonly TARGET_SDK_VERSION = 50;
  private readonly MIN_SUPPORTED_SDK = 49;

  private readonly FEATURE_SUPPORT: SDKFeatureSupport = {
    'expo-notifications': {
      minSDK: 45,
      description: 'Push notifications and local notifications',
      platforms: ['ios', 'android'],
    },
    'expo-device': {
      minSDK: 40,
      description: 'Device information and capabilities',
      platforms: ['ios', 'android', 'web'],
    },
    'notification-channels': {
      minSDK: 47,
      description: 'Android notification channels',
      platforms: ['android'],
    },
    'ios-notification-categories': {
      minSDK: 48,
      description: 'iOS notification categories and actions',
      platforms: ['ios'],
    },
    'critical-alerts': {
      minSDK: 49,
      description: 'iOS critical alerts',
      platforms: ['ios'],
    },
    'background-modes': {
      minSDK: 45,
      description: 'Background processing capabilities',
      platforms: ['ios', 'android'],
    },
    'expo-constants-config': {
      minSDK: 46,
      description: 'Enhanced app configuration access',
      platforms: ['ios', 'android', 'web'],
    },
    'expo-splash-screen': {
      minSDK: 40,
      description: 'Splash screen management',
      platforms: ['ios', 'android'],
    },
    'expo-status-bar': {
      minSDK: 40,
      description: 'Status bar styling',
      platforms: ['ios', 'android'],
    },
    'metro-bundler': {
      minSDK: 49,
      description: 'Enhanced Metro bundler support',
      platforms: ['ios', 'android', 'web'],
    },
  };

  /**
   * Check Expo SDK version compatibility
   */
  checkVersionCompatibility(): ExpoVersionInfo {
    const currentSDKString = this.getCurrentSDKVersion();
    const currentSDK = this.parseSDKVersion(currentSDKString);
    
    const isCompatible = currentSDK >= this.MIN_SUPPORTED_SDK;
    const needsUpgrade = currentSDK < this.TARGET_SDK_VERSION;
    
    const supportedFeatures = this.getSupportedFeatures(currentSDK);
    const unsupportedFeatures = this.getUnsupportedFeatures(currentSDK);
    const upgradeInstructions = this.getUpgradeInstructions(currentSDK);

    return {
      currentSDK: currentSDKString,
      targetSDK: this.TARGET_SDK_VERSION.toString(),
      isCompatible,
      needsUpgrade,
      supportedFeatures,
      unsupportedFeatures,
      upgradeInstructions,
    };
  }

  /**
   * Get current SDK version
   */
  private getCurrentSDKVersion(): string {
    return Constants.expoConfig?.sdkVersion || 
           Constants.sdkVersion || 
           Constants.expoVersion || 
           'unknown';
  }

  /**
   * Parse SDK version to number
   */
  private parseSDKVersion(versionString: string): number {
    if (versionString === 'unknown' || versionString === 'UNVERSIONED') {
      return 0;
    }
    
    const match = versionString.match(/(\d+)/);
    return match ? parseInt(match[1], 10) : 0;
  }

  /**
   * Get supported features for current SDK
   */
  private getSupportedFeatures(currentSDK: number): string[] {
    const currentPlatform = Platform.OS as 'ios' | 'android' | 'web';
    
    return Object.entries(this.FEATURE_SUPPORT)
      .filter(([_, feature]) => 
        currentSDK >= feature.minSDK && 
        feature.platforms.includes(currentPlatform)
      )
      .map(([name, feature]) => `${name}: ${feature.description}`);
  }

  /**
   * Get unsupported features for current SDK
   */
  private getUnsupportedFeatures(currentSDK: number): string[] {
    const currentPlatform = Platform.OS as 'ios' | 'android' | 'web';
    
    return Object.entries(this.FEATURE_SUPPORT)
      .filter(([_, feature]) => 
        currentSDK < feature.minSDK && 
        feature.platforms.includes(currentPlatform)
      )
      .map(([name, feature]) => 
        `${name}: ${feature.description} (requires SDK ${feature.minSDK}+)`
      );
  }

  /**
   * Get upgrade instructions
   */
  private getUpgradeInstructions(currentSDK: number): string[] {
    const instructions: string[] = [];

    if (currentSDK === 0) {
      instructions.push('Unable to determine current Expo SDK version');
      instructions.push('Ensure you are running in an Expo environment');
      instructions.push('Check that expo is properly installed: npm install expo');
      return instructions;
    }

    if (currentSDK < this.MIN_SUPPORTED_SDK) {
      instructions.push(`Current SDK ${currentSDK} is below minimum supported version ${this.MIN_SUPPORTED_SDK}`);
      instructions.push('Upgrade is required for app functionality');
      instructions.push('Run: npx expo install --fix');
      instructions.push('Update package.json expo version to ~50.0.0');
      instructions.push('Update all expo-* packages to compatible versions');
    } else if (currentSDK < this.TARGET_SDK_VERSION) {
      instructions.push(`Current SDK ${currentSDK} is compatible but not optimal`);
      instructions.push('Consider upgrading for latest features and security updates');
      instructions.push('Run: npx expo install --fix');
      instructions.push('Review breaking changes in Expo SDK 50 documentation');
    } else {
      instructions.push('Expo SDK version is up to date');
      instructions.push('All features should be available');
    }

    return instructions;
  }

  /**
   * Check if specific feature is supported
   */
  isFeatureSupported(featureName: string): boolean {
    const currentSDK = this.parseSDKVersion(this.getCurrentSDKVersion());
    const feature = this.FEATURE_SUPPORT[featureName];
    
    if (!feature) {
      return false;
    }

    const currentPlatform = Platform.OS as 'ios' | 'android' | 'web';
    return currentSDK >= feature.minSDK && feature.platforms.includes(currentPlatform);
  }

  /**
   * Get platform-specific compatibility info
   */
  getPlatformCompatibility(): {
    platform: string;
    isSupported: boolean;
    features: string[];
    limitations: string[];
  } {
    const currentPlatform = Platform.OS;
    const currentSDK = this.parseSDKVersion(this.getCurrentSDKVersion());
    
    const platformFeatures = Object.entries(this.FEATURE_SUPPORT)
      .filter(([_, feature]) => feature.platforms.includes(currentPlatform as any))
      .map(([name]) => name);

    const supportedFeatures = platformFeatures.filter(feature => 
      this.isFeatureSupported(feature)
    );

    const limitations: string[] = [];
    
    if (currentPlatform === 'ios') {
      if (currentSDK < 49) {
        limitations.push('Critical alerts not available');
      }
      if (currentSDK < 48) {
        limitations.push('Advanced notification categories not available');
      }
    } else if (currentPlatform === 'android') {
      if (currentSDK < 47) {
        limitations.push('Notification channels not available');
      }
    }

    return {
      platform: currentPlatform,
      isSupported: currentSDK >= this.MIN_SUPPORTED_SDK,
      features: supportedFeatures,
      limitations,
    };
  }

  /**
   * Get detailed compatibility report
   */
  getCompatibilityReport(): {
    summary: ExpoVersionInfo;
    platformInfo: any;
    recommendations: string[];
    criticalIssues: string[];
  } {
    const summary = this.checkVersionCompatibility();
    const platformInfo = this.getPlatformCompatibility();
    
    const recommendations: string[] = [];
    const criticalIssues: string[] = [];

    if (!summary.isCompatible) {
      criticalIssues.push('Expo SDK version is below minimum supported version');
      criticalIssues.push('App may not function correctly');
      recommendations.push('Immediate upgrade required');
    }

    if (summary.needsUpgrade) {
      recommendations.push('Upgrade to latest Expo SDK for best performance');
      recommendations.push('Review and test all features after upgrade');
    }

    if (summary.unsupportedFeatures.length > 0) {
      recommendations.push('Some features are not available on current SDK version');
      recommendations.push('Consider upgrading to access all features');
    }

    if (platformInfo.limitations.length > 0) {
      recommendations.push(`Platform-specific limitations: ${platformInfo.limitations.join(', ')}`);
    }

    if (recommendations.length === 0) {
      recommendations.push('Expo SDK configuration is optimal');
    }

    return {
      summary,
      platformInfo,
      recommendations,
      criticalIssues,
    };
  }

  /**
   * Log compatibility status to console
   */
  logCompatibilityStatus(): void {
    const report = this.getCompatibilityReport();
    
    console.log('🌊 Ocean Soul Sparkles - Expo SDK Compatibility Report');
    console.log('='.repeat(50));
    
    console.log(`📱 Platform: ${report.platformInfo.platform}`);
    console.log(`📦 Current SDK: ${report.summary.currentSDK}`);
    console.log(`🎯 Target SDK: ${report.summary.targetSDK}`);
    console.log(`✅ Compatible: ${report.summary.isCompatible ? 'Yes' : 'No'}`);
    
    if (report.criticalIssues.length > 0) {
      console.log('\n🚨 Critical Issues:');
      report.criticalIssues.forEach(issue => console.log(`  - ${issue}`));
    }
    
    if (report.recommendations.length > 0) {
      console.log('\n💡 Recommendations:');
      report.recommendations.forEach(rec => console.log(`  - ${rec}`));
    }
    
    console.log(`\n✨ Supported Features (${report.summary.supportedFeatures.length}):`);
    report.summary.supportedFeatures.forEach(feature => console.log(`  - ${feature}`));
    
    if (report.summary.unsupportedFeatures.length > 0) {
      console.log(`\n⚠️ Unsupported Features (${report.summary.unsupportedFeatures.length}):`);
      report.summary.unsupportedFeatures.forEach(feature => console.log(`  - ${feature}`));
    }
  }
}

// Export singleton instance
export const expoVersionChecker = new ExpoVersionChecker();
