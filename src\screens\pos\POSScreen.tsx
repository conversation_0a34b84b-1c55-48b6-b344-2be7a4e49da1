/**
 * Ocean Soul Sparkles Mobile App - POS Screen
 * Point of Sale interface for processing transactions
 */

import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  FlatList,
  TextInput,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  RefreshControl,
} from 'react-native';
import { Product, Service } from '@/types/database';
import { productService } from '@/services/database/productService';
import { usePOSStore, useCartItemCount, useCartTotal } from '@/store/posStore';
import ProductCard from '@/components/pos/ProductCard';
import CartSummary from '@/components/pos/CartSummary';
import ReceiptModal from '@/components/pos/ReceiptModal';
import { transactionService } from '@/services/database/transactionService';

const POSScreen: React.FC = () => {
  const [products, setProducts] = useState<Product[]>([]);
  const [services, setServices] = useState<Service[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [activeTab, setActiveTab] = useState<'products' | 'services' | 'all'>('all');
  const [showReceipt, setShowReceipt] = useState(false);
  const [lastTransaction, setLastTransaction] = useState<any>(null);
  const isMountedRef = useRef(true);

  const { searchQuery, setSearchQuery, selectedCategory, setSelectedCategory } = usePOSStore();
  const cartItemCount = useCartItemCount();
  const cartTotal = useCartTotal();

  // Load products and services
  const loadData = async (isRefresh = false) => {
    try {
      if (!isMountedRef.current) return;

      if (isRefresh) {
        setRefreshing(true);
      } else {
        setLoading(true);
      }

      const [productsResult, servicesResult] = await Promise.all([
        productService.getProducts({ search: searchQuery }),
        productService.getServices({ search: searchQuery })
      ]);

      if (!isMountedRef.current) return;

      if (productsResult.error) {
        console.error('Error loading products:', productsResult.error);
        if (isMountedRef.current) {
          Alert.alert('Error', 'Failed to load products');
        }
      } else {
        setProducts(productsResult.data || []);
      }

      if (servicesResult.error) {
        console.error('Error loading services:', servicesResult.error);
        if (isMountedRef.current) {
          Alert.alert('Error', 'Failed to load services');
        }
      } else {
        setServices(servicesResult.data || []);
      }
    } catch (error) {
      console.error('Error loading POS data:', error);
      if (isMountedRef.current) {
        Alert.alert('Error', 'Failed to load data');
      }
    } finally {
      if (isMountedRef.current) {
        setLoading(false);
        setRefreshing(false);
      }
    }
  };

  useEffect(() => {
    isMountedRef.current = true;
    loadData();

    return () => {
      isMountedRef.current = false;
    };
  }, []);

  useEffect(() => {
    // Debounce search
    const timeoutId = setTimeout(() => {
      if (isMountedRef.current) {
        loadData();
      }
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [searchQuery]);

  // Get filtered items based on active tab
  const getFilteredItems = () => {
    let items: Array<{ item: Product | Service; type: 'product' | 'service' }> = [];

    if (activeTab === 'products' || activeTab === 'all') {
      items.push(...products.map(product => ({ item: product, type: 'product' as const })));
    }

    if (activeTab === 'services' || activeTab === 'all') {
      items.push(...services.map(service => ({ item: service, type: 'service' as const })));
    }

    // Filter by category if selected
    if (selectedCategory) {
      items = items.filter(({ item }) =>
        item.category?.toLowerCase() === selectedCategory.toLowerCase()
      );
    }

    return items;
  };

  // Get unique categories
  const getCategories = () => {
    const allItems = [...products, ...services];
    const categories = [...new Set(allItems.map(item => item.category).filter(Boolean))];
    return categories;
  };

  const handleTransactionComplete = async (transactionId: string) => {
    try {
      // Fetch the completed transaction for receipt
      const result = await transactionService.getTransactionById(transactionId);
      if (result.data) {
        const receipt = transactionService.generateReceipt(result.data);
        setLastTransaction(receipt);
        setShowReceipt(true);
      }
    } catch (error) {
      console.error('Error fetching transaction for receipt:', error);
    }
  };

  const filteredItems = getFilteredItems();
  const categories = getCategories();

  if (loading && !refreshing) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#FF9A8B" />
          <Text style={styles.loadingText}>Loading POS...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.title}>💳 POS System</Text>
        <View style={styles.cartInfo}>
          <Text style={styles.cartText}>
            {cartItemCount} items • ${cartTotal.toFixed(2)}
          </Text>
        </View>
      </View>

      {/* Search Bar */}
      <View style={styles.searchContainer}>
        <TextInput
          style={styles.searchInput}
          placeholder="Search products and services..."
          value={searchQuery}
          onChangeText={setSearchQuery}
          clearButtonMode="while-editing"
        />
      </View>

      {/* Tab Navigation */}
      <View style={styles.tabContainer}>
        <TouchableOpacity
          style={[styles.tab, activeTab === 'all' && styles.activeTab]}
          onPress={() => setActiveTab('all')}
        >
          <Text style={[styles.tabText, activeTab === 'all' && styles.activeTabText]}>
            All ({products.length + services.length})
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.tab, activeTab === 'products' && styles.activeTab]}
          onPress={() => setActiveTab('products')}
        >
          <Text style={[styles.tabText, activeTab === 'products' && styles.activeTabText]}>
            Products ({products.length})
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.tab, activeTab === 'services' && styles.activeTab]}
          onPress={() => setActiveTab('services')}
        >
          <Text style={[styles.tabText, activeTab === 'services' && styles.activeTabText]}>
            Services ({services.length})
          </Text>
        </TouchableOpacity>
      </View>

      {/* Category Filter */}
      {categories.length > 0 && (
        <View style={styles.categoryContainer}>
          <TouchableOpacity
            style={[styles.categoryChip, !selectedCategory && styles.activeCategoryChip]}
            onPress={() => setSelectedCategory(null)}
          >
            <Text style={[styles.categoryText, !selectedCategory && styles.activeCategoryText]}>
              All
            </Text>
          </TouchableOpacity>
          {categories.map((category) => (
            <TouchableOpacity
              key={category}
              style={[styles.categoryChip, selectedCategory === category && styles.activeCategoryChip]}
              onPress={() => setSelectedCategory(selectedCategory === category ? null : category)}
            >
              <Text style={[styles.categoryText, selectedCategory === category && styles.activeCategoryText]}>
                {category}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      )}

      {/* Main Content */}
      <View style={styles.mainContent}>
        {/* Products/Services Grid */}
        <View style={styles.productsContainer}>
          {filteredItems.length === 0 ? (
            <View style={styles.emptyState}>
              <Text style={styles.emptyStateIcon}>🔍</Text>
              <Text style={styles.emptyStateText}>
                {searchQuery ? 'No items found' : 'No items available'}
              </Text>
              {searchQuery && (
                <TouchableOpacity onPress={() => setSearchQuery('')}>
                  <Text style={styles.clearSearchText}>Clear search</Text>
                </TouchableOpacity>
              )}
            </View>
          ) : (
            <FlatList
              data={filteredItems}
              renderItem={({ item }) => (
                <ProductCard item={item.item} type={item.type} />
              )}
              keyExtractor={(item) => `${item.type}-${item.item.id}`}
              numColumns={2}
              showsVerticalScrollIndicator={false}
              refreshControl={
                <RefreshControl
                  refreshing={refreshing}
                  onRefresh={() => loadData(true)}
                  colors={['#FF9A8B']}
                />
              }
              contentContainerStyle={styles.gridContainer}
            />
          )}
        </View>

        {/* Cart Summary */}
        <View style={styles.cartContainer}>
          <CartSummary onTransactionComplete={handleTransactionComplete} />
        </View>
      </View>

      {/* Receipt Modal */}
      <ReceiptModal
        visible={showReceipt}
        onClose={() => setShowReceipt(false)}
        receipt={lastTransaction}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#666',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#FF9A8B',
  },
  cartInfo: {
    backgroundColor: '#FF9A8B',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  cartText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
  },
  searchContainer: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#fff',
  },
  searchInput: {
    backgroundColor: '#f8f9fa',
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    borderWidth: 1,
    borderColor: '#e9ecef',
  },
  tabContainer: {
    flexDirection: 'row',
    backgroundColor: '#fff',
    paddingHorizontal: 16,
    paddingBottom: 12,
  },
  tab: {
    flex: 1,
    paddingVertical: 8,
    paddingHorizontal: 12,
    marginHorizontal: 4,
    borderRadius: 6,
    backgroundColor: '#f8f9fa',
    alignItems: 'center',
  },
  activeTab: {
    backgroundColor: '#FF9A8B',
  },
  tabText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#666',
  },
  activeTabText: {
    color: '#fff',
  },
  categoryContainer: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  categoryChip: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    marginRight: 8,
    borderRadius: 16,
    backgroundColor: '#f8f9fa',
    borderWidth: 1,
    borderColor: '#e9ecef',
  },
  activeCategoryChip: {
    backgroundColor: '#FF9A8B',
    borderColor: '#FF9A8B',
  },
  categoryText: {
    fontSize: 12,
    fontWeight: '500',
    color: '#666',
  },
  activeCategoryText: {
    color: '#fff',
  },
  mainContent: {
    flex: 1,
    flexDirection: 'row',
  },
  productsContainer: {
    flex: 2,
    backgroundColor: '#f5f5f5',
  },
  cartContainer: {
    flex: 1,
    backgroundColor: '#fff',
    borderLeftWidth: 1,
    borderLeftColor: '#e0e0e0',
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
  },
  emptyStateIcon: {
    fontSize: 48,
    marginBottom: 16,
  },
  emptyStateText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginBottom: 8,
  },
  clearSearchText: {
    fontSize: 14,
    color: '#FF9A8B',
    fontWeight: '500',
  },
  gridContainer: {
    padding: 8,
  },
});

export default POSScreen;
