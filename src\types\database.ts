/**
 * Ocean Soul Sparkles Mobile App - Database Types
 * Shared types with admin portal for Supabase integration
 */

// Core database types (matching admin portal admin_users table)
export interface AdminUser {
  id: string;
  email: string;
  first_name: string;
  last_name: string;
  role: 'DEV' | 'Admin' | 'Artist' | 'Braider';
  is_active: boolean;
  mfa_enabled: boolean;
  mfa_secret?: string;
  last_activity: string;
  created_at: string;
  updated_at: string;
  permissions?: string[];
  tenant_id?: string;
}

// Legacy Staff interface for backward compatibility
export interface Staff {
  id: string;
  email: string;
  full_name: string;
  role: 'admin' | 'manager' | 'staff' | 'artist';
  phone?: string;
  avatar_url?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface Product {
  id: string;
  name: string;
  description?: string;
  price: number;
  category: string;
  sku?: string;
  stock_quantity?: number;
  is_active: boolean;
  image_url?: string;
  created_at: string;
  updated_at: string;
}

export interface Service {
  id: string;
  name: string;
  description?: string;
  base_price: number;
  duration_minutes?: number;
  category: string;
  is_active: boolean;
  image_url?: string;
  created_at: string;
  updated_at: string;
}

export interface Booking {
  id: string;
  customer_id: string;
  staff_id?: string;
  service_id?: string;
  booking_date: string;
  start_time: string;
  end_time?: string;
  status: 'pending' | 'confirmed' | 'in_progress' | 'completed' | 'cancelled';
  total_amount?: number;
  notes?: string;
  created_at: string;
  updated_at: string;
  
  // Relations
  customer?: Customer;
  staff?: Staff;
  service?: Service;
}

export interface Customer {
  id: string;
  email: string;
  name: string; // Primary name field in database
  first_name?: string; // Separate first name field
  last_name?: string; // Separate last name field
  phone?: string;
  address?: string;
  city?: string;
  state?: string;
  postal_code?: string;
  notes?: string;
  created_at: string;
  updated_at: string;

  // Additional fields from database schema
  country?: string;
  marketing_consent?: boolean;
  auth_id?: string;
  is_guest?: boolean;
  birth_date?: string;
  occupation?: string;
  referral_source?: string;
  customer_since?: string;
  lifetime_value?: number;
  last_booking_date?: string;
  booking_count?: number;
  vip?: boolean;
  profile_image_url?: string;

  // Computed property for backward compatibility
  full_name?: string; // Can be computed from first_name + last_name or use name
}

export interface Quote {
  id: string;
  customer_id: string;
  customer_first_name?: string;
  customer_last_name?: string;
  customer_email?: string;
  customer_phone?: string;
  service_name: string;
  service_description?: string;
  event_type?: string;
  event_date?: string;
  event_time?: string;
  event_duration?: number;
  number_of_people?: number;
  location_type?: string;
  event_address?: string;
  travel_distance?: number;
  base_price?: number;
  travel_cost?: number;
  additional_costs?: number;
  estimated_total: number; // Correct database property name
  finalized_total?: number;
  status: 'draft' | 'sent' | 'accepted' | 'declined' | 'expired';
  priority?: string;
  notes?: string;
  internal_notes?: string;
  special_requirements?: string;
  source?: string;
  original_booking_id?: string;
  created_by?: string;
  updated_by?: string;
  finalized_by?: string;
  finalized_at?: string;
  sent_at?: string;
  expires_at?: string;
  created_at: string;
  updated_at: string;

  // Relations
  customer?: Customer;
  quote_items?: QuoteItem[];
  booking?: Booking;
}

export interface QuoteItem {
  id: string;
  quote_id: string;
  item_type: 'product' | 'service';
  item_id: string;
  quantity: number;
  unit_price: number;
  total_price: number;
  description?: string;
  created_at: string;
}

export interface Invoice {
  id: string;
  customer_id: string;
  booking_id?: string;
  quote_id?: string;
  invoice_number: string;
  total_amount: number;
  status: 'draft' | 'sent' | 'paid' | 'overdue' | 'cancelled';
  due_date?: string;
  paid_date?: string;
  notes?: string;
  created_at: string;
  updated_at: string;
  
  // Relations
  customer?: Customer;
  booking?: Booking;
  quote?: Quote;
}

// POS-specific types
export interface CartItem {
  id: string;
  type: 'product' | 'service';
  item_id: string;
  item: Product | Service;
  name: string;
  quantity: number;
  unit_price: number;
  total_price: number;
  notes?: string;
}

export interface Transaction {
  id: string;
  transaction_number: string;
  customer_id?: string;
  staff_id: string;
  subtotal: number;
  tax_amount: number;
  total_amount: number;
  payment_method: 'cash' | 'card' | 'digital_wallet' | 'pending';
  payment_status: 'pending' | 'completed' | 'failed' | 'refunded';
  square_payment_id?: string;
  receipt_url?: string;
  receipt_email?: string;
  notes?: string;
  created_at: string;
  updated_at: string;

  // Relations
  customer?: Customer;
  staff?: AdminUser;
  transaction_items?: TransactionItem[];
}

export interface TransactionItem {
  id: string;
  transaction_id: string;
  item_type: 'product' | 'service';
  item_id: string;
  item_name: string;
  quantity: number;
  unit_price: number;
  total_price: number;
  notes?: string;
  created_at: string;

  // Relations
  product?: Product;
  service?: Service;
}

export interface Receipt {
  transaction: Transaction;
  business_info: {
    name: string;
    address: string;
    phone: string;
    email: string;
    website?: string;
    abn?: string;
  };
  generated_at: string;
  receipt_number: string;
}

// Email system types
export interface EmailTemplate {
  id: string;
  name: string;
  subject: string;
  html_content: string;
  text_content?: string;
  template_type: 'quote' | 'booking_confirmation' | 'invoice' | 'receipt' | 'reminder';
  variables: string[]; // Array of variable names like ['customer_name', 'estimated_total']
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

// Database response types
export type DatabaseResponse<T> = {
  data: T | null;
  error: Error | null;
};

export type DatabaseListResponse<T> = {
  data: T[] | null;
  error: Error | null;
  count?: number;
};

// Query filters
export interface QueryFilters {
  limit?: number;
  offset?: number;
  order_by?: string;
  order_direction?: 'asc' | 'desc';
  search?: string;
  filters?: Record<string, any>;
}
