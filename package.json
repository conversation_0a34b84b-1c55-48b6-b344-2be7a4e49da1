{"name": "oceansoulsparkles-app", "version": "1.0.0", "description": "Ocean Soul Sparkles Mobile App - Cross-platform POS and business management", "main": "node_modules/expo/AppEntry.js", "private": true, "scripts": {"start": "expo start", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "build": "expo export --platform web --output-dir dist", "build:web": "expo export --platform web", "build:vercel": "expo export --platform web --output-dir dist", "preview": "expo start --web", "deploy": "npm run build:vercel && vercel --prod", "metro": "npx react-native start --port 8081 --reset-cache", "metro:reset": "npx react-native start --reset-cache --port 8081", "android:metro": "npx react-native run-android --port 8081", "android:studio": "npm run metro", "dev:android": "concurrently \"npm run metro\" \"npm run android:metro\"", "debug:android": "npx react-native run-android --variant=debug --port 8081", "setup:android": "node scripts/android-studio-setup.js", "clean:metro": "npx react-native start --reset-cache --port 8081", "logs:android": "npx react-native log-android"}, "dependencies": {"@expo/metro-runtime": "~3.1.3", "@react-native-async-storage/async-storage": "1.21.0", "@react-navigation/bottom-tabs": "^6.6.1", "@react-navigation/native": "^6.1.18", "@react-navigation/native-stack": "^6.11.0", "@supabase/supabase-js": "^2.38.5", "@tanstack/react-query": "^5.17.9", "expo": "~50.0.0", "expo-constants": "~15.4.5", "expo-dev-client": "~3.3.12", "expo-device": "~5.9.3", "expo-linking": "~6.2.2", "expo-notifications": "~0.27.6", "expo-splash-screen": "~0.26.4", "expo-status-bar": "~1.11.1", "expo-system-ui": "~2.9.3", "expo-web-browser": "~12.8.2", "react": "18.2.0", "react-dom": "18.2.0", "react-native": "0.73.6", "react-native-gesture-handler": "~2.14.0", "react-native-reanimated": "~3.6.2", "react-native-safe-area-context": "4.8.2", "react-native-screens": "~3.29.0", "react-native-square-in-app-payments": "^1.7.6", "react-native-web": "~0.19.6", "zustand": "^4.4.7"}, "devDependencies": {"@babel/core": "^7.20.0", "@expo/cli": "^0.16.0", "@expo/metro-config": "^0.17.8", "@react-native-community/cli": "^19.1.1", "@types/jest": "^29.5.8", "@types/react": "~18.2.45", "@types/react-test-renderer": "^18.0.7", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "babel-plugin-module-resolver": "^5.0.2", "concurrently": "^9.2.0", "dotenv": "^16.3.1", "eslint": "^8.54.0", "eslint-config-expo": "^7.0.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "jest": "^29.7.0", "jest-expo": "~50.0.1", "react-test-renderer": "18.2.0", "typescript": "^5.3.0"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "overrides": {"expo-modules-autolinking": "~1.10.0", "@expo/config-plugins": "^7.8.0"}, "keywords": ["ocean-soul-sparkles", "mobile-app", "pos-system", "react-native", "expo", "cross-platform", "business-management"], "author": "Ocean Soul Sparkles", "license": "UNLICENSED"}