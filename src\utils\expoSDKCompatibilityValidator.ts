/**
 * Ocean Soul Sparkles Mobile App - Expo SDK Compatibility Validator
 * Validates Expo SDK compatibility and configuration for both iOS and Android
 */

import { Platform } from 'react-native';
import Constants from 'expo-constants';

interface CompatibilityTestResult {
  testName: string;
  success: boolean;
  message: string;
  details?: any;
  platform?: 'ios' | 'android' | 'both';
}

interface ExpoSDKInfo {
  expoVersion: string;
  sdkVersion: string;
  platform: string;
  isDevice: boolean;
  isExpoGo: boolean;
  appOwnership: string;
}

class ExpoSDKCompatibilityValidator {
  
  /**
   * Run comprehensive Expo SDK compatibility tests
   */
  async runAllCompatibilityTests(): Promise<CompatibilityTestResult[]> {
    const results: CompatibilityTestResult[] = [];

    // Test 1: Basic Expo SDK info
    results.push(await this.testExpoSDKInfo());

    // Test 2: Required Expo modules
    results.push(await this.testRequiredExpoModules());

    // Test 3: Platform-specific features
    results.push(await this.testPlatformSpecificFeatures());

    // Test 4: Permissions compatibility
    results.push(await this.testPermissionsCompatibility());

    // Test 5: Configuration validation
    results.push(await this.testConfigurationValidation());

    // Test 6: Plugin compatibility
    results.push(await this.testPluginCompatibility());

    return results;
  }

  /**
   * Test basic Expo SDK information
   */
  private async testExpoSDKInfo(): Promise<CompatibilityTestResult> {
    try {
      const sdkInfo = this.getExpoSDKInfo();
      
      const isCompatibleSDK = this.isSDKVersionCompatible(sdkInfo.sdkVersion);
      const hasValidConfig = !!Constants.expoConfig;

      return {
        testName: 'Expo SDK Information',
        success: isCompatibleSDK && hasValidConfig,
        message: isCompatibleSDK && hasValidConfig
          ? `Expo SDK ${sdkInfo.sdkVersion} is compatible`
          : `Expo SDK compatibility issues detected`,
        details: sdkInfo,
        platform: 'both',
      };
    } catch (error) {
      return {
        testName: 'Expo SDK Information',
        success: false,
        message: `Failed to get Expo SDK info: ${error}`,
        platform: 'both',
      };
    }
  }

  /**
   * Test required Expo modules
   */
  private async testRequiredExpoModules(): Promise<CompatibilityTestResult> {
    const requiredModules = [
      { name: 'expo-constants', test: () => import('expo-constants') },
      { name: 'expo-notifications', test: () => import('expo-notifications') },
      { name: 'expo-device', test: () => import('expo-device') },
      { name: 'expo-splash-screen', test: () => import('expo-splash-screen') },
      { name: 'expo-status-bar', test: () => import('expo-status-bar') },
    ];

    const moduleResults: any[] = [];
    
    for (const module of requiredModules) {
      try {
        await module.test();
        moduleResults.push({ name: module.name, available: true });
      } catch (error) {
        moduleResults.push({ 
          name: module.name, 
          available: false, 
          error: error.toString() 
        });
      }
    }

    const availableModules = moduleResults.filter(m => m.available);
    const missingModules = moduleResults.filter(m => !m.available);

    return {
      testName: 'Required Expo Modules',
      success: missingModules.length === 0,
      message: missingModules.length === 0
        ? `All ${requiredModules.length} required modules are available`
        : `${missingModules.length} modules are missing or incompatible`,
      details: { available: availableModules, missing: missingModules },
      platform: 'both',
    };
  }

  /**
   * Test platform-specific features
   */
  private async testPlatformSpecificFeatures(): Promise<CompatibilityTestResult> {
    const platformTests: any[] = [];

    if (Platform.OS === 'ios') {
      // Test iOS-specific features
      try {
        const Notifications = await import('expo-notifications');
        
        // Test iOS notification categories
        const categoryTest = await this.testIOSNotificationCategories(Notifications);
        platformTests.push(categoryTest);

        // Test iOS permissions
        const permissionTest = await this.testIOSPermissions(Notifications);
        platformTests.push(permissionTest);

      } catch (error) {
        platformTests.push({
          feature: 'iOS Notifications',
          success: false,
          error: error.toString(),
        });
      }
    } else if (Platform.OS === 'android') {
      // Test Android-specific features
      try {
        const Notifications = await import('expo-notifications');
        
        // Test Android notification channels
        const channelTest = await this.testAndroidNotificationChannels(Notifications);
        platformTests.push(channelTest);

        // Test Android permissions
        const permissionTest = await this.testAndroidPermissions(Notifications);
        platformTests.push(permissionTest);

      } catch (error) {
        platformTests.push({
          feature: 'Android Notifications',
          success: false,
          error: error.toString(),
        });
      }
    }

    const successfulTests = platformTests.filter(t => t.success);

    return {
      testName: 'Platform-Specific Features',
      success: platformTests.length > 0 && successfulTests.length === platformTests.length,
      message: `${successfulTests.length}/${platformTests.length} platform features working`,
      details: platformTests,
      platform: Platform.OS as 'ios' | 'android',
    };
  }

  /**
   * Test permissions compatibility
   */
  private async testPermissionsCompatibility(): Promise<CompatibilityTestResult> {
    try {
      const Notifications = await import('expo-notifications');
      
      const { status } = await Notifications.getPermissionsAsync();
      
      return {
        testName: 'Permissions Compatibility',
        success: true,
        message: `Permission status: ${status}`,
        details: { status },
        platform: 'both',
      };
    } catch (error) {
      return {
        testName: 'Permissions Compatibility',
        success: false,
        message: `Permission check failed: ${error}`,
        platform: 'both',
      };
    }
  }

  /**
   * Test configuration validation
   */
  private async testConfigurationValidation(): Promise<CompatibilityTestResult> {
    try {
      const config = Constants.expoConfig;
      const issues: string[] = [];

      if (!config) {
        issues.push('No Expo config found');
      } else {
        // Check required config fields
        if (!config.name) issues.push('Missing app name');
        if (!config.slug) issues.push('Missing app slug');
        if (!config.version) issues.push('Missing app version');

        // Check platform-specific config
        if (Platform.OS === 'ios' && !config.ios?.bundleIdentifier) {
          issues.push('Missing iOS bundle identifier');
        }
        if (Platform.OS === 'android' && !config.android?.package) {
          issues.push('Missing Android package name');
        }

        // Check plugins
        if (!config.plugins || !Array.isArray(config.plugins)) {
          issues.push('No plugins configured');
        }
      }

      return {
        testName: 'Configuration Validation',
        success: issues.length === 0,
        message: issues.length === 0
          ? 'Configuration is valid'
          : `Configuration issues: ${issues.join(', ')}`,
        details: { config, issues },
        platform: 'both',
      };
    } catch (error) {
      return {
        testName: 'Configuration Validation',
        success: false,
        message: `Configuration validation failed: ${error}`,
        platform: 'both',
      };
    }
  }

  /**
   * Test plugin compatibility
   */
  private async testPluginCompatibility(): Promise<CompatibilityTestResult> {
    try {
      const config = Constants.expoConfig;
      const plugins = config?.plugins || [];
      
      const expectedPlugins = [
        'expo-splash-screen',
        'expo-notifications',
      ];

      const configuredPlugins = plugins.map((plugin: any) => 
        typeof plugin === 'string' ? plugin : plugin[0]
      );

      const missingPlugins = expectedPlugins.filter(
        plugin => !configuredPlugins.includes(plugin)
      );

      return {
        testName: 'Plugin Compatibility',
        success: missingPlugins.length === 0,
        message: missingPlugins.length === 0
          ? `All ${expectedPlugins.length} required plugins configured`
          : `Missing plugins: ${missingPlugins.join(', ')}`,
        details: { configured: configuredPlugins, missing: missingPlugins },
        platform: 'both',
      };
    } catch (error) {
      return {
        testName: 'Plugin Compatibility',
        success: false,
        message: `Plugin compatibility check failed: ${error}`,
        platform: 'both',
      };
    }
  }

  /**
   * Test iOS notification categories
   */
  private async testIOSNotificationCategories(Notifications: any): Promise<any> {
    try {
      // This would test if iOS notification categories can be set
      return {
        feature: 'iOS Notification Categories',
        success: true,
        message: 'iOS notification categories supported',
      };
    } catch (error) {
      return {
        feature: 'iOS Notification Categories',
        success: false,
        error: error.toString(),
      };
    }
  }

  /**
   * Test iOS permissions
   */
  private async testIOSPermissions(Notifications: any): Promise<any> {
    try {
      const { status } = await Notifications.getPermissionsAsync();
      return {
        feature: 'iOS Permissions',
        success: true,
        message: `iOS permissions accessible, status: ${status}`,
      };
    } catch (error) {
      return {
        feature: 'iOS Permissions',
        success: false,
        error: error.toString(),
      };
    }
  }

  /**
   * Test Android notification channels
   */
  private async testAndroidNotificationChannels(Notifications: any): Promise<any> {
    try {
      // This would test if Android notification channels can be created
      return {
        feature: 'Android Notification Channels',
        success: true,
        message: 'Android notification channels supported',
      };
    } catch (error) {
      return {
        feature: 'Android Notification Channels',
        success: false,
        error: error.toString(),
      };
    }
  }

  /**
   * Test Android permissions
   */
  private async testAndroidPermissions(Notifications: any): Promise<any> {
    try {
      const { status } = await Notifications.getPermissionsAsync();
      return {
        feature: 'Android Permissions',
        success: true,
        message: `Android permissions accessible, status: ${status}`,
      };
    } catch (error) {
      return {
        feature: 'Android Permissions',
        success: false,
        error: error.toString(),
      };
    }
  }

  /**
   * Get Expo SDK information
   */
  getExpoSDKInfo(): ExpoSDKInfo {
    return {
      expoVersion: Constants.expoVersion || 'unknown',
      sdkVersion: Constants.expoConfig?.sdkVersion || Constants.sdkVersion || 'unknown',
      platform: Platform.OS,
      isDevice: Constants.isDevice,
      isExpoGo: Constants.appOwnership === 'expo',
      appOwnership: Constants.appOwnership || 'unknown',
    };
  }

  /**
   * Check if SDK version is compatible
   */
  private isSDKVersionCompatible(sdkVersion: string): boolean {
    // Expo SDK 50+ is what we're targeting
    const majorVersion = parseInt(sdkVersion.split('.')[0] || '0');
    return majorVersion >= 50;
  }

  /**
   * Get compatibility summary
   */
  async getCompatibilitySummary(): Promise<{
    isCompatible: boolean;
    sdkInfo: ExpoSDKInfo;
    issues: string[];
    recommendations: string[];
  }> {
    const results = await this.runAllCompatibilityTests();
    const failedTests = results.filter(r => !r.success);
    const sdkInfo = this.getExpoSDKInfo();

    const issues = failedTests.map(test => `${test.testName}: ${test.message}`);
    const recommendations: string[] = [];

    if (failedTests.length > 0) {
      recommendations.push('Run npm install to ensure all dependencies are installed');
      recommendations.push('Check that all required Expo modules are properly configured');
      
      if (failedTests.some(t => t.testName.includes('Module'))) {
        recommendations.push('Add missing Expo modules to package.json dependencies');
      }
      
      if (failedTests.some(t => t.testName.includes('Configuration'))) {
        recommendations.push('Review app.config.js for missing required fields');
      }
    }

    if (recommendations.length === 0) {
      recommendations.push('All Expo SDK compatibility tests passed');
    }

    return {
      isCompatible: failedTests.length === 0,
      sdkInfo,
      issues,
      recommendations,
    };
  }
}

// Export singleton instance
export const expoSDKCompatibilityValidator = new ExpoSDKCompatibilityValidator();
