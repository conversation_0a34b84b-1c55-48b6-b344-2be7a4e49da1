/**
 * Ocean Soul Sparkles Mobile App - Cart Summary Component
 * Displays cart items and total with quantity controls
 */

import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Alert,
} from 'react-native';
import { usePOSStore, useCartItems, useCartTotal, useCartItemCount } from '@/store/posStore';
import CheckoutModal from './CheckoutModal';

interface CartSummaryProps {
  onTransactionComplete?: (transactionId: string) => void;
}

const CartSummary: React.FC<CartSummaryProps> = ({ onTransactionComplete }) => {
  const [showCheckout, setShowCheckout] = useState(false);
  const cartItems = useCartItems();
  const cartTotal = useCartTotal();
  const cartItemCount = useCartItemCount();
  const { updateCartItemQuantity, removeFromCart, clearCart } = usePOSStore();

  // Safety check to ensure store is initialized
  if (cartTotal === undefined || cartTotal === null) {
    return (
      <View style={styles.emptyCart}>
        <Text style={styles.emptyCartIcon}>🛒</Text>
        <Text style={styles.emptyCartText}>Loading cart...</Text>
      </View>
    );
  }

  const handleQuantityChange = (cartItemId: string, newQuantity: number) => {
    if (newQuantity <= 0) {
      Alert.alert(
        'Remove Item',
        'Remove this item from cart?',
        [
          { text: 'Cancel', style: 'cancel' },
          { text: 'Remove', style: 'destructive', onPress: () => removeFromCart(cartItemId) }
        ]
      );
    } else {
      updateCartItemQuantity(cartItemId, newQuantity);
    }
  };

  const handleClearCart = () => {
    Alert.alert(
      'Clear Cart',
      'Remove all items from cart?',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Clear All', style: 'destructive', onPress: clearCart }
      ]
    );
  };

  const handleTransactionComplete = (transactionId: string) => {
    // Close checkout modal
    setShowCheckout(false);
    // Call parent callback if provided
    onTransactionComplete?.(transactionId);
  };

  const formatPrice = (price: number | undefined | null) => {
    if (price === undefined || price === null || isNaN(price)) {
      return '$0.00';
    }
    return `$${price.toFixed(2)}`;
  };

  const getItemIcon = (type: 'product' | 'service', category?: string) => {
    if (type === 'service') {
      return '✂️';
    }
    
    const cat = category?.toLowerCase() || '';
    if (cat.includes('hair')) return '💇‍♀️';
    if (cat.includes('braid')) return '🎀';
    if (cat.includes('care')) return '🧴';
    if (cat.includes('tool')) return '🔧';
    if (cat.includes('accessory')) return '💎';
    return '📦';
  };

  if (cartItems.length === 0) {
    return (
      <View style={styles.emptyCart}>
        <Text style={styles.emptyCartIcon}>🛒</Text>
        <Text style={styles.emptyCartText}>Your cart is empty</Text>
        <Text style={styles.emptyCartSubtext}>Add products or services to get started</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Cart ({cartItemCount} items)</Text>
        <TouchableOpacity onPress={handleClearCart} style={styles.clearButton}>
          <Text style={styles.clearButtonText}>Clear All</Text>
        </TouchableOpacity>
      </View>

      {/* Cart Items */}
      <ScrollView style={styles.itemsList} showsVerticalScrollIndicator={false}>
        {cartItems.map((cartItem) => (
          <View key={cartItem.id} style={styles.cartItem}>
            <View style={styles.itemInfo}>
              <View style={styles.itemHeader}>
                <Text style={styles.itemIcon}>
                  {getItemIcon(cartItem.type, cartItem.item.category)}
                </Text>
                <View style={styles.itemDetails}>
                  <Text style={styles.itemName} numberOfLines={1}>
                    {cartItem.item.name}
                  </Text>
                  <Text style={styles.itemType}>
                    {cartItem.type === 'service' ? 'Service' : 'Product'}
                  </Text>
                </View>
                <TouchableOpacity
                  onPress={() => removeFromCart(cartItem.id)}
                  style={styles.removeButton}
                >
                  <Text style={styles.removeButtonText}>✕</Text>
                </TouchableOpacity>
              </View>

              <View style={styles.itemFooter}>
                <View style={styles.quantityControls}>
                  <TouchableOpacity
                    onPress={() => handleQuantityChange(cartItem.id, cartItem.quantity - 1)}
                    style={styles.quantityButton}
                  >
                    <Text style={styles.quantityButtonText}>−</Text>
                  </TouchableOpacity>
                  
                  <Text style={styles.quantity}>{cartItem.quantity}</Text>
                  
                  <TouchableOpacity
                    onPress={() => handleQuantityChange(cartItem.id, cartItem.quantity + 1)}
                    style={styles.quantityButton}
                  >
                    <Text style={styles.quantityButtonText}>+</Text>
                  </TouchableOpacity>
                </View>

                <View style={styles.priceInfo}>
                  <Text style={styles.unitPrice}>
                    {formatPrice(cartItem.unit_price)} each
                  </Text>
                  <Text style={styles.totalPrice}>
                    {formatPrice(cartItem.total_price)}
                  </Text>
                </View>
              </View>
            </View>
          </View>
        ))}
      </ScrollView>

      {/* Total */}
      <View style={styles.totalSection}>
        <View style={styles.totalRow}>
          <Text style={styles.totalLabel}>Subtotal</Text>
          <Text style={styles.totalAmount}>{formatPrice(cartTotal)}</Text>
        </View>
        
        {/* Placeholder for tax calculation */}
        <View style={styles.totalRow}>
          <Text style={styles.totalLabel}>Tax (10% GST)</Text>
          <Text style={styles.totalAmount}>{formatPrice(cartTotal * 0.1)}</Text>
        </View>
        
        <View style={[styles.totalRow, styles.grandTotalRow]}>
          <Text style={styles.grandTotalLabel}>Total</Text>
          <Text style={styles.grandTotalAmount}>{formatPrice(cartTotal * 1.1)}</Text>
        </View>
      </View>

      {/* Checkout Button */}
      <TouchableOpacity
        style={styles.checkoutButton}
        onPress={() => setShowCheckout(true)}
      >
        <Text style={styles.checkoutButtonText}>
          Proceed to Checkout • {formatPrice((cartTotal || 0) * 1.1)}
        </Text>
      </TouchableOpacity>

      {/* Checkout Modal */}
      <CheckoutModal
        visible={showCheckout}
        onClose={() => setShowCheckout(false)}
        cartItems={cartItems}
        cartTotal={cartTotal}
        onTransactionComplete={handleTransactionComplete}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  emptyCart: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  emptyCartIcon: {
    fontSize: 64,
    marginBottom: 16,
  },
  emptyCartText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
  },
  emptyCartSubtext: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
  },
  clearButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
    backgroundColor: '#f8f9fa',
  },
  clearButtonText: {
    fontSize: 14,
    color: '#666',
  },
  itemsList: {
    flex: 1,
  },
  cartItem: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  itemInfo: {
    flex: 1,
  },
  itemHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  itemIcon: {
    fontSize: 24,
    marginRight: 12,
  },
  itemDetails: {
    flex: 1,
  },
  itemName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 2,
  },
  itemType: {
    fontSize: 12,
    color: '#666',
    textTransform: 'uppercase',
  },
  removeButton: {
    padding: 4,
  },
  removeButtonText: {
    fontSize: 16,
    color: '#999',
  },
  itemFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  quantityControls: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f8f9fa',
    borderRadius: 8,
    padding: 4,
  },
  quantityButton: {
    width: 32,
    height: 32,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#fff',
    borderRadius: 6,
  },
  quantityButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
  },
  quantity: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginHorizontal: 16,
    minWidth: 24,
    textAlign: 'center',
  },
  priceInfo: {
    alignItems: 'flex-end',
  },
  unitPrice: {
    fontSize: 12,
    color: '#666',
    marginBottom: 2,
  },
  totalPrice: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FF9A8B',
  },
  totalSection: {
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
    backgroundColor: '#f8f9fa',
  },
  totalRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  grandTotalRow: {
    marginTop: 8,
    paddingTop: 8,
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
    marginBottom: 0,
  },
  totalLabel: {
    fontSize: 14,
    color: '#666',
  },
  totalAmount: {
    fontSize: 14,
    color: '#333',
  },
  grandTotalLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
  },
  grandTotalAmount: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FF9A8B',
  },
  checkoutButton: {
    backgroundColor: '#FF9A8B',
    margin: 16,
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
  },
  checkoutButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default CartSummary;
