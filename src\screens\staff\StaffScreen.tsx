/**
 * Ocean Soul Sparkles Mobile App - Staff Management Screen
 * Displays staff members with roles, status, and management functionality
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  FlatList,
  TouchableOpacity,
  TextInput,
  ActivityIndicator,
  Alert,
  RefreshControl,
} from 'react-native';
import { AdminUser } from '@/types/database';
import { staffService } from '@/services/database/staffService';

const StaffScreen: React.FC = () => {
  const [staff, setStaff] = useState<AdminUser[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedRole, setSelectedRole] = useState<string | null>(null);
  const [stats, setStats] = useState({
    total: 0,
    active: 0,
    inactive: 0,
    byRole: {} as Record<string, number>
  });

  useEffect(() => {
    loadStaff();
    loadStats();
  }, [searchQuery, selectedRole]);

  const loadStaff = async (isRefresh = false) => {
    try {
      if (isRefresh) {
        setRefreshing(true);
      } else {
        setLoading(true);
      }

      const result = await staffService.getStaff({
        search: searchQuery,
        filters: {
          role: selectedRole || undefined,
        },
        order_by: 'first_name',
        order_direction: 'asc',
      });

      if (result.error) {
        console.error('Error loading staff:', result.error);
        Alert.alert('Error', 'Failed to load staff members');
      } else {
        setStaff(result.data || []);
      }
    } catch (error) {
      console.error('Load staff error:', error);
      Alert.alert('Error', 'Failed to load staff members');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const loadStats = async () => {
    try {
      const statsResult = await staffService.getStaffStats();
      setStats(statsResult);
    } catch (error) {
      console.error('Load stats error:', error);
    }
  };

  const handleToggleStatus = async (staffMember: AdminUser) => {
    try {
      const newStatus = !staffMember.is_active;
      const result = await staffService.updateStaffStatus(staffMember.id, newStatus);

      if (result.error) {
        Alert.alert('Error', 'Failed to update staff status');
      } else {
        // Update local state
        setStaff(prev => prev.map(s =>
          s.id === staffMember.id ? { ...s, is_active: newStatus } : s
        ));
        loadStats(); // Refresh stats
        Alert.alert(
          'Success',
          `${staffMember.first_name} ${staffMember.last_name} is now ${newStatus ? 'active' : 'inactive'}`
        );
      }
    } catch (error) {
      console.error('Toggle status error:', error);
      Alert.alert('Error', 'Failed to update staff status');
    }
  };

  const formatLastActivity = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));

    if (diffInHours < 1) return 'Just now';
    if (diffInHours < 24) return `${diffInHours}h ago`;
    if (diffInHours < 168) return `${Math.floor(diffInHours / 24)}d ago`;
    return date.toLocaleDateString();
  };
  const renderStaffMember = ({ item }: { item: AdminUser }) => (
    <View style={styles.staffCard}>
      <View style={styles.staffHeader}>
        <View style={styles.staffInfo}>
          <View style={styles.nameRow}>
            <Text style={styles.staffIcon}>
              {staffService.getRoleIcon(item.role)}
            </Text>
            <View style={styles.nameContainer}>
              <Text style={styles.staffName}>
                {item.first_name} {item.last_name}
              </Text>
              <Text style={styles.staffEmail}>{item.email}</Text>
            </View>
          </View>

          <View style={styles.roleContainer}>
            <View style={[
              styles.roleBadge,
              { backgroundColor: staffService.getRoleColor(item.role) }
            ]}>
              <Text style={styles.roleText}>{item.role}</Text>
            </View>
          </View>
        </View>

        <TouchableOpacity
          style={[
            styles.statusButton,
            item.is_active ? styles.activeButton : styles.inactiveButton
          ]}
          onPress={() => handleToggleStatus(item)}
        >
          <Text style={[
            styles.statusButtonText,
            item.is_active ? styles.activeButtonText : styles.inactiveButtonText
          ]}>
            {item.is_active ? '✅ Active' : '❌ Inactive'}
          </Text>
        </TouchableOpacity>
      </View>

      <View style={styles.staffDetails}>
        <Text style={styles.detailText}>
          🕒 Last activity: {formatLastActivity(item.last_activity)}
        </Text>
        {item.mfa_enabled && (
          <Text style={styles.detailText}>🔐 MFA Enabled</Text>
        )}
        <Text style={styles.detailText}>
          📅 Joined: {new Date(item.created_at).toLocaleDateString()}
        </Text>
      </View>
    </View>
  );

  const renderRoleFilter = () => {
    const roles = ['DEV', 'Admin', 'Artist', 'Braider'];

    return (
      <View style={styles.roleFilters}>
        <TouchableOpacity
          style={[styles.roleFilter, !selectedRole && styles.activeRoleFilter]}
          onPress={() => setSelectedRole(null)}
        >
          <Text style={[styles.roleFilterText, !selectedRole && styles.activeRoleFilterText]}>
            All ({stats.total})
          </Text>
        </TouchableOpacity>

        {roles.map(role => (
          <TouchableOpacity
            key={role}
            style={[styles.roleFilter, selectedRole === role && styles.activeRoleFilter]}
            onPress={() => setSelectedRole(selectedRole === role ? null : role)}
          >
            <Text style={[styles.roleFilterText, selectedRole === role && styles.activeRoleFilterText]}>
              {staffService.getRoleIcon(role)} {role} ({stats.byRole[role] || 0})
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    );
  };

  const renderEmptyState = () => (
    <View style={styles.emptyContainer}>
      <Text style={styles.emptyIcon}>👥</Text>
      <Text style={styles.emptyText}>No staff members found</Text>
      <Text style={styles.emptySubtext}>
        {searchQuery
          ? 'Try adjusting your search terms'
          : 'Staff members will appear here when added'
        }
      </Text>
    </View>
  );

  const renderStatsHeader = () => (
    <View style={styles.statsContainer}>
      <View style={styles.statCard}>
        <Text style={styles.statNumber}>{stats.total}</Text>
        <Text style={styles.statLabel}>Total Staff</Text>
      </View>
      <View style={styles.statCard}>
        <Text style={[styles.statNumber, { color: '#10b981' }]}>{stats.active}</Text>
        <Text style={styles.statLabel}>Active</Text>
      </View>
      <View style={styles.statCard}>
        <Text style={[styles.statNumber, { color: '#ef4444' }]}>{stats.inactive}</Text>
        <Text style={styles.statLabel}>Inactive</Text>
      </View>
    </View>
  );

  if (loading && staff.length === 0) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#FF9A8B" />
          <Text style={styles.loadingText}>Loading staff...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.headerTitle}>👥 Staff Management</Text>
      </View>

      {/* Stats */}
      {renderStatsHeader()}

      {/* Search */}
      <View style={styles.searchContainer}>
        <TextInput
          style={styles.searchInput}
          placeholder="Search staff members..."
          value={searchQuery}
          onChangeText={setSearchQuery}
          clearButtonMode="while-editing"
        />
      </View>

      {/* Role Filters */}
      {renderRoleFilter()}

      {/* Staff List */}
      <FlatList
        data={staff}
        renderItem={renderStaffMember}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.listContainer}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={() => loadStaff(true)}
            colors={['#FF9A8B']}
          />
        }
        ListEmptyComponent={!loading ? renderEmptyState : null}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    backgroundColor: '#FF9A8B',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomLeftRadius: 20,
    borderBottomRightRadius: 20,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#fff',
    textAlign: 'center',
  },
  statsContainer: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingVertical: 16,
    justifyContent: 'space-between',
  },
  statCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    flex: 1,
    marginHorizontal: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  statNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
  },
  statLabel: {
    fontSize: 12,
    color: '#666',
    marginTop: 4,
  },
  searchContainer: {
    paddingHorizontal: 20,
    paddingBottom: 16,
  },
  searchInput: {
    backgroundColor: '#fff',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  roleFilters: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingBottom: 16,
    flexWrap: 'wrap',
  },
  roleFilter: {
    backgroundColor: '#fff',
    borderRadius: 20,
    paddingHorizontal: 12,
    paddingVertical: 6,
    marginRight: 8,
    marginBottom: 8,
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  activeRoleFilter: {
    backgroundColor: '#FF9A8B',
    borderColor: '#FF9A8B',
  },
  roleFilterText: {
    fontSize: 12,
    color: '#666',
    fontWeight: '500',
  },
  activeRoleFilterText: {
    color: '#fff',
  },
  listContainer: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  staffCard: {
    backgroundColor: '#fff',
    borderRadius: 16,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  staffHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  staffInfo: {
    flex: 1,
  },
  nameRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  staffIcon: {
    fontSize: 24,
    marginRight: 12,
  },
  nameContainer: {
    flex: 1,
  },
  staffName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  staffEmail: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  roleContainer: {
    marginTop: 4,
  },
  roleBadge: {
    borderRadius: 12,
    paddingHorizontal: 8,
    paddingVertical: 4,
    alignSelf: 'flex-start',
  },
  roleText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
  },
  statusButton: {
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 6,
    marginLeft: 12,
  },
  activeButton: {
    backgroundColor: '#dcfce7',
  },
  inactiveButton: {
    backgroundColor: '#fee2e2',
  },
  statusButtonText: {
    fontSize: 12,
    fontWeight: '600',
  },
  activeButtonText: {
    color: '#166534',
  },
  inactiveButtonText: {
    color: '#991b1b',
  },
  staffDetails: {
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
    paddingTop: 12,
  },
  detailText: {
    fontSize: 12,
    color: '#666',
    marginBottom: 4,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    color: '#666',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 60,
  },
  emptyIcon: {
    fontSize: 48,
    marginBottom: 16,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  emptySubtext: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    paddingHorizontal: 40,
  },
});

export default StaffScreen;