/**
 * Database Test Utility
 * Quick test to identify the correct transaction table structure
 */

import { supabase } from '@/services/database/supabase';

export const testDatabaseStructure = async () => {
  console.log('🔍 Testing database structure...');
  
  // Test different possible table names
  const tablesToTest = [
    'transactions',
    'loyalty_transactions',
    'sales',
    'orders',
    'payments',
    'bookings',
    'appointments',
    'reservations',
    'schedules'
  ];
  
  const results: Record<string, any> = {};
  
  for (const tableName of tablesToTest) {
    try {
      console.log(`Testing table: ${tableName}`);
      
      // Try to select from the table
      const { data, error } = await supabase
        .from(tableName)
        .select('*')
        .limit(1);
      
      if (error) {
        results[tableName] = { 
          exists: false, 
          error: error.message,
          accessible: false 
        };
        console.log(`❌ ${tableName}: ${error.message}`);
      } else {
        results[tableName] = { 
          exists: true, 
          accessible: true,
          sampleData: data,
          columns: data && data.length > 0 ? Object.keys(data[0]) : []
        };
        console.log(`✅ ${tableName}: Accessible`);
        if (data && data.length > 0) {
          console.log(`   Columns: ${Object.keys(data[0]).join(', ')}`);
        }
      }
    } catch (err) {
      results[tableName] = { 
        exists: false, 
        error: err instanceof Error ? err.message : 'Unknown error',
        accessible: false 
      };
      console.log(`❌ ${tableName}: ${err}`);
    }
  }
  
  // Test customer relationships
  console.log('\n🔗 Testing customer relationships...');
  
  for (const tableName of tablesToTest) {
    if (results[tableName]?.accessible) {
      try {
        // Try to join with customers table
        const { data, error } = await supabase
          .from(tableName)
          .select(`
            *,
            customer:customers(*)
          `)
          .limit(1);
        
        if (error) {
          results[tableName].customerRelation = {
            works: false,
            error: error.message
          };
          console.log(`❌ ${tableName} -> customers: ${error.message}`);
        } else {
          results[tableName].customerRelation = {
            works: true,
            hasData: data && data.length > 0
          };
          console.log(`✅ ${tableName} -> customers: Works`);
        }
      } catch (err) {
        results[tableName].customerRelation = {
          works: false,
          error: err instanceof Error ? err.message : 'Unknown error'
        };
      }
    }
  }
  
  console.log('\n📊 Final Results:', JSON.stringify(results, null, 2));
  return results;
};

// Test specific query that's failing
export const testTransactionQuery = async () => {
  console.log('🧪 Testing specific transaction query...');
  
  try {
    // This is the exact query from the transaction service
    const { data, error } = await supabase
      .from('transactions')
      .select(`
        *,
        customer:customers(*),
        staff:admin_users(*),
        transaction_items(*)
      `)
      .limit(1);
    
    if (error) {
      console.log('❌ Original query failed:', error.message);
      
      // Try with loyalty_transactions - test different relationship patterns
      console.log('🔍 Testing loyalty_transactions with different relationship patterns...');

      // Test 1: Try with all relationships
      const { data: loyaltyData1, error: loyaltyError1 } = await supabase
        .from('loyalty_transactions')
        .select(`
          *,
          customer:customers(*),
          staff:admin_users(*),
          transaction_items(*)
        `)
        .limit(1);

      if (!loyaltyError1) {
        console.log('✅ Loyalty transactions query with all relationships works!');
        return { success: true, table: 'loyalty_transactions', data: loyaltyData1, queryType: 'full' };
      }

      // Test 2: Try without staff relationship
      const { data: loyaltyData2, error: loyaltyError2 } = await supabase
        .from('loyalty_transactions')
        .select(`
          *,
          customer:customers(*),
          transaction_items(*)
        `)
        .limit(1);

      if (!loyaltyError2) {
        console.log('✅ Loyalty transactions query without staff relationship works!');
        return { success: true, table: 'loyalty_transactions', data: loyaltyData2, queryType: 'no_staff' };
      }

      // Test 3: Try with just basic data
      const { data: loyaltyData3, error: loyaltyError3 } = await supabase
        .from('loyalty_transactions')
        .select('*')
        .limit(1);

      if (!loyaltyError3) {
        console.log('✅ Basic loyalty transactions query works!');
        console.log('📋 Available columns in loyalty_transactions:', loyaltyData3 && loyaltyData3.length > 0 ? Object.keys(loyaltyData3[0]) : 'No data to show columns');
        return { success: true, table: 'loyalty_transactions', data: loyaltyData3, queryType: 'basic' };
      }

      console.log('❌ All loyalty transactions queries failed:', loyaltyError3?.message);
    } else {
      console.log('✅ Original transactions query works!');
      return { success: true, table: 'transactions', data };
    }
  } catch (err) {
    console.log('❌ Query test failed:', err);
  }
  
  return { success: false };
};

// Test to inspect loyalty_transactions table structure
export const inspectLoyaltyTransactions = async () => {
  console.log('🔍 Inspecting loyalty_transactions table structure...');

  try {
    // Get a sample record to see the structure
    const { data, error } = await supabase
      .from('loyalty_transactions')
      .select('*')
      .limit(5);

    if (error) {
      console.log('❌ Error inspecting loyalty_transactions:', error.message);
      return { success: false, error: error.message };
    }

    console.log('📊 Sample loyalty_transactions data:', data);

    if (data && data.length > 0) {
      const columns = Object.keys(data[0]);
      console.log('📋 Available columns:', columns);

      // Check if it has the expected transaction fields
      const expectedFields = ['id', 'customer_id', 'staff_id', 'total_amount', 'created_at'];
      const missingFields = expectedFields.filter(field => !columns.includes(field));
      const extraFields = columns.filter(col => !expectedFields.includes(col));

      console.log('✅ Expected fields present:', expectedFields.filter(field => columns.includes(field)));
      console.log('❌ Missing expected fields:', missingFields);
      console.log('➕ Extra fields:', extraFields);

      return {
        success: true,
        columns,
        sampleData: data,
        missingFields,
        extraFields
      };
    } else {
      console.log('📭 Table is empty');
      return { success: true, columns: [], sampleData: [], empty: true };
    }
  } catch (error) {
    console.log('❌ Inspection failed:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
  }
};

// Test to inspect booking tables
export const inspectBookingTables = async () => {
  console.log('🔍 Inspecting booking tables...');

  const bookingTables = ['bookings', 'appointments', 'reservations', 'schedules'];
  const results: Record<string, any> = {};

  for (const tableName of bookingTables) {
    try {
      console.log(`Testing table: ${tableName}`);

      const { data, error } = await supabase
        .from(tableName)
        .select('*')
        .limit(5);

      if (error) {
        results[tableName] = {
          exists: false,
          error: error.message,
          accessible: false
        };
        console.log(`❌ ${tableName}: ${error.message}`);
      } else {
        results[tableName] = {
          exists: true,
          accessible: true,
          sampleData: data,
          columns: data && data.length > 0 ? Object.keys(data[0]) : [],
          count: data?.length || 0
        };
        console.log(`✅ ${tableName}: Accessible with ${data?.length || 0} records`);
        if (data && data.length > 0) {
          console.log(`   Columns: ${Object.keys(data[0]).join(', ')}`);
          console.log(`   Sample record:`, data[0]);
        }
      }
    } catch (err) {
      results[tableName] = {
        exists: false,
        error: err instanceof Error ? err.message : 'Unknown error',
        accessible: false
      };
      console.log(`❌ ${tableName}: ${err}`);
    }
  }

  console.log('\n📊 Booking Tables Results:', JSON.stringify(results, null, 2));
  return results;
};
