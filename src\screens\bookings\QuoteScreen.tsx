/**
 * Ocean Soul Sparkles Mobile App - Quote Screen
 * Handles quote creation and pricing for pending bookings
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  TouchableOpacity,
  Alert,
  TextInput,
  ActivityIndicator,
} from 'react-native';
import { Booking, Quote } from '@/types/database';
import { bookingService } from '@/services/database/bookingService';
import { quoteService } from '@/services/database/quoteService';
import { useAuth } from '@/store/authStore.minimal';

interface QuoteScreenProps {
  booking: Booking;
  onClose: () => void;
  onQuoteCreated: () => void;
}

const QuoteScreen: React.FC<QuoteScreenProps> = ({
  booking,
  onClose,
  onQuoteCreated,
}) => {
  const [loading, setSaving] = useState(false);
  const [quoteData, setQuoteData] = useState({
    basePrice: booking.service?.base_price?.toString() || '',
    additionalServices: '',
    discount: '',
    notes: '',
  });

  const { user } = useAuth();

  const calculateTotal = () => {
    const base = parseFloat(quoteData.basePrice) || 0;
    const additional = parseFloat(quoteData.additionalServices) || 0;
    const discount = parseFloat(quoteData.discount) || 0;
    return Math.max(0, base + additional - discount);
  };

  const handleCreateQuote = async () => {
    try {
      setSaving(true);

      const total = calculateTotal();

      // Create quote title based on service
      const quoteTitle = booking.service?.name
        ? `Quote for ${booking.service.name}`
        : 'Service Quote';

      // Create detailed description
      const quoteDescription = `
QUOTE BREAKDOWN:
• Base Service: $${quoteData.basePrice}
${quoteData.additionalServices ? `• Additional Services: $${quoteData.additionalServices}` : ''}
${quoteData.discount ? `• Discount: -$${quoteData.discount}` : ''}

Total Amount: $${total.toFixed(2)}

${quoteData.notes ? `Additional Notes: ${quoteData.notes}` : ''}
      `.trim();

      // Create the quote record
      const quoteResult = await quoteService.createQuote({
        customer_id: booking.customer_id,
        staff_id: user?.id,
        booking_id: booking.id,
        title: quoteTitle,
        description: quoteDescription,
        total_amount: total,
        status: 'sent',
        valid_until: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 days from now
        notes: quoteData.notes || undefined,
      });

      if (quoteResult.error) {
        throw new Error(quoteResult.error.message);
      }

      // Update booking with quote information and new status
      const bookingResult = await bookingService.updateBooking(booking.id, {
        total_amount: total,
        status: 'pending', // Keep as pending until quote is accepted
        notes: `Quote created: ${quoteTitle} - $${total.toFixed(2)}`,
      });

      if (bookingResult.error) {
        console.warn('Warning: Quote created but booking update failed:', bookingResult.error);
      }

      Alert.alert(
        'Quote Created Successfully! ✅',
        `Quote "${quoteTitle}" for $${total.toFixed(2)} has been created and sent to the customer.`,
        [
          {
            text: 'OK',
            onPress: () => {
              onQuoteCreated();
              onClose();
            },
          },
        ]
      );
    } catch (err) {
      console.error('❌ Failed to create quote:', err);
      Alert.alert('Error', 'Failed to create quote. Please try again.');
    } finally {
      setSaving(false);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={onClose} style={styles.backButton}>
          <Text style={styles.backButtonText}>‹ Back</Text>
        </TouchableOpacity>
        
        <Text style={styles.headerTitle}>Create Quote</Text>
        
        <View style={styles.placeholder} />
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Customer Info */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>👤 Customer</Text>
          <View style={styles.infoCard}>
            <Text style={styles.customerName}>
              {booking.customer?.full_name || 'Unknown Customer'}
            </Text>
            <Text style={styles.serviceName}>
              {booking.service?.name || 'Unknown Service'}
            </Text>
          </View>
        </View>

        {/* Quote Form */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>💰 Quote Details</Text>
          <View style={styles.formCard}>
            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Base Service Price</Text>
              <TextInput
                style={styles.input}
                value={quoteData.basePrice}
                onChangeText={(text) => setQuoteData(prev => ({ ...prev, basePrice: text }))}
                placeholder="0.00"
                keyboardType="numeric"
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Additional Services</Text>
              <TextInput
                style={styles.input}
                value={quoteData.additionalServices}
                onChangeText={(text) => setQuoteData(prev => ({ ...prev, additionalServices: text }))}
                placeholder="0.00"
                keyboardType="numeric"
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Discount</Text>
              <TextInput
                style={styles.input}
                value={quoteData.discount}
                onChangeText={(text) => setQuoteData(prev => ({ ...prev, discount: text }))}
                placeholder="0.00"
                keyboardType="numeric"
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Notes</Text>
              <TextInput
                style={[styles.input, styles.textArea]}
                value={quoteData.notes}
                onChangeText={(text) => setQuoteData(prev => ({ ...prev, notes: text }))}
                placeholder="Additional notes or special instructions..."
                multiline
                numberOfLines={3}
              />
            </View>
          </View>
        </View>

        {/* Quote Summary */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>📋 Quote Summary</Text>
          <View style={styles.summaryCard}>
            <View style={styles.summaryRow}>
              <Text style={styles.summaryLabel}>Base Service:</Text>
              <Text style={styles.summaryValue}>${quoteData.basePrice || '0.00'}</Text>
            </View>
            
            <View style={styles.summaryRow}>
              <Text style={styles.summaryLabel}>Additional Services:</Text>
              <Text style={styles.summaryValue}>${quoteData.additionalServices || '0.00'}</Text>
            </View>
            
            <View style={styles.summaryRow}>
              <Text style={styles.summaryLabel}>Discount:</Text>
              <Text style={styles.summaryValue}>-${quoteData.discount || '0.00'}</Text>
            </View>
            
            <View style={[styles.summaryRow, styles.totalRow]}>
              <Text style={styles.totalLabel}>Total:</Text>
              <Text style={styles.totalValue}>${calculateTotal().toFixed(2)}</Text>
            </View>
          </View>
        </View>

        {/* Action Buttons */}
        <View style={styles.actionSection}>
          <TouchableOpacity 
            style={[styles.createButton, loading && styles.disabledButton]} 
            onPress={handleCreateQuote}
            disabled={loading}
          >
            {loading ? (
              <ActivityIndicator color="#fff" />
            ) : (
              <Text style={styles.createButtonText}>✅ Create Quote & Confirm Booking</Text>
            )}
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.cancelButton} onPress={onClose}>
            <Text style={styles.cancelButtonText}>Cancel</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: '#10b981',
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  backButton: {
    paddingVertical: 8,
    paddingHorizontal: 12,
  },
  backButtonText: {
    fontSize: 18,
    color: '#fff',
    fontWeight: 'bold',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#fff',
  },
  placeholder: {
    width: 60,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 12,
  },
  infoCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  customerName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  serviceName: {
    fontSize: 16,
    color: '#666',
  },
  formCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  inputGroup: {
    marginBottom: 16,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    fontSize: 16,
    backgroundColor: '#fff',
  },
  textArea: {
    height: 80,
    textAlignVertical: 'top',
  },
  summaryCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
  },
  summaryLabel: {
    fontSize: 16,
    color: '#666',
  },
  summaryValue: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
  },
  totalRow: {
    borderTopWidth: 1,
    borderTopColor: '#eee',
    marginTop: 8,
    paddingTop: 16,
  },
  totalLabel: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  totalValue: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#10b981',
  },
  actionSection: {
    marginTop: 24,
    marginBottom: 32,
  },
  createButton: {
    backgroundColor: '#10b981',
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
    marginBottom: 12,
  },
  disabledButton: {
    opacity: 0.6,
  },
  createButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  cancelButton: {
    backgroundColor: '#6b7280',
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
  },
  cancelButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
});

export default QuoteScreen;
