/**
 * Ocean Soul Sparkles Mobile App - Quote Service
 * Handles quote management and CRUD operations
 */

import { supabase } from './supabase';
import { Quote, QuoteItem, Customer, Staff, Booking, DatabaseResponse, DatabaseListResponse, QueryFilters } from '@/types/database';
import { bookingService } from './bookingService';

export class QuoteService {

  /**
   * Get all quotes with optional filtering and relations
   */
  async getQuotes(filters?: QueryFilters): Promise<DatabaseListResponse<Quote>> {
    try {
      console.log('📋 Loading quotes...');
      
      let query = supabase
        .from('quotes')
        .select('*');

      // Apply search filter with performance optimization
      if (filters?.search) {
        const searchTerm = filters.search.trim();
        if (searchTerm.length >= 2) { // Minimum search length for performance
          query = query.or(`title.ilike.%${searchTerm}%,description.ilike.%${searchTerm}%`);
        }
      }

      // Apply status filter (indexed field, very efficient)
      if (filters?.filters?.status) {
        query = query.eq('status', filters.filters.status);
      }

      // Apply customer filter (indexed field, very efficient)
      if (filters?.filters?.customer_id) {
        query = query.eq('customer_id', filters.filters.customer_id);
      }

      // Apply ordering with performance consideration
      const orderBy = filters?.order_by || 'created_at';
      const orderDirection = filters?.order_direction || 'desc';
      query = query.order(orderBy, { ascending: orderDirection === 'asc' });

      // Apply pagination with performance-conscious defaults
      const limit = filters?.limit || 25; // Smaller default for quotes
      const offset = filters?.offset || 0;

      query = query.limit(limit);
      if (offset > 0) {
        query = query.range(offset, offset + limit - 1);
      }

      const { data, error, count } = await query;

      if (error) {
        console.error('❌ Get quotes error:', error);
        return { data: null, error, count: 0 };
      }

      console.log(`✅ Loaded ${data?.length || 0} quotes`);
      return { data: data || [], error: null, count: count || data?.length || 0 };
    } catch (error) {
      console.error('❌ Quote service error:', error);
      return { data: null, error: error as Error, count: 0 };
    }
  }

  /**
   * Get a single quote by ID with relations
   */
  async getQuoteById(id: string): Promise<DatabaseResponse<Quote>> {
    try {
      console.log('📋 Loading quote:', id);

      const { data, error } = await supabase
        .from('quotes')
        .select('*')
        .eq('id', id)
        .single();

      if (error) {
        console.error('❌ Get quote error:', error);
        return { data: null, error };
      }

      console.log('✅ Quote loaded successfully');
      return { data, error: null };
    } catch (error) {
      console.error('❌ Get quote service error:', error);
      return { data: null, error: error as Error };
    }
  }

  /**
   * Create a new quote
   */
  async createQuote(quoteData: Partial<Quote>): Promise<DatabaseResponse<Quote>> {
    try {
      console.log('📝 Creating new quote...');

      const { data, error } = await supabase
        .from('quotes')
        .insert([{
          customer_id: quoteData.customer_id,
          staff_id: quoteData.staff_id,
          booking_id: quoteData.booking_id,
          title: quoteData.title,
          description: quoteData.description,
          estimated_total: quoteData.estimated_total,
          status: quoteData.status || 'draft',
          valid_until: quoteData.valid_until,
          notes: quoteData.notes,
        }])
        .select('*')
        .single();

      if (error) {
        console.error('❌ Create quote error:', error);
        return { data: null, error };
      }

      console.log('✅ Quote created successfully');
      return { data, error: null };
    } catch (error) {
      console.error('❌ Create quote service error:', error);
      return { data: null, error: error as Error };
    }
  }

  /**
   * Update an existing quote
   */
  async updateQuote(id: string, updateData: Partial<Quote>): Promise<DatabaseResponse<Quote>> {
    try {
      console.log('📝 Updating quote:', id);

      const { data, error } = await supabase
        .from('quotes')
        .update({
          customer_id: updateData.customer_id,
          staff_id: updateData.staff_id,
          booking_id: updateData.booking_id,
          title: updateData.title,
          description: updateData.description,
          estimated_total: updateData.estimated_total,
          status: updateData.status,
          valid_until: updateData.valid_until,
          notes: updateData.notes,
          updated_at: new Date().toISOString(),
        })
        .eq('id', id)
        .select('*')
        .single();

      if (error) {
        console.error('❌ Update quote error:', error);
        return { data: null, error };
      }

      console.log('✅ Quote updated successfully');
      return { data, error: null };
    } catch (error) {
      console.error('❌ Update quote service error:', error);
      return { data: null, error: error as Error };
    }
  }

  /**
   * Delete a quote
   */
  async deleteQuote(id: string): Promise<DatabaseResponse<boolean>> {
    try {
      console.log('🗑️ Deleting quote:', id);

      // First delete quote items
      await supabase
        .from('quote_items')
        .delete()
        .eq('quote_id', id);

      // Then delete the quote
      const { error } = await supabase
        .from('quotes')
        .delete()
        .eq('id', id);

      if (error) {
        console.error('❌ Delete quote error:', error);
        return { data: null, error };
      }

      console.log('✅ Quote deleted successfully');
      return { data: true, error: null };
    } catch (error) {
      console.error('❌ Delete quote service error:', error);
      return { data: null, error: error as Error };
    }
  }

  /**
   * Add item to quote
   */
  async addQuoteItem(quoteId: string, itemData: Partial<QuoteItem>): Promise<DatabaseResponse<QuoteItem>> {
    try {
      console.log('📝 Adding item to quote:', quoteId);

      const { data, error } = await supabase
        .from('quote_items')
        .insert([{
          quote_id: quoteId,
          item_type: itemData.item_type,
          item_id: itemData.item_id,
          quantity: itemData.quantity,
          unit_price: itemData.unit_price,
          total_price: itemData.total_price,
          description: itemData.description,
        }])
        .select('*')
        .single();

      if (error) {
        console.error('❌ Add quote item error:', error);
        return { data: null, error };
      }

      console.log('✅ Quote item added successfully');
      return { data, error: null };
    } catch (error) {
      console.error('❌ Add quote item service error:', error);
      return { data: null, error: error as Error };
    }
  }

  /**
   * Update quote status
   */
  async updateQuoteStatus(id: string, status: Quote['status']): Promise<DatabaseResponse<Quote>> {
    try {
      console.log('📝 Updating quote status:', id, status);

      const { data, error } = await supabase
        .from('quotes')
        .update({
          status,
          updated_at: new Date().toISOString(),
        })
        .eq('id', id)
        .select('*')
        .single();

      if (error) {
        console.error('❌ Update quote status error:', error);
        return { data: null, error };
      }

      console.log('✅ Quote status updated successfully');
      return { data, error: null };
    } catch (error) {
      console.error('❌ Update quote status service error:', error);
      return { data: null, error: error as Error };
    }
  }

  /**
   * Get quotes by customer
   */
  async getQuotesByCustomer(customerId: string): Promise<DatabaseListResponse<Quote>> {
    return this.getQuotes({
      filters: { customer_id: customerId },
      order_by: 'created_at',
      order_direction: 'desc'
    });
  }

  /**
   * Get quotes by status
   */
  async getQuotesByStatus(status: Quote['status']): Promise<DatabaseListResponse<Quote>> {
    return this.getQuotes({
      filters: { status },
      order_by: 'created_at',
      order_direction: 'desc'
    });
  }

  /**
   * Convert an approved quote to a confirmed booking
   */
  async convertQuoteToBooking(quoteId: string): Promise<DatabaseResponse<Booking>> {
    try {
      console.log('🔄 Converting quote to booking:', quoteId);

      // First, get the quote with all its details
      const quoteResult = await this.getQuoteById(quoteId);
      if (quoteResult.error || !quoteResult.data) {
        throw new Error(quoteResult.error?.message || 'Quote not found');
      }

      const quote = quoteResult.data;

      // Check if quote can be converted
      if (quote.status !== 'accepted') {
        throw new Error('Only accepted quotes can be converted to bookings');
      }

      if (!quote.booking_id) {
        throw new Error('Quote is not linked to a booking');
      }

      // Update the related booking with quote details
      const bookingResult = await bookingService.updateBooking(quote.booking_id, {
        status: 'confirmed',
        total_amount: quote.estimated_total,
        notes: `Booking confirmed from quote: ${quote.title}`,
      });

      if (bookingResult.error) {
        throw new Error(`Failed to update booking: ${bookingResult.error.message}`);
      }

      // Update quote status to indicate it has been converted
      await this.updateQuoteStatus(quoteId, 'accepted');

      console.log('✅ Quote converted to booking successfully');
      return { data: bookingResult.data, error: null };
    } catch (error) {
      console.error('❌ Quote to booking conversion error:', error);
      return { data: null, error: error as Error };
    }
  }

  /**
   * Approve quote and optionally convert to booking
   */
  async approveQuote(quoteId: string, convertToBooking: boolean = true): Promise<DatabaseResponse<Quote>> {
    try {
      console.log('✅ Approving quote:', quoteId);

      // Update quote status to accepted
      const result = await this.updateQuoteStatus(quoteId, 'accepted');

      if (result.error) {
        throw new Error(result.error.message);
      }

      // If requested and quote has a booking, convert to confirmed booking
      if (convertToBooking && result.data?.booking_id) {
        const conversionResult = await this.convertQuoteToBooking(quoteId);
        if (conversionResult.error) {
          console.warn('Quote approved but booking conversion failed:', conversionResult.error);
        }
      }

      console.log('✅ Quote approved successfully');
      return result;
    } catch (error) {
      console.error('❌ Quote approval error:', error);
      return { data: null, error: error as Error };
    }
  }
}

// Export singleton instance
export const quoteService = new QuoteService();
