/**
 * Ocean Soul Sparkles Mobile App - POS Store Tests
 * Tests for cart management and POS state
 */

import { usePOSStore } from '../posStore';
import { Product, Service } from '@/types/database';

// Mock product and service data
const mockProduct: Product = {
  id: 'product-1',
  name: 'Test Product',
  description: 'A test product',
  price: 25.00,
  category: 'test',
  stock_quantity: 10,
  is_active: true,
  created_at: new Date().toISOString(),
  updated_at: new Date().toISOString(),
};

const mockService: Service = {
  id: 'service-1',
  name: 'Test Service',
  description: 'A test service',
  base_price: 50.00,
  category: 'test',
  duration_minutes: 60,
  is_active: true,
  created_at: new Date().toISOString(),
  updated_at: new Date().toISOString(),
};

describe('POS Store', () => {
  beforeEach(() => {
    // Reset store state before each test
    usePOSStore.getState().clearCart();
    usePOSStore.getState().setSearchQuery('');
    usePOSStore.getState().setSelectedCategory(null);
    usePOSStore.getState().setCurrentCustomer(null);
  });

  describe('Cart Management', () => {
    it('should add product to cart', () => {
      const { addToCart, cartItems, cartTotal, cartItemCount } = usePOSStore.getState();

      addToCart(mockProduct, 'product', 2);

      const state = usePOSStore.getState();
      expect(state.cartItems).toHaveLength(1);
      expect(state.cartItems[0].item.name).toBe(mockProduct.name);
      expect(state.cartItems[0].quantity).toBe(2);
      expect(state.cartItems[0].total_price).toBe(50.00); // 25 * 2
      expect(state.cartTotal).toBe(50.00);
      expect(state.cartItemCount).toBe(2);
    });

    it('should add service to cart', () => {
      const { addToCart } = usePOSStore.getState();

      addToCart(mockService, 'service', 1);

      const state = usePOSStore.getState();
      expect(state.cartItems).toHaveLength(1);
      expect(state.cartItems[0].item.name).toBe(mockService.name);
      expect(state.cartItems[0].quantity).toBe(1);
      expect(state.cartItems[0].total_price).toBe(50.00);
      expect(state.cartTotal).toBe(50.00);
      expect(state.cartItemCount).toBe(1);
    });

    it('should update quantity when adding existing item', () => {
      const { addToCart } = usePOSStore.getState();

      // Add item first time
      addToCart(mockProduct, 'product', 1);
      expect(usePOSStore.getState().cartItems).toHaveLength(1);
      expect(usePOSStore.getState().cartItems[0].quantity).toBe(1);

      // Add same item again
      addToCart(mockProduct, 'product', 2);
      expect(usePOSStore.getState().cartItems).toHaveLength(1);
      expect(usePOSStore.getState().cartItems[0].quantity).toBe(3);
      expect(usePOSStore.getState().cartTotal).toBe(75.00); // 25 * 3
    });

    it('should remove item from cart', () => {
      const { addToCart, removeFromCart } = usePOSStore.getState();

      addToCart(mockProduct, 'product', 2);
      const cartItemId = usePOSStore.getState().cartItems[0].id;

      removeFromCart(cartItemId);

      const state = usePOSStore.getState();
      expect(state.cartItems).toHaveLength(0);
      expect(state.cartTotal).toBe(0);
      expect(state.cartItemCount).toBe(0);
    });

    it('should update cart item quantity', () => {
      const { addToCart, updateCartItemQuantity } = usePOSStore.getState();

      addToCart(mockProduct, 'product', 2);
      const cartItemId = usePOSStore.getState().cartItems[0].id;

      updateCartItemQuantity(cartItemId, 5);

      const state = usePOSStore.getState();
      expect(state.cartItems[0].quantity).toBe(5);
      expect(state.cartItems[0].total_price).toBe(125.00); // 25 * 5
      expect(state.cartTotal).toBe(125.00);
      expect(state.cartItemCount).toBe(5);
    });

    it('should clear entire cart', () => {
      const { addToCart, clearCart } = usePOSStore.getState();

      addToCart(mockProduct, 'product', 2);
      addToCart(mockService, 'service', 1);

      expect(usePOSStore.getState().cartItems).toHaveLength(2);

      clearCart();

      const state = usePOSStore.getState();
      expect(state.cartItems).toHaveLength(0);
      expect(state.cartTotal).toBe(0);
      expect(state.cartItemCount).toBe(0);
    });

    it('should handle multiple different items', () => {
      const { addToCart } = usePOSStore.getState();

      addToCart(mockProduct, 'product', 2);
      addToCart(mockService, 'service', 1);

      const state = usePOSStore.getState();
      expect(state.cartItems).toHaveLength(2);
      expect(state.cartTotal).toBe(100.00); // (25 * 2) + (50 * 1)
      expect(state.cartItemCount).toBe(3); // 2 + 1
    });
  });

  describe('Search and Filtering', () => {
    it('should update search query', () => {
      const { setSearchQuery } = usePOSStore.getState();

      setSearchQuery('test search');

      expect(usePOSStore.getState().searchQuery).toBe('test search');
    });

    it('should update selected category', () => {
      const { setSelectedCategory } = usePOSStore.getState();

      setSelectedCategory('hair');

      expect(usePOSStore.getState().selectedCategory).toBe('hair');
    });

    it('should clear selected category', () => {
      const { setSelectedCategory } = usePOSStore.getState();

      setSelectedCategory('hair');
      expect(usePOSStore.getState().selectedCategory).toBe('hair');

      setSelectedCategory(null);
      expect(usePOSStore.getState().selectedCategory).toBeNull();
    });
  });

  describe('Customer Management', () => {
    it('should set current customer', () => {
      const { setCurrentCustomer } = usePOSStore.getState();
      const mockCustomer = {
        id: 'customer-1',
        name: 'Test Customer',
        email: '<EMAIL>',
        phone: '+61400000000',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };

      setCurrentCustomer(mockCustomer);

      expect(usePOSStore.getState().currentCustomer).toBe(mockCustomer);
    });

    it('should clear current customer', () => {
      const { setCurrentCustomer } = usePOSStore.getState();

      setCurrentCustomer(null);

      expect(usePOSStore.getState().currentCustomer).toBeNull();
    });
  });

  describe('Helper Functions', () => {
    it('should find cart item by ID', () => {
      const { addToCart, getCartItemById } = usePOSStore.getState();

      addToCart(mockProduct, 'product', 1);
      const cartItemId = usePOSStore.getState().cartItems[0].id;

      const foundItem = getCartItemById(cartItemId);

      expect(foundItem).toBeDefined();
      expect(foundItem?.item.name).toBe(mockProduct.name);
    });

    it('should find cart item by item ID and type', () => {
      const { addToCart, getCartItemByItemId } = usePOSStore.getState();

      addToCart(mockProduct, 'product', 1);

      const foundItem = getCartItemByItemId(mockProduct.id, 'product');

      expect(foundItem).toBeDefined();
      expect(foundItem?.item.name).toBe(mockProduct.name);
    });

    it('should return undefined for non-existent cart item', () => {
      const { getCartItemById, getCartItemByItemId } = usePOSStore.getState();

      const foundById = getCartItemById('non-existent-id');
      const foundByItemId = getCartItemByItemId('non-existent-item-id', 'product');

      expect(foundById).toBeUndefined();
      expect(foundByItemId).toBeUndefined();
    });
  });

  describe('Loading State', () => {
    it('should update loading state', () => {
      const { setLoading } = usePOSStore.getState();

      setLoading(true);
      expect(usePOSStore.getState().isLoading).toBe(true);

      setLoading(false);
      expect(usePOSStore.getState().isLoading).toBe(false);
    });
  });
});
