{"extends": "expo/tsconfig.base", "compilerOptions": {"strict": true, "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@/components/*": ["./src/components/*"], "@/screens/*": ["./src/screens/*"], "@/services/*": ["./src/services/*"], "@/hooks/*": ["./src/hooks/*"], "@/store/*": ["./src/store/*"], "@/utils/*": ["./src/utils/*"], "@/types/*": ["./src/types/*"], "@/assets/*": ["./src/assets/*"]}, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "skipLibCheck": true, "resolveJsonModule": true, "noEmit": true, "jsx": "react-jsx"}, "include": ["**/*.ts", "**/*.tsx"], "exclude": ["node_modules", "dist", "build", "android", "ios"]}