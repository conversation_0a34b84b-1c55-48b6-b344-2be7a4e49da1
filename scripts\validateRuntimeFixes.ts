/**
 * Ocean Soul Sparkles Mobile App - Runtime Validation Script
 * Validates that critical runtime errors have been fixed
 */

import { Quote } from '../src/types/database';
import { getFallbackTemplate } from '../src/services/email/emailTemplates';

/**
 * Test 1: Validate Quote Property References
 */
function testQuotePropertyReferences(): { success: boolean; errors: string[] } {
  const errors: string[] = [];
  
  try {
    // Test Quote interface has correct properties
    const testQuote: Quote = {
      id: 'test-quote-id',
      customer_id: 'test-customer-id',
      service_name: 'Test Quote',
      service_description: 'Test quote description',
      estimated_total: 150.00, // This should be the correct property name
      status: 'draft',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };

    // Verify estimated_total property exists and is accessible
    if (typeof testQuote.estimated_total !== 'number') {
      errors.push('Quote.estimated_total property is not accessible or not a number');
    }

    // Test email template variables
    const quoteTemplate = getFallbackTemplate('quote');
    if (!quoteTemplate) {
      errors.push('Quote email template not found');
    } else {
      // Check that template uses estimated_total, not quote_amount
      if (quoteTemplate.html_content.includes('{{quote_amount}}')) {
        errors.push('Email template still contains {{quote_amount}} instead of {{estimated_total}}');
      }

      if (quoteTemplate.text_content?.includes('{{quote_amount}}')) {
        errors.push('Email template text content still contains {{quote_amount}} instead of {{estimated_total}}');
      }

      // Check variables array
      if (quoteTemplate.variables.includes('quote_amount')) {
        errors.push('Email template variables array still contains quote_amount instead of estimated_total');
      }

      if (!quoteTemplate.variables.includes('estimated_total')) {
        errors.push('Email template variables array missing estimated_total');
      }
    }

    console.log('✅ Quote property reference test completed');
    return { success: errors.length === 0, errors };

  } catch (error) {
    errors.push(`Quote property test failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    return { success: false, errors };
  }
}

/**
 * Test 2: Validate App Registration Setup
 */
function testAppRegistrationSetup(): { success: boolean; errors: string[] } {
  const errors: string[] = [];
  
  try {
    // Check if index.js exists and has proper structure
    const fs = require('fs');
    const path = require('path');
    
    const indexPath = path.join(process.cwd(), 'index.js');
    if (!fs.existsSync(indexPath)) {
      errors.push('index.js entry point file does not exist');
    } else {
      const indexContent = fs.readFileSync(indexPath, 'utf8');
      
      if (!indexContent.includes('registerRootComponent')) {
        errors.push('index.js does not contain registerRootComponent call');
      }
      
      if (!indexContent.includes('from \'./App\'')) {
        errors.push('index.js does not import App component correctly');
      }
    }

    // Check app.json configuration
    const appJsonPath = path.join(process.cwd(), 'app.json');
    if (!fs.existsSync(appJsonPath)) {
      errors.push('app.json configuration file does not exist');
    } else {
      const appJsonContent = fs.readFileSync(appJsonPath, 'utf8');
      const appConfig = JSON.parse(appJsonContent);
      
      if (!appConfig.expo?.main) {
        errors.push('app.json does not specify main entry point');
      } else if (appConfig.expo.main !== 'index.js') {
        errors.push(`app.json main entry point is ${appConfig.expo.main}, should be index.js`);
      }
    }

    console.log('✅ App registration setup test completed');
    return { success: errors.length === 0, errors };

  } catch (error) {
    errors.push(`App registration test failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    return { success: false, errors };
  }
}

/**
 * Test 3: Validate Email Template Consistency
 */
function testEmailTemplateConsistency(): { success: boolean; errors: string[] } {
  const errors: string[] = [];
  
  try {
    const templates = ['quote', 'reminder'];
    
    for (const templateType of templates) {
      const template = getFallbackTemplate(templateType);
      if (!template) {
        errors.push(`${templateType} template not found`);
        continue;
      }

      // Check HTML content
      if (template.html_content.includes('{{quote_amount}}')) {
        errors.push(`${templateType} template HTML still uses {{quote_amount}}`);
      }

      // Check text content
      if (template.text_content?.includes('{{quote_amount}}')) {
        errors.push(`${templateType} template text still uses {{quote_amount}}`);
      }

      // Check variables array
      if (template.variables.includes('quote_amount')) {
        errors.push(`${templateType} template variables still includes quote_amount`);
      }

      // Verify total_amount is present where expected
      if (templateType === 'quote' || templateType === 'reminder') {
        if (!template.html_content.includes('{{total_amount}}')) {
          errors.push(`${templateType} template HTML missing {{total_amount}}`);
        }
        
        if (!template.variables.includes('total_amount')) {
          errors.push(`${templateType} template variables missing total_amount`);
        }
      }
    }

    console.log('✅ Email template consistency test completed');
    return { success: errors.length === 0, errors };

  } catch (error) {
    errors.push(`Email template test failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    return { success: false, errors };
  }
}

/**
 * Main validation function
 */
export function validateRuntimeFixes(): {
  success: boolean;
  summary: string;
  details: {
    quoteProperties: { success: boolean; errors: string[] };
    appRegistration: { success: boolean; errors: string[] };
    emailTemplates: { success: boolean; errors: string[] };
  };
} {
  console.log('🧪 Running runtime fixes validation...');

  const quotePropertiesResult = testQuotePropertyReferences();
  const appRegistrationResult = testAppRegistrationSetup();
  const emailTemplatesResult = testEmailTemplateConsistency();

  const allTests = [quotePropertiesResult, appRegistrationResult, emailTemplatesResult];
  const allPassed = allTests.every(test => test.success);
  const totalErrors = allTests.reduce((sum, test) => sum + test.errors.length, 0);

  const summary = allPassed 
    ? '✅ All runtime fixes validated successfully - app should build and run without errors'
    : `❌ ${totalErrors} runtime issues found - these must be fixed before deployment`;

  console.log(summary);

  return {
    success: allPassed,
    summary,
    details: {
      quoteProperties: quotePropertiesResult,
      appRegistration: appRegistrationResult,
      emailTemplates: emailTemplatesResult,
    },
  };
}

/**
 * Run validation if script is executed directly
 */
if (require.main === module) {
  const result = validateRuntimeFixes();
  
  console.log('\n📊 VALIDATION RESULTS:');
  console.log('='.repeat(50));
  console.log(result.summary);
  
  if (!result.success) {
    console.log('\n🚨 ERRORS FOUND:');
    
    if (result.details.quoteProperties.errors.length > 0) {
      console.log('\n📝 Quote Properties Issues:');
      result.details.quoteProperties.errors.forEach(error => console.log(`  - ${error}`));
    }
    
    if (result.details.appRegistration.errors.length > 0) {
      console.log('\n📱 App Registration Issues:');
      result.details.appRegistration.errors.forEach(error => console.log(`  - ${error}`));
    }
    
    if (result.details.emailTemplates.errors.length > 0) {
      console.log('\n📧 Email Template Issues:');
      result.details.emailTemplates.errors.forEach(error => console.log(`  - ${error}`));
    }
    
    process.exit(1);
  } else {
    console.log('\n🎉 All runtime fixes validated successfully!');
    console.log('The app should now build and run without the reported errors.');
    process.exit(0);
  }
}
