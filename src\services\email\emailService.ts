/**
 * Ocean Soul Sparkles Mobile App - Email Service
 * Integrates with admin portal email infrastructure for consistent email delivery
 */

import { supabase } from '@/services/database/supabase';
import { getFallbackTemplate } from './emailTemplates';
import Constants from 'expo-constants';

// Environment variables
const API_BASE_URL = Constants.expoConfig?.extra?.EXPO_PUBLIC_API_BASE_URL || process.env.EXPO_PUBLIC_API_BASE_URL;

export interface EmailTemplate {
  id: string;
  name: string;
  subject: string;
  html_content: string;
  text_content?: string;
  template_type: 'quote' | 'booking_confirmation' | 'invoice' | 'receipt' | 'reminder';
  variables: string[]; // Array of variable names like ['customer_name', 'total_amount']
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface EmailRequest {
  to: string;
  to_name?: string;
  template_type: EmailTemplate['template_type'];
  template_variables: Record<string, any>;
  subject_override?: string;
  priority?: 'low' | 'normal' | 'high';
  send_immediately?: boolean;
}

export interface EmailDeliveryStatus {
  id: string;
  email_request_id: string;
  status: 'pending' | 'sent' | 'delivered' | 'failed' | 'bounced';
  sent_at?: string;
  delivered_at?: string;
  failed_at?: string;
  error_message?: string;
  provider_message_id?: string;
  created_at: string;
  updated_at: string;
}

export interface EmailResult {
  success: boolean;
  email_id?: string;
  delivery_status?: EmailDeliveryStatus;
  error?: string;
}

export class EmailService {
  private static instance: EmailService;
  private authToken: string | null = null;

  private constructor() {}

  public static getInstance(): EmailService {
    if (!EmailService.instance) {
      EmailService.instance = new EmailService();
    }
    return EmailService.instance;
  }

  /**
   * Set authentication token for admin portal API calls
   */
  setAuthToken(token: string) {
    this.authToken = token;
  }

  /**
   * Get email templates from Supabase database
   */
  async getEmailTemplates(templateType?: EmailTemplate['template_type']): Promise<EmailTemplate[]> {
    try {
      console.log('📧 Loading email templates...');

      let query = supabase
        .from('email_templates')
        .select('*')
        .eq('is_active', true);

      if (templateType) {
        query = query.eq('template_type', templateType);
      }

      const { data, error } = await query.order('name');

      if (error) {
        console.error('❌ Failed to load email templates:', error);
        return [];
      }

      console.log(`✅ Loaded ${data?.length || 0} email templates`);
      return data || [];
    } catch (error) {
      console.error('❌ Email templates error:', error);
      return [];
    }
  }

  /**
   * Get specific email template by type
   */
  async getEmailTemplate(templateType: EmailTemplate['template_type']): Promise<EmailTemplate | null> {
    try {
      const templates = await this.getEmailTemplates(templateType);

      if (templates.length > 0) {
        return templates[0];
      }

      // Fallback to built-in templates if Supabase templates not available
      console.log(`⚠️ No Supabase template found for ${templateType}, using fallback template`);
      const fallbackTemplate = getFallbackTemplate(templateType);

      if (fallbackTemplate) {
        console.log(`✅ Using fallback template for ${templateType}`);
        return fallbackTemplate;
      }

      console.error(`❌ No template available for type: ${templateType}`);
      return null;
    } catch (error) {
      console.error('❌ Failed to get email template:', error);

      // Try fallback template on error
      const fallbackTemplate = getFallbackTemplate(templateType);
      if (fallbackTemplate) {
        console.log(`✅ Using fallback template for ${templateType} due to error`);
        return fallbackTemplate;
      }

      return null;
    }
  }

  /**
   * Process template variables and replace placeholders
   */
  private processTemplate(template: EmailTemplate, variables: Record<string, any>): { subject: string; html: string; text?: string } {
    let subject = template.subject;
    let html = template.html_content;
    let text = template.text_content;

    // Replace variables in subject
    Object.entries(variables).forEach(([key, value]) => {
      const placeholder = `{{${key}}}`;
      const stringValue = String(value || '');
      
      subject = subject.replace(new RegExp(placeholder, 'g'), stringValue);
      html = html.replace(new RegExp(placeholder, 'g'), stringValue);
      if (text) {
        text = text.replace(new RegExp(placeholder, 'g'), stringValue);
      }
    });

    return { subject, html, text };
  }

  /**
   * Send email using admin portal API
   */
  async sendEmail(emailRequest: EmailRequest): Promise<EmailResult> {
    try {
      console.log('📧 Sending email via admin portal API...');

      if (!API_BASE_URL) {
        throw new Error('Admin portal API URL not configured');
      }

      // Get the appropriate email template
      const template = await this.getEmailTemplate(emailRequest.template_type);
      if (!template) {
        throw new Error(`Email template not found for type: ${emailRequest.template_type}`);
      }

      // Process template with variables
      const processedTemplate = this.processTemplate(template, emailRequest.template_variables);

      // Prepare email payload for admin portal API
      const emailPayload = {
        to: emailRequest.to,
        to_name: emailRequest.to_name,
        subject: emailRequest.subject_override || processedTemplate.subject,
        html_content: processedTemplate.html,
        text_content: processedTemplate.text,
        template_type: emailRequest.template_type,
        template_id: template.id,
        priority: emailRequest.priority || 'normal',
        send_immediately: emailRequest.send_immediately !== false,
        variables: emailRequest.template_variables,
      };

      // Send email via admin portal API
      const response = await fetch(`${API_BASE_URL}/email/send`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...(this.authToken && { 'Authorization': `Bearer ${this.authToken}` }),
        },
        body: JSON.stringify(emailPayload),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || `Email API error: ${response.status}`);
      }

      console.log('✅ Email sent successfully via admin portal');
      return {
        success: true,
        email_id: result.email_id,
        delivery_status: result.delivery_status,
      };

    } catch (error) {
      console.error('❌ Email sending failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown email error',
      };
    }
  }

  /**
   * Send quote email notification
   */
  async sendQuoteEmail(
    customerEmail: string,
    customerName: string,
    quoteData: {
      quote_number: string;
      service_name: string;
      service_description: string;
      estimated_total: number;
      expires_at: string;
      event_date?: string;
      staff_name?: string;
    }
  ): Promise<EmailResult> {
    try {
      console.log('📧 Sending quote email notification...');

      const emailRequest: EmailRequest = {
        to: customerEmail,
        to_name: customerName,
        template_type: 'quote',
        template_variables: {
          customer_name: customerName,
          quote_number: quoteData.quote_number,
          service_name: quoteData.service_name,
          service_description: quoteData.service_description,
          estimated_total: quoteData.estimated_total.toFixed(2),
          expires_at: new Date(quoteData.expires_at).toLocaleDateString(),
          event_date: quoteData.event_date ? new Date(quoteData.event_date).toLocaleDateString() : '',
          staff_name: quoteData.staff_name || 'Ocean Soul Sparkles Team',
          business_name: 'Ocean Soul Sparkles',
          business_email: '<EMAIL>',
          business_phone: '+61 XXX XXX XXX', // Update with actual phone
          business_website: 'oceansoulsparkles.com.au',
        },
        priority: 'normal',
        send_immediately: true,
      };

      return await this.sendEmail(emailRequest);
    } catch (error) {
      console.error('❌ Quote email failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to send quote email',
      };
    }
  }

  /**
   * Send booking confirmation email
   */
  async sendBookingConfirmationEmail(
    customerEmail: string,
    customerName: string,
    bookingData: {
      booking_number: string;
      service_name: string;
      booking_date: string;
      start_time: string;
      end_time?: string;
      total_amount?: number;
      staff_name?: string;
      notes?: string;
    }
  ): Promise<EmailResult> {
    try {
      console.log('📧 Sending booking confirmation email...');

      const emailRequest: EmailRequest = {
        to: customerEmail,
        to_name: customerName,
        template_type: 'booking_confirmation',
        template_variables: {
          customer_name: customerName,
          booking_number: bookingData.booking_number,
          service_name: bookingData.service_name,
          booking_date: new Date(bookingData.booking_date).toLocaleDateString(),
          start_time: bookingData.start_time,
          end_time: bookingData.end_time || '',
          total_amount: bookingData.total_amount?.toFixed(2) || '0.00',
          staff_name: bookingData.staff_name || 'Ocean Soul Sparkles Team',
          notes: bookingData.notes || '',
          business_name: 'Ocean Soul Sparkles',
          business_email: '<EMAIL>',
          business_phone: '+61 XXX XXX XXX', // Update with actual phone
          business_website: 'oceansoulsparkles.com.au',
        },
        priority: 'normal',
        send_immediately: true,
      };

      return await this.sendEmail(emailRequest);
    } catch (error) {
      console.error('❌ Booking confirmation email failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to send booking confirmation email',
      };
    }
  }

  /**
   * Check email delivery status
   */
  async getEmailDeliveryStatus(emailId: string): Promise<EmailDeliveryStatus | null> {
    try {
      if (!API_BASE_URL) {
        throw new Error('Admin portal API URL not configured');
      }

      const response = await fetch(`${API_BASE_URL}/email/status/${emailId}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          ...(this.authToken && { 'Authorization': `Bearer ${this.authToken}` }),
        },
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || `Email status API error: ${response.status}`);
      }

      return result.delivery_status;
    } catch (error) {
      console.error('❌ Failed to get email delivery status:', error);
      return null;
    }
  }
}

// Export singleton instance
export const emailService = EmailService.getInstance();
