#!/usr/bin/env node#

const { execSync } = require('child_process');
const fs = require('fs');

console.log('🔧 Fixing Vercel environment variables...\n');

// Environment variables to set
const envVars = {
  'EXPO_PUBLIC_SUPABASE_URL': 'https://ndlgbcsbidyhxbpqzgqp.supabase.co',
  'EXPO_PUBLIC_SUPABASE_ANON_KEY': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5kbGdiY3NiaWR5aHhicHF6Z3FwIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDcwNTk5OTcsImV4cCI6MjA2MjYzNTk5N30.W8qsqYWPzTGZHu3MxRLYq4147K0CGcGxznCbe9emCzI',
  'EXPO_PUBLIC_SQUARE_APPLICATION_ID': '*****************************',
  'EXPO_PUBLIC_SQUARE_LOCATION_ID': 'LBZPW61WHXG6F',
  'EXPO_PUBLIC_ENVIRONMENT': 'production',
  'EXPO_PUBLIC_APP_NAME': 'Ocean Soul Sparkles'
};

console.log('📋 Setting environment variables...');

Object.entries(envVars).forEach(([key, value]) => {
  try {
    // Remove existing variable (ignore errors)
    try {
      execSync(`vercel env rm ${key} --yes`, { stdio: 'pipe' });
      console.log(`🗑️  Removed existing ${key}`);
    } catch (e) {
      // Variable doesn't exist, that's fine
    }

    // Add new variable
    execSync(`echo "${value}" | vercel env add ${key} production preview development`, { stdio: 'pipe' });
    console.log(`✅ Added ${key}`);
  } catch (error) {
    console.error(`❌ Failed to set ${key}:`, error.message);
  }
});

console.log('\n✅ Environment variables updated!');
console.log('\n🚀 Now try deploying again:');
console.log('   vercel --prod');