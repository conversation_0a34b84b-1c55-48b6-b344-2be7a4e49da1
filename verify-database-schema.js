/**
 * Ocean Soul Sparkles Mobile App - Database Schema Verification
 * Verifies the actual database schema matches our TypeScript interfaces
 */

const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client with hardcoded values from .env.local
const supabaseUrl = 'https://ndlgbcsbidyhxbpqzgqp.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5kbGdiY3NiaWR5aHhicHF6Z3FwIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDcwNTk5OTcsImV4cCI6MjA2MjYzNTk5N30.W8qsqYWPzTGZHu3MxRLYq4147K0CGcGxznCbe9emCzI';

console.log('🔗 Using Supabase URL:', supabaseUrl);

const supabase = createClient(supabaseUrl, supabaseKey);

async function verifyDatabaseSchema() {
  console.log('🔍 Verifying Ocean Soul Sparkles Database Schema...');
  console.log('='.repeat(60));

  try {
    // Test 1: Check if quotes table exists and get its structure
    console.log('\n📋 Test 1: Quotes table structure verification...');
    
    try {
      const { data: quotesData, error: quotesError } = await supabase
        .from('quotes')
        .select('*')
        .limit(1);

      if (quotesError) {
        console.log('❌ Quotes table access failed:', quotesError.message);
        
        // Check if it's a permission issue or table doesn't exist
        if (quotesError.message.includes('permission denied') || quotesError.message.includes('relation') || quotesError.message.includes('does not exist')) {
          console.log('💡 This suggests the quotes table might not exist or has permission issues');
        }
      } else {
        console.log('✅ Quotes table accessible');
        
        if (quotesData && quotesData.length > 0) {
          const quote = quotesData[0];
          console.log('📊 Sample quote structure:');
          console.log('   Available columns:', Object.keys(quote));
          
          // Check for amount-related properties
          const amountProps = Object.keys(quote).filter(key => 
            key.includes('amount') || key.includes('total') || key.includes('price')
          );
          console.log('💰 Amount-related properties:', amountProps);
          
          // Check for specific properties our code expects
          const expectedProps = ['estimated_total', 'service_name', 'service_description'];
          const missingProps = expectedProps.filter(prop => !(prop in quote));
          const foundProps = expectedProps.filter(prop => prop in quote);
          
          console.log('✅ Found expected properties:', foundProps);
          if (missingProps.length > 0) {
            console.log('❌ Missing expected properties:', missingProps);
          }
        } else {
          console.log('📊 No quotes found in database - testing with insert...');
          await testQuoteInsert();
        }
      }
    } catch (err) {
      console.log('❌ Quotes table test failed:', err.message);
    }

    // Test 2: Check other related tables
    console.log('\n📋 Test 2: Related tables verification...');
    
    const tablesToCheck = ['customers', 'bookings', 'services', 'admin_users'];
    
    for (const table of tablesToCheck) {
      try {
        const { data, error } = await supabase
          .from(table)
          .select('*')
          .limit(1);
        
        if (error) {
          console.log(`❌ ${table} table: ${error.message}`);
        } else {
          console.log(`✅ ${table} table: accessible`);
          if (data && data.length > 0) {
            console.log(`   Columns: ${Object.keys(data[0]).join(', ')}`);
          }
        }
      } catch (err) {
        console.log(`❌ ${table} table: ${err.message}`);
      }
    }

  } catch (error) {
    console.error('❌ Database schema verification failed:', error);
  }
}

async function testQuoteInsert() {
  console.log('\n🧪 Testing quote insert to determine schema...');
  
  // Test with total_amount
  try {
    const testQuote1 = {
      customer_id: '00000000-0000-0000-0000-000000000000',
      title: 'Schema Test Quote',
      description: 'Testing total_amount property',
      total_amount: 100.00,
      status: 'draft'
    };

    const { data: insertData1, error: insertError1 } = await supabase
      .from('quotes')
      .insert([testQuote1])
      .select('*');

    if (insertError1) {
      console.log('❌ Insert with total_amount failed:', insertError1.message);
      
      // Try with amount instead
      const testQuote2 = {
        customer_id: '00000000-0000-0000-0000-000000000000',
        title: 'Schema Test Quote 2',
        description: 'Testing amount property',
        amount: 100.00,
        status: 'draft'
      };

      const { data: insertData2, error: insertError2 } = await supabase
        .from('quotes')
        .insert([testQuote2])
        .select('*');

      if (insertError2) {
        console.log('❌ Insert with amount failed:', insertError2.message);
        
        // Try with quote_amount
        const testQuote3 = {
          customer_id: '00000000-0000-0000-0000-000000000000',
          title: 'Schema Test Quote 3',
          description: 'Testing quote_amount property',
          quote_amount: 100.00,
          status: 'draft'
        };

        const { data: insertData3, error: insertError3 } = await supabase
          .from('quotes')
          .insert([testQuote3])
          .select('*');

        if (insertError3) {
          console.log('❌ Insert with quote_amount failed:', insertError3.message);
          console.log('💡 The quotes table might have a different schema than expected');
        } else {
          console.log('✅ Insert with quote_amount succeeded!');
          console.log('📊 Actual quote structure:', Object.keys(insertData3[0]));
          // Clean up
          await supabase.from('quotes').delete().eq('id', insertData3[0].id);
        }
      } else {
        console.log('✅ Insert with amount succeeded!');
        console.log('📊 Actual quote structure:', Object.keys(insertData2[0]));
        // Clean up
        await supabase.from('quotes').delete().eq('id', insertData2[0].id);
      }
    } else {
      console.log('✅ Insert with total_amount succeeded!');
      console.log('📊 Actual quote structure:', Object.keys(insertData1[0]));
      // Clean up
      await supabase.from('quotes').delete().eq('id', insertData1[0].id);
    }
  } catch (err) {
    console.log('❌ Quote insert test failed:', err.message);
  }
}

// Run the verification
verifyDatabaseSchema()
  .then(() => {
    console.log('\n🎯 Database schema verification complete');
    console.log('='.repeat(60));
  })
  .catch(error => {
    console.error('❌ Verification script failed:', error);
  });
