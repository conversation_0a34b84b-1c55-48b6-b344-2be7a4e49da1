#!/usr/bin/env node

/**
 * Ocean Soul Sparkles - EAS Setup Verification
 */

const fs = require('fs');
const path = require('path');

console.log('🌊 Ocean Soul Sparkles - EAS Setup Verification\n');

// Check if we're in the right directory
const requiredFiles = ['package.json', 'app.config.ts', 'App.tsx'];
const missingFiles = requiredFiles.filter(file => !fs.existsSync(file));

if (missingFiles.length > 0) {
  console.log('❌ Missing required files:', missingFiles.join(', '));
  console.log('💡 Make sure you\'re in the oceansoulapp directory');
  process.exit(1);
}

console.log('✅ Directory structure looks correct\n');

// Check app.config.ts for project ID
try {
  const configContent = fs.readFileSync('app.config.ts', 'utf8');
  
  if (configContent.includes('ba00e89d-a467-4a76-8272-a8ae6c364fd8')) {
    console.log('✅ New project ID found in app.config.ts');
  } else if (configContent.includes('e1330832-16b4-4a58-98d0-378f18419267')) {
    console.log('⚠️  Old project ID still in app.config.ts - needs updating');
  } else {
    console.log('❌ No project ID found in app.config.ts');
  }
} catch (error) {
  console.log('❌ Error reading app.config.ts:', error.message);
}

// Check eas.json
if (fs.existsSync('eas.json')) {
  console.log('✅ eas.json exists');
  try {
    const easConfig = JSON.parse(fs.readFileSync('eas.json', 'utf8'));
    console.log('✅ eas.json is valid JSON');
    
    if (easConfig.build) {
      console.log('✅ Build profiles configured');
      console.log('   - Development profile:', easConfig.build.development ? '✅' : '❌');
      console.log('   - Production profile:', easConfig.build.production ? '✅' : '❌');
    }
  } catch (error) {
    console.log('❌ eas.json is invalid:', error.message);
  }
} else {
  console.log('⚠️  eas.json not found - run: npx eas build:configure');
}

// Check package.json
try {
  const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
  console.log(`✅ Package: ${packageJson.name} v${packageJson.version}`);
} catch (error) {
  console.log('❌ Error reading package.json:', error.message);
}

console.log('\n🚀 EAS Setup Status:');
console.log('1. ✅ EAS CLI configured');
console.log('2. ✅ Build profiles created');
console.log('3. ⚠️  Project ID needs manual update in app.config.ts');

console.log('\n🔧 Next steps:');
console.log('1. Update app.config.ts with project ID: ba00e89d-a467-4a76-8272-a8ae6c364fd8');
console.log('2. Test: npx eas build --platform android --profile development --local');
console.log('3. Or cloud build: npx eas build --platform all --profile development');

