/**
 * Ocean Soul Sparkles Mobile App - Customer Detail Modal Component
 * Modal for viewing customer details and booking history
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  TouchableOpacity,
  ScrollView,
  ActivityIndicator,
  Alert,
  Linking,
} from 'react-native';
import { Customer, Booking } from '@/types/database';
import { bookingService } from '@/services/database/bookingService';

interface CustomerDetailModalProps {
  visible: boolean;
  customer: Customer | null;
  onClose: () => void;
  onEdit: (customer: Customer) => void;
  onDelete: (customer: Customer) => void;
}

const CustomerDetailModal: React.FC<CustomerDetailModalProps> = ({
  visible,
  customer,
  onClose,
  onEdit,
  onDelete,
}) => {
  const [bookings, setBookings] = useState<Booking[]>([]);
  const [loadingBookings, setLoadingBookings] = useState(false);

  useEffect(() => {
    if (visible && customer) {
      loadCustomerBookings();
    }
  }, [visible, customer]);

  const loadCustomerBookings = async () => {
    if (!customer) return;

    setLoadingBookings(true);
    try {
      const result = await bookingService.getBookings({
        filters: { customer_id: customer.id },
        order_by: 'booking_date',
        order_direction: 'desc',
      });

      if (result.data) {
        setBookings(result.data);
      }
    } catch (error) {
      console.error('Error loading customer bookings:', error);
    } finally {
      setLoadingBookings(false);
    }
  };

  const handleCall = () => {
    if (customer?.phone) {
      const phoneNumber = customer.phone.replace(/[^\d+]/g, '');
      Linking.openURL(`tel:${phoneNumber}`);
    }
  };

  const handleEmail = () => {
    if (customer?.email) {
      Linking.openURL(`mailto:${customer.email}`);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'confirmed': return '#10b981';
      case 'pending': return '#f59e0b';
      case 'cancelled': return '#ef4444';
      case 'completed': return '#6366f1';
      default: return '#6b7280';
    }
  };

  const getStatusEmoji = (status: string) => {
    switch (status) {
      case 'confirmed': return '✅';
      case 'pending': return '⏳';
      case 'cancelled': return '❌';
      case 'completed': return '🎉';
      default: return '📅';
    }
  };

  const renderBookingCard = (booking: Booking) => (
    <View key={booking.id} style={styles.bookingCard}>
      <View style={styles.bookingHeader}>
        <View style={styles.bookingInfo}>
          <Text style={styles.bookingService}>
            {booking.service?.name || 'Service'}
          </Text>
          <Text style={styles.bookingDate}>
            {new Date(booking.booking_date).toLocaleDateString()} at {booking.start_time}
          </Text>
        </View>
        <View style={[styles.statusBadge, { backgroundColor: getStatusColor(booking.status) }]}>
          <Text style={styles.statusText}>
            {getStatusEmoji(booking.status)} {booking.status.toUpperCase()}
          </Text>
        </View>
      </View>
      
      {booking.staff && (
        <Text style={styles.bookingStaff}>
          👤 {booking.staff.first_name} {booking.staff.last_name}
        </Text>
      )}
      
      <View style={styles.bookingFooter}>
        <Text style={styles.bookingAmount}>
          ${booking.total_amount?.toFixed(2) || '0.00'}
        </Text>
        <Text style={styles.bookingId}>
          #{booking.id.slice(-8)}
        </Text>
      </View>
    </View>
  );

  if (!customer) {
    return null;
  }

  return (
    <Modal visible={visible} animationType="slide" presentationStyle="pageSheet">
      <View style={styles.container}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <Text style={styles.closeButtonText}>Close</Text>
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Customer Details</Text>
          <TouchableOpacity 
            onPress={() => onEdit(customer)} 
            style={styles.editButton}
          >
            <Text style={styles.editButtonText}>Edit</Text>
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {/* Customer Info Card */}
          <View style={styles.customerCard}>
            <View style={styles.customerHeader}>
              <View style={styles.customerAvatar}>
                <Text style={styles.customerInitials}>
                  {customer.full_name.split(' ').map(n => n[0]).join('').toUpperCase()}
                </Text>
              </View>
              <View style={styles.customerInfo}>
                <Text style={styles.customerName}>{customer.full_name}</Text>
                <Text style={styles.customerSince}>
                  Customer since {new Date(customer.created_at).toLocaleDateString()}
                </Text>
              </View>
            </View>

            {/* Contact Actions */}
            <View style={styles.contactActions}>
              {customer.phone && (
                <TouchableOpacity style={styles.contactButton} onPress={handleCall}>
                  <Text style={styles.contactButtonText}>📞 Call</Text>
                </TouchableOpacity>
              )}
              {customer.email && (
                <TouchableOpacity style={styles.contactButton} onPress={handleEmail}>
                  <Text style={styles.contactButtonText}>📧 Email</Text>
                </TouchableOpacity>
              )}
            </View>
          </View>

          {/* Contact Information */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Contact Information</Text>
            {customer.email && (
              <View style={styles.infoRow}>
                <Text style={styles.infoLabel}>Email:</Text>
                <Text style={styles.infoValue}>{customer.email}</Text>
              </View>
            )}
            {customer.phone && (
              <View style={styles.infoRow}>
                <Text style={styles.infoLabel}>Phone:</Text>
                <Text style={styles.infoValue}>{customer.phone}</Text>
              </View>
            )}
          </View>

          {/* Address Information */}
          {(customer.address || customer.city || customer.state || customer.postal_code) && (
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Address</Text>
              {customer.address && (
                <View style={styles.infoRow}>
                  <Text style={styles.infoLabel}>Street:</Text>
                  <Text style={styles.infoValue}>{customer.address}</Text>
                </View>
              )}
              {customer.city && (
                <View style={styles.infoRow}>
                  <Text style={styles.infoLabel}>City:</Text>
                  <Text style={styles.infoValue}>{customer.city}</Text>
                </View>
              )}
              {customer.state && (
                <View style={styles.infoRow}>
                  <Text style={styles.infoLabel}>State:</Text>
                  <Text style={styles.infoValue}>{customer.state}</Text>
                </View>
              )}
              {customer.postal_code && (
                <View style={styles.infoRow}>
                  <Text style={styles.infoLabel}>Postal Code:</Text>
                  <Text style={styles.infoValue}>{customer.postal_code}</Text>
                </View>
              )}
            </View>
          )}

          {/* Notes */}
          {customer.notes && (
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Notes</Text>
              <Text style={styles.notesText}>{customer.notes}</Text>
            </View>
          )}

          {/* Booking History */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>
              Booking History ({bookings.length})
            </Text>
            
            {loadingBookings ? (
              <View style={styles.loadingContainer}>
                <ActivityIndicator size="small" color="#FF9A8B" />
                <Text style={styles.loadingText}>Loading bookings...</Text>
              </View>
            ) : bookings.length > 0 ? (
              <View style={styles.bookingsContainer}>
                {bookings.map(renderBookingCard)}
              </View>
            ) : (
              <View style={styles.emptyBookings}>
                <Text style={styles.emptyBookingsText}>
                  No bookings found for this customer
                </Text>
              </View>
            )}
          </View>

          {/* Actions */}
          <View style={styles.actionsSection}>
            <TouchableOpacity 
              style={styles.deleteButton}
              onPress={() => onDelete(customer)}
            >
              <Text style={styles.deleteButtonText}>🗑️ Delete Customer</Text>
            </TouchableOpacity>
          </View>
        </ScrollView>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  closeButton: {
    paddingVertical: 8,
    paddingHorizontal: 12,
  },
  closeButtonText: {
    fontSize: 16,
    color: '#FF9A8B',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
  },
  editButton: {
    paddingVertical: 8,
    paddingHorizontal: 12,
  },
  editButtonText: {
    fontSize: 16,
    color: '#007AFF',
    fontWeight: '600',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  customerCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  customerHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  customerAvatar: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: '#FF9A8B',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  customerInitials: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#fff',
  },
  customerInfo: {
    flex: 1,
  },
  customerName: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  customerSince: {
    fontSize: 14,
    color: '#666',
  },
  contactActions: {
    flexDirection: 'row',
    gap: 12,
  },
  contactButton: {
    flex: 1,
    backgroundColor: '#f0f0f0',
    borderRadius: 8,
    paddingVertical: 12,
    alignItems: 'center',
  },
  contactButtonText: {
    fontSize: 16,
    color: '#333',
    fontWeight: '500',
  },
  section: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginBottom: 12,
  },
  infoRow: {
    flexDirection: 'row',
    marginBottom: 8,
  },
  infoLabel: {
    fontSize: 16,
    color: '#666',
    width: 100,
  },
  infoValue: {
    fontSize: 16,
    color: '#333',
    flex: 1,
  },
  notesText: {
    fontSize: 16,
    color: '#333',
    lineHeight: 24,
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 20,
  },
  loadingText: {
    marginLeft: 8,
    fontSize: 14,
    color: '#666',
  },
  bookingsContainer: {
    gap: 12,
  },
  bookingCard: {
    backgroundColor: '#f8f9fa',
    borderRadius: 8,
    padding: 12,
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  bookingHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  bookingInfo: {
    flex: 1,
  },
  bookingService: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 4,
  },
  bookingDate: {
    fontSize: 14,
    color: '#666',
  },
  statusBadge: {
    borderRadius: 12,
    paddingHorizontal: 8,
    paddingVertical: 4,
  },
  statusText: {
    fontSize: 12,
    color: '#fff',
    fontWeight: '600',
  },
  bookingStaff: {
    fontSize: 14,
    color: '#666',
    marginBottom: 8,
  },
  bookingFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  bookingAmount: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FF9A8B',
  },
  bookingId: {
    fontSize: 12,
    color: '#999',
  },
  emptyBookings: {
    paddingVertical: 20,
    alignItems: 'center',
  },
  emptyBookingsText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
  },
  actionsSection: {
    marginBottom: 32,
  },
  deleteButton: {
    backgroundColor: '#ffe6e6',
    borderRadius: 8,
    paddingVertical: 12,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#ffcccc',
  },
  deleteButtonText: {
    fontSize: 16,
    color: '#ef4444',
    fontWeight: '500',
  },
});

export default CustomerDetailModal;
