-- Chat Threads Table
CREATE TABLE IF NOT EXISTS chat_threads (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  booking_id UUID REFERENCES bookings(id) ON DELETE CASCADE,
  title TEXT NOT NULL,
  created_by UUID REFERENCES admin_users(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  last_message_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  participants UUID[] DEFAULT '{}',
  is_active BOOLEAN DEFAULT true
);

-- Chat Messages Table
CREATE TABLE IF NOT EXISTS chat_messages (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  thread_id UUID REFERENCES chat_threads(id) ON DELETE CASCADE,
  sender_id TEXT NOT NULL, -- Can be UUID or 'system'
  sender_name TEXT NOT NULL,
  message TEXT NOT NULL,
  message_type TEXT DEFAULT 'text' CHECK (message_type IN ('text', 'availability_response', 'system')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  read_by U<PERSON>D[] DEFAULT '{}'
);

-- Availability Responses Table
CREATE TABLE IF NOT EXISTS availability_responses (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  thread_id UUID REFERENCES chat_threads(id) ON DELETE CASCADE,
  staff_id UUID REFERENCES admin_users(id),
  staff_name TEXT NOT NULL,
  status TEXT NOT NULL CHECK (status IN ('available', 'unavailable', 'maybe')),
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(thread_id, staff_id)
);

-- Staff Notification Preferences Table
CREATE TABLE IF NOT EXISTS staff_notification_preferences (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES admin_users(id) UNIQUE,
  push_token TEXT,
  enable_booking_notifications BOOLEAN DEFAULT true,
  enable_chat_notifications BOOLEAN DEFAULT true,
  quiet_hours JSONB DEFAULT '{"enabled": false, "startTime": "22:00", "endTime": "08:00"}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS
ALTER TABLE chat_threads ENABLE ROW LEVEL SECURITY;
ALTER TABLE chat_messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE availability_responses ENABLE ROW LEVEL SECURITY;
ALTER TABLE staff_notification_preferences ENABLE ROW LEVEL SECURITY;

-- RLS Policies (staff can access threads they participate in)
CREATE POLICY "Staff can view their chat threads" ON chat_threads
  FOR SELECT USING (auth.uid() = ANY(participants));

CREATE POLICY "Staff can create chat threads" ON chat_threads
  FOR INSERT WITH CHECK (auth.uid() = created_by);

CREATE POLICY "Staff can view messages in their threads" ON chat_messages
  FOR SELECT USING (
    thread_id IN (
      SELECT id FROM chat_threads WHERE auth.uid() = ANY(participants)
    )
  );

CREATE POLICY "Staff can send messages in their threads" ON chat_messages
  FOR INSERT WITH CHECK (
    thread_id IN (
      SELECT id FROM chat_threads WHERE auth.uid() = ANY(participants)
    )
  );

-- RLS Policies for availability_responses
CREATE POLICY "Staff can view availability responses in their threads" ON availability_responses
  FOR SELECT USING (
    thread_id IN (
      SELECT id FROM chat_threads WHERE auth.uid() = ANY(participants)
    )
  );

CREATE POLICY "Staff can create availability responses in their threads" ON availability_responses
  FOR INSERT WITH CHECK (
    thread_id IN (
      SELECT id FROM chat_threads WHERE auth.uid() = ANY(participants)
    ) AND staff_id = auth.uid()
  );

CREATE POLICY "Staff can update their own availability responses" ON availability_responses
  FOR UPDATE USING (staff_id = auth.uid());

-- RLS Policies for staff_notification_preferences
CREATE POLICY "Staff can view their own notification preferences" ON staff_notification_preferences
  FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Staff can create their own notification preferences" ON staff_notification_preferences
  FOR INSERT WITH CHECK (user_id = auth.uid());

CREATE POLICY "Staff can update their own notification preferences" ON staff_notification_preferences
  FOR UPDATE USING (user_id = auth.uid());

-- Indexes for performance
CREATE INDEX idx_chat_threads_booking_id ON chat_threads(booking_id);
CREATE INDEX idx_chat_messages_thread_id ON chat_messages(thread_id);
CREATE INDEX idx_availability_responses_thread_id ON availability_responses(thread_id);