-- Ocean Soul Sparkles - Transaction System Database Schema
-- Phase 3A: Transaction & Receipt System Tables
-- Run this in your Supabase SQL Editor to create the required tables

-- =====================================================
-- TRANSACTIONS TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS public.transactions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    transaction_number VARCHAR(50) UNIQUE NOT NULL,
    customer_id UUID REFERENCES public.customers(id) ON DELETE SET NULL,
    staff_id UUID REFERENCES public.admin_users(id) ON DELETE RESTRICT NOT NULL,
    subtotal DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    tax_amount DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    total_amount DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    payment_method VARCHAR(20) NOT NULL CHECK (payment_method IN ('cash', 'card', 'digital_wallet', 'pending')),
    payment_status VARCHAR(20) NOT NULL DEFAULT 'completed' CHECK (payment_status IN ('pending', 'completed', 'failed', 'refunded')),
    square_payment_id VARCHAR(255),
    receipt_url TEXT,
    receipt_email VARCHAR(255),
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
);

-- =====================================================
-- TRANSACTION ITEMS TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS public.transaction_items (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    transaction_id UUID REFERENCES public.transactions(id) ON DELETE CASCADE NOT NULL,
    item_type VARCHAR(10) NOT NULL CHECK (item_type IN ('product', 'service')),
    item_id UUID NOT NULL, -- References either products.id or services.id
    item_name VARCHAR(255) NOT NULL, -- Stored for historical record
    quantity INTEGER NOT NULL DEFAULT 1 CHECK (quantity > 0),
    unit_price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    total_price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
);

-- =====================================================
-- INDEXES FOR PERFORMANCE
-- =====================================================
CREATE INDEX IF NOT EXISTS idx_transactions_customer_id ON public.transactions(customer_id);
CREATE INDEX IF NOT EXISTS idx_transactions_staff_id ON public.transactions(staff_id);
CREATE INDEX IF NOT EXISTS idx_transactions_created_at ON public.transactions(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_transactions_payment_status ON public.transactions(payment_status);
CREATE INDEX IF NOT EXISTS idx_transactions_transaction_number ON public.transactions(transaction_number);

CREATE INDEX IF NOT EXISTS idx_transaction_items_transaction_id ON public.transaction_items(transaction_id);
CREATE INDEX IF NOT EXISTS idx_transaction_items_item_type_id ON public.transaction_items(item_type, item_id);

-- =====================================================
-- ROW LEVEL SECURITY (RLS) POLICIES
-- =====================================================

-- Enable RLS on transactions table
ALTER TABLE public.transactions ENABLE ROW LEVEL SECURITY;

-- Policy: Users can view all transactions (for business operations)
CREATE POLICY "Users can view all transactions" ON public.transactions
    FOR SELECT USING (true);

-- Policy: Authenticated users can create transactions
CREATE POLICY "Authenticated users can create transactions" ON public.transactions
    FOR INSERT WITH CHECK (auth.role() = 'authenticated');

-- Policy: Users can update their own transactions or admins can update any
CREATE POLICY "Users can update transactions" ON public.transactions
    FOR UPDATE USING (
        staff_id = auth.uid() OR 
        EXISTS (
            SELECT 1 FROM public.admin_users 
            WHERE id = auth.uid() AND role IN ('DEV', 'Admin')
        )
    );

-- Enable RLS on transaction_items table
ALTER TABLE public.transaction_items ENABLE ROW LEVEL SECURITY;

-- Policy: Users can view all transaction items
CREATE POLICY "Users can view all transaction items" ON public.transaction_items
    FOR SELECT USING (true);

-- Policy: Authenticated users can create transaction items
CREATE POLICY "Authenticated users can create transaction items" ON public.transaction_items
    FOR INSERT WITH CHECK (auth.role() = 'authenticated');

-- Policy: Users can update transaction items for their transactions
CREATE POLICY "Users can update transaction items" ON public.transaction_items
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM public.transactions t 
            WHERE t.id = transaction_id AND (
                t.staff_id = auth.uid() OR 
                EXISTS (
                    SELECT 1 FROM public.admin_users 
                    WHERE id = auth.uid() AND role IN ('DEV', 'Admin')
                )
            )
        )
    );

-- =====================================================
-- TRIGGERS FOR AUTOMATIC UPDATES
-- =====================================================

-- Function to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Trigger to automatically update updated_at on transactions
CREATE TRIGGER update_transactions_updated_at 
    BEFORE UPDATE ON public.transactions 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- SAMPLE DATA (OPTIONAL - FOR TESTING)
-- =====================================================

-- Insert a sample transaction (uncomment to test)
/*
INSERT INTO public.transactions (
    transaction_number,
    staff_id,
    subtotal,
    tax_amount,
    total_amount,
    payment_method,
    payment_status,
    notes
) VALUES (
    'OSS241226-001',
    (SELECT id FROM public.admin_users LIMIT 1), -- Use first admin user
    100.00,
    10.00,
    110.00,
    'cash',
    'completed',
    'Sample transaction for testing'
);
*/

-- =====================================================
-- VERIFICATION QUERIES
-- =====================================================

-- Check if tables were created successfully
SELECT 
    schemaname,
    tablename,
    tableowner
FROM pg_tables 
WHERE schemaname = 'public' 
AND tablename IN ('transactions', 'transaction_items')
ORDER BY tablename;

-- Check table structure
SELECT 
    table_name,
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_schema = 'public' 
AND table_name IN ('transactions', 'transaction_items')
ORDER BY table_name, ordinal_position;

-- =====================================================
-- NOTES
-- =====================================================

/*
IMPORTANT SETUP INSTRUCTIONS:

1. Run this entire SQL script in your Supabase SQL Editor
2. Verify the tables were created by checking the "Tables" section in Supabase
3. The transaction system will now work for cash payments
4. For card/digital wallet payments, you'll need Square integration (Phase 3D)

FEATURES ENABLED:
- Complete transaction recording
- Transaction history tracking  
- Receipt generation
- Staff transaction tracking
- Customer transaction linking (optional)
- Proper security with RLS policies
- Automatic timestamp updates
- Performance optimized with indexes

NEXT STEPS:
- Test cash payment processing
- Verify transaction history displays
- Test receipt generation
- Ready for Phase 3B (Customer Management)
*/
