/**
 * Ocean Soul Sparkles Mobile App - Enhanced Booking Review Screen
 * Comprehensive booking review with distance-based pricing and quote generation
 */

import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  TextInput,
} from 'react-native';
import { Booking, Customer, Service, AdminUser } from '@/types/database';
import { bookingService } from '@/services/database/bookingService';
import { customerService } from '@/services/database/customerService';
import { serviceService } from '@/services/database/serviceService';
import { staffService } from '@/services/database/staffService';
import { distancePricingService, PricingCalculationResult } from '@/services/pricing/distancePricingService';
import { quoteEmailService } from '@/services/email/quoteEmailService';
import { quoteService } from '@/services/database/quoteService';
import { useAuth } from '@/store/authStore.minimal';
import { staffCommunicationService } from '@/services/communication/staffCommunicationService';
import { pushNotificationService } from '@/services/notifications/pushNotificationService';

interface EnhancedBookingReviewScreenProps {
  bookingId: string;
  onClose: () => void;
  onBookingUpdated: () => void;
  onQuoteGenerated?: (quoteId: string) => void;
}

const EnhancedBookingReviewScreen: React.FC<EnhancedBookingReviewScreenProps> = ({
  bookingId,
  onClose,
  onBookingUpdated,
  onQuoteGenerated,
}) => {
  // State management
  const [booking, setBooking] = useState<Booking | null>(null);
  const [customer, setCustomer] = useState<Customer | null>(null);
  const [service, setService] = useState<Service | null>(null);
  const [staff, setStaff] = useState<AdminUser | null>(null);
  const [loading, setLoading] = useState(true);
  const [calculating, setCalculating] = useState(false);
  const [generating, setGenerating] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Pricing state
  const [pricingResult, setPricingResult] = useState<PricingCalculationResult | null>(null);
  const [customPricing, setCustomPricing] = useState(false);
  const [customPrice, setCustomPrice] = useState('');
  const [pricingNotes, setPricingNotes] = useState('');

  const { user } = useAuth();
  const isMountedRef = useRef(true);

  useEffect(() => {
    isMountedRef.current = true;
    loadBookingData();

    return () => {
      isMountedRef.current = false;
    };
  }, [bookingId]);

  useEffect(() => {
    if (booking && customer && service) {
      calculateDistancePricing();
    }
  }, [booking, customer, service]);

  /**
   * Load all booking-related data
   */
  const loadBookingData = async () => {
    try {
      if (!isMountedRef.current) return;

      setLoading(true);
      setError(null);

      console.log('📋 Loading enhanced booking review data...');

      // Load booking
      const bookingResult = await bookingService.getBookingById(bookingId);
      if (bookingResult.error || !bookingResult.data) {
        throw new Error(bookingResult.error?.message || 'Booking not found');
      }

      if (!isMountedRef.current) return;
      setBooking(bookingResult.data);

      // Load customer
      const customerResult = await customerService.getCustomerById(bookingResult.data.customer_id);
      if (customerResult.error || !customerResult.data) {
        throw new Error(customerResult.error?.message || 'Customer not found');
      }

      setCustomer(customerResult.data);

      // Load service if available
      if (bookingResult.data.service_id) {
        const serviceResult = await serviceService.getServiceById(bookingResult.data.service_id);
        if (serviceResult.data) {
          setService(serviceResult.data);
        }
      }

      // Load staff if available
      if (bookingResult.data.staff_id) {
        const staffResult = await staffService.getStaffById(bookingResult.data.staff_id);
        if (staffResult.data) {
          setStaff(staffResult.data);
        }
      }

      console.log('✅ Booking review data loaded successfully');

    } catch (err) {
      console.error('❌ Failed to load booking data:', err);
      setError(err instanceof Error ? err.message : 'Failed to load booking data');
    } finally {
      setLoading(false);
    }
  };

  /**
   * Calculate distance-based pricing
   */
  const calculateDistancePricing = async () => {
    if (!booking || !customer || !service) return;

    try {
      setCalculating(true);
      console.log('💰 Calculating distance-based pricing...');

      const pricingResult = await distancePricingService.calculateBookingPricing(
        booking,
        service,
        customer
      );

      setPricingResult(pricingResult);

      if (pricingResult.success) {
        console.log('✅ Distance pricing calculated:', pricingResult.total_price);
      } else {
        console.warn('⚠️ Distance pricing calculation failed:', pricingResult.error);
      }

    } catch (err) {
      console.error('❌ Distance pricing error:', err);
    } finally {
      setCalculating(false);
    }
  };

  /**
   * Generate quote from booking with calculated pricing
   */
  const generateQuote = async () => {
    if (!booking || !customer || !service || !user) {
      Alert.alert('Error', 'Missing required data for quote generation');
      return;
    }

    try {
      setGenerating(true);
      console.log('📝 Generating quote from booking review...');

      // Determine final price
      const finalPrice = customPricing && customPrice 
        ? parseFloat(customPrice)
        : pricingResult?.total_price || service.base_price || 0;

      if (finalPrice <= 0) {
        Alert.alert('Error', 'Please set a valid price for the quote');
        return;
      }

      // Build quote description
      const quoteDescription = buildQuoteDescription(finalPrice);

      // Create quote
      const quoteResult = await quoteService.createQuote({
        customer_id: customer.id,
        created_by: user.id,
        service_name: service.name,
        service_description: quoteDescription,
        estimated_total: finalPrice,
        status: 'sent',
        expires_at: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
        notes: pricingNotes || undefined,
      });

      if (quoteResult.error || !quoteResult.data) {
        throw new Error(quoteResult.error?.message || 'Failed to create quote');
      }

      // Update booking status
      await bookingService.updateBooking(booking.id, {
        status: 'pending',
        total_amount: finalPrice,
        notes: `${booking.notes || ''}\n\n📋 Quote generated: $${finalPrice.toFixed(2)} - ${new Date().toISOString()}`,
      });

      // Send email notification
      let emailStatus = '';
      try {
        const emailResult = await quoteEmailService.sendQuoteEmail(quoteResult.data);
        emailStatus = emailResult.success 
          ? '\n\n📧 Email notification sent to customer!'
          : '\n\n⚠️ Quote created but email notification failed.';
      } catch (emailError) {
        emailStatus = '\n\n⚠️ Quote created but email notification failed.';
        console.warn('Email notification error:', emailError);
      }

      // Create chat thread for staff coordination
      const chatThread = await staffCommunicationService.createBookingChatThread(
        booking.id,
        `${customer?.name || 'Customer'} - ${service?.name || 'Service'}`
      );

      if (chatThread) {
        console.log('💬 Chat thread created for booking coordination');
        
        // Send notification to all active staff about new quote
        await pushNotificationService.sendNotificationToAllActiveStaff({
          type: 'new_booking',
          bookingId: booking.id,
          title: '📋 Quote Generated - Staff Review Needed',
          body: `Quote for ${customer?.name || 'Customer'} ready for review`,
          data: {
            bookingId: booking.id,
            action: 'review_quote',
            screen: 'BookingReview',
          },
        });
      }

      Alert.alert(
        'Quote Generated Successfully! ✅',
        `Quote for $${finalPrice.toFixed(2)} has been created and sent to ${customer.full_name}.${emailStatus}`,
        [
          {
            text: 'OK',
            onPress: () => {
              onBookingUpdated();
              if (onQuoteGenerated) {
                onQuoteGenerated(quoteResult.data!.id);
              }
              onClose();
            },
          },
        ]
      );

    } catch (err) {
      console.error('❌ Quote generation failed:', err);
      Alert.alert('Error', 'Failed to generate quote. Please try again.');
    } finally {
      setGenerating(false);
    }
  };

  /**
   * Build detailed quote description
   */
  const buildQuoteDescription = (finalPrice: number): string => {
    const lines = [`QUOTE FOR ${service?.name?.toUpperCase() || 'SERVICE'}`];
    lines.push('');

    // Service details
    lines.push(`Service: ${service?.name || 'Custom Service'}`);
    lines.push(`Date: ${new Date(booking?.booking_date || '').toLocaleDateString()}`);
    lines.push(`Time: ${booking?.start_time || 'TBD'}`);
    
    if (booking?.end_time) {
      lines.push(`Duration: ${booking.start_time} - ${booking.end_time}`);
    }

    lines.push('');

    // Pricing breakdown
    if (pricingResult?.success && !customPricing) {
      lines.push('PRICING BREAKDOWN:');
      lines.push(`• Base Service: $${pricingResult.breakdown.service_base.toFixed(2)}`);
      
      if (pricingResult.breakdown.distance_adjustment > 0) {
        lines.push(`• Distance Adjustment (${pricingResult.pricing_tier.name}): $${pricingResult.breakdown.distance_adjustment.toFixed(2)}`);
      }
      
      if (pricingResult.breakdown.travel_fee > 0) {
        lines.push(`• Travel Fee: $${pricingResult.breakdown.travel_fee.toFixed(2)}`);
      }
      
      lines.push('');
      lines.push(`Distance: ${pricingResult.distance_km}km`);
      lines.push(`Pricing Tier: ${pricingResult.pricing_tier.description}`);
    } else {
      lines.push('PRICING:');
      lines.push(`• Total Service Cost: $${finalPrice.toFixed(2)}`);
      if (customPricing) {
        lines.push('• Custom pricing applied');
      }
    }

    lines.push('');
    lines.push(`TOTAL AMOUNT: $${finalPrice.toFixed(2)}`);

    // Additional notes
    if (pricingNotes) {
      lines.push('');
      lines.push('ADDITIONAL NOTES:');
      lines.push(pricingNotes);
    }

    if (booking?.notes) {
      lines.push('');
      lines.push('BOOKING NOTES:');
      lines.push(booking.notes);
    }

    return lines.join('\n');
  };

  /**
   * Render pricing section
   */
  const renderPricingSection = () => {
    if (calculating) {
      return (
        <View style={styles.pricingCard}>
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="small" color="#FF9A8B" />
            <Text style={styles.loadingText}>Calculating distance-based pricing...</Text>
          </View>
        </View>
      );
    }

    return (
      <View style={styles.pricingCard}>
        <Text style={styles.sectionTitle}>💰 Pricing Calculation</Text>
        
        {pricingResult?.success ? (
          <View style={styles.pricingBreakdown}>
            <View style={styles.pricingRow}>
              <Text style={styles.pricingLabel}>Base Service:</Text>
              <Text style={styles.pricingValue}>${pricingResult.breakdown.service_base.toFixed(2)}</Text>
            </View>
            
            {pricingResult.breakdown.distance_adjustment > 0 && (
              <View style={styles.pricingRow}>
                <Text style={styles.pricingLabel}>Distance Adjustment ({pricingResult.pricing_tier.name}):</Text>
                <Text style={styles.pricingValue}>${pricingResult.breakdown.distance_adjustment.toFixed(2)}</Text>
              </View>
            )}
            
            {pricingResult.breakdown.travel_fee > 0 && (
              <View style={styles.pricingRow}>
                <Text style={styles.pricingLabel}>Travel Fee:</Text>
                <Text style={styles.pricingValue}>${pricingResult.breakdown.travel_fee.toFixed(2)}</Text>
              </View>
            )}
            
            <View style={[styles.pricingRow, styles.totalRow]}>
              <Text style={styles.totalLabel}>Total:</Text>
              <Text style={styles.totalValue}>${pricingResult.breakdown.total.toFixed(2)}</Text>
            </View>
            
            <Text style={styles.distanceInfo}>
              📍 Distance: {pricingResult.distance_km}km • {pricingResult.pricing_tier.description}
            </Text>
          </View>
        ) : (
          <View style={styles.pricingError}>
            <Text style={styles.errorText}>
              ⚠️ Distance pricing unavailable: {pricingResult?.error || 'Unknown error'}
            </Text>
            <Text style={styles.errorSubtext}>
              Using base service price: ${service?.base_price?.toFixed(2) || '0.00'}
            </Text>
          </View>
        )}

        {/* Custom pricing option */}
        <View style={styles.customPricingSection}>
          <View style={styles.switchRow}>
            <Text style={styles.switchLabel}>Use Custom Pricing</Text>
            <TouchableOpacity
              style={[styles.switch, customPricing && styles.switchActive]}
              onPress={() => setCustomPricing(!customPricing)}
            >
              <View style={[styles.switchThumb, customPricing && styles.switchThumbActive]} />
            </TouchableOpacity>
          </View>
          
          {customPricing && (
            <View style={styles.customPriceInput}>
              <Text style={styles.inputLabel}>Custom Price ($):</Text>
              <TextInput
                style={styles.priceInput}
                value={customPrice}
                onChangeText={setCustomPrice}
                placeholder="Enter custom price"
                keyboardType="decimal-pad"
              />
            </View>
          )}
        </View>
      </View>
    );
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#FF9A8B" />
          <Text style={styles.loadingText}>Loading booking details...</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (error) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.errorContainer}>
          <Text style={styles.errorTitle}>❌ Error</Text>
          <Text style={styles.errorText}>{error}</Text>
          <TouchableOpacity style={styles.retryButton} onPress={loadBookingData}>
            <Text style={styles.retryButtonText}>Retry</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={onClose} style={styles.backButton}>
          <Text style={styles.backButtonText}>‹ Back</Text>
        </TouchableOpacity>
        
        <Text style={styles.headerTitle}>Enhanced Booking Review</Text>
        
        <View style={styles.placeholder} />
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Booking Info */}
        <View style={styles.infoCard}>
          <Text style={styles.sectionTitle}>📅 Booking Details</Text>
          <Text style={styles.infoText}>
            <Text style={styles.infoLabel}>Customer: </Text>
            {customer?.full_name || 'Unknown'}
          </Text>
          <Text style={styles.infoText}>
            <Text style={styles.infoLabel}>Service: </Text>
            {service?.name || 'Custom Service'}
          </Text>
          <Text style={styles.infoText}>
            <Text style={styles.infoLabel}>Date: </Text>
            {new Date(booking?.booking_date || '').toLocaleDateString()}
          </Text>
          <Text style={styles.infoText}>
            <Text style={styles.infoLabel}>Time: </Text>
            {booking?.start_time || 'TBD'}
          </Text>
          {customer?.address && (
            <Text style={styles.infoText}>
              <Text style={styles.infoLabel}>Location: </Text>
              {customer.address}
            </Text>
          )}
        </View>

        {/* Pricing Section */}
        {renderPricingSection()}

        {/* Notes Section */}
        <View style={styles.notesCard}>
          <Text style={styles.sectionTitle}>📝 Additional Notes</Text>
          <TextInput
            style={styles.notesInput}
            value={pricingNotes}
            onChangeText={setPricingNotes}
            placeholder="Add any additional notes for the quote..."
            multiline
            numberOfLines={3}
          />
        </View>

        {/* Generate Quote Button */}
        <TouchableOpacity
          style={[styles.generateButton, generating && styles.generateButtonDisabled]}
          onPress={generateQuote}
          disabled={generating}
        >
          {generating ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator color="#fff" size="small" />
              <Text style={styles.generateButtonText}>Generating Quote...</Text>
            </View>
          ) : (
            <Text style={styles.generateButtonText}>✅ Generate Quote & Send Email</Text>
          )}
        </TouchableOpacity>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  backButton: {
    padding: 8,
  },
  backButtonText: {
    fontSize: 18,
    color: '#FF9A8B',
    fontWeight: '600',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
  },
  placeholder: {
    width: 40,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'row',
  },
  loadingText: {
    marginLeft: 8,
    fontSize: 16,
    color: '#666',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#ef4444',
    marginBottom: 8,
  },
  errorText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginBottom: 20,
  },
  retryButton: {
    backgroundColor: '#FF9A8B',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
  },
  retryButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  infoCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  pricingCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  notesCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginBottom: 12,
  },
  infoText: {
    fontSize: 16,
    color: '#333',
    marginBottom: 8,
  },
  infoLabel: {
    fontWeight: '600',
    color: '#FF9A8B',
  },
  pricingBreakdown: {
    marginBottom: 16,
  },
  pricingRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 4,
  },
  pricingLabel: {
    fontSize: 14,
    color: '#666',
    flex: 1,
  },
  pricingValue: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
  },
  totalRow: {
    borderTopWidth: 1,
    borderTopColor: '#e5e7eb',
    marginTop: 8,
    paddingTop: 8,
  },
  totalLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
  },
  totalValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FF9A8B',
  },
  distanceInfo: {
    fontSize: 12,
    color: '#666',
    fontStyle: 'italic',
    marginTop: 8,
  },
  pricingError: {
    backgroundColor: '#fef3c7',
    padding: 12,
    borderRadius: 8,
    marginBottom: 16,
  },
  errorSubtext: {
    fontSize: 14,
    color: '#92400e',
    marginTop: 4,
  },
  customPricingSection: {
    borderTopWidth: 1,
    borderTopColor: '#e5e7eb',
    paddingTop: 16,
    marginTop: 16,
  },
  switchRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  switchLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
  },
  switch: {
    width: 50,
    height: 30,
    borderRadius: 15,
    backgroundColor: '#e5e7eb',
    justifyContent: 'center',
    paddingHorizontal: 2,
  },
  switchActive: {
    backgroundColor: '#FF9A8B',
  },
  switchThumb: {
    width: 26,
    height: 26,
    borderRadius: 13,
    backgroundColor: '#fff',
    alignSelf: 'flex-start',
  },
  switchThumbActive: {
    alignSelf: 'flex-end',
  },
  customPriceInput: {
    marginTop: 8,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
  },
  priceInput: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    fontSize: 16,
    backgroundColor: '#fff',
  },
  notesInput: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    fontSize: 16,
    backgroundColor: '#fff',
    textAlignVertical: 'top',
    minHeight: 80,
  },
  generateButton: {
    backgroundColor: '#FF9A8B',
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
    marginBottom: 20,
  },
  generateButtonDisabled: {
    backgroundColor: '#d1d5db',
  },
  generateButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
});

export default EnhancedBookingReviewScreen;


