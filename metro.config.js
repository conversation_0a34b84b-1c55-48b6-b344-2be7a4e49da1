const { getDefaultConfig } = require('@expo/metro-config');

/** @type {import('@expo/metro-config').MetroConfig} */
const config = getDefaultConfig(__dirname);

// Enhanced Metro configuration for dual development (Expo + React Native CLI)
config.resolver.assetExts.push('bin');

// Enable Metro for React Native CLI development
config.resolver.platforms = ['ios', 'android', 'native', 'web'];

// Configure transformer for better Android Studio integration
config.transformer = {
  ...config.transformer,
  babelTransformerPath: require.resolve('@expo/metro-config/babel-transformer'),
  assetPlugins: ['expo-asset/tools/hashAssetFiles'],
};

// Server configuration for Android Studio connection
config.server = {
  ...config.server,
  port: 8081, // Standard Metro port for React Native CLI
  enhanceMiddleware: (middleware) => {
    return (req, res, next) => {
      // Enable CORS for Android Studio debugging
      res.header('Access-Control-Allow-Origin', '*');
      res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
      res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization');

      if (req.method === 'OPTIONS') {
        res.sendStatus(200);
      } else {
        return middleware(req, res, next);
      }
    };
  },
};

// Watchman configuration for better file watching
config.watchFolders = [
  __dirname,
];

// Reset cache configuration for development
config.resetCache = process.env.NODE_ENV === 'development';

module.exports = config;
