/**
 * Ocean Soul Sparkles Mobile App - iOS Notification Tester
 * Tests iOS notification features and permissions
 */

import * as Notifications from 'expo-notifications';
import { Platform } from 'react-native';
import { iOSNotificationManager } from './iOSNotificationManager';

interface iOSTestResult {
  testName: string;
  success: boolean;
  message: string;
  details?: any;
}

class iOSNotificationTester {
  
  /**
   * Run comprehensive iOS notification tests
   */
  async runAllTests(): Promise<iOSTestResult[]> {
    const results: iOSTestResult[] = [];

    if (Platform.OS !== 'ios') {
      results.push({
        testName: 'Platform Check',
        success: false,
        message: 'Tests can only run on iOS platform',
      });
      return results;
    }

    // Test 1: iOS manager initialization
    results.push(await this.testManagerInitialization());

    // Test 2: Permission request
    results.push(await this.testPermissionRequest());

    // Test 3: Notification categories
    results.push(await this.testNotificationCategories());

    // Test 4: Local notification with category
    results.push(await this.testLocalNotificationWithCategory());

    // Test 5: Interruption levels
    results.push(await this.testInterruptionLevels());

    // Test 6: Action handlers
    results.push(await this.testActionHandlers());

    return results;
  }

  /**
   * Test iOS manager initialization
   */
  private async testManagerInitialization(): Promise<iOSTestResult> {
    try {
      await iOSNotificationManager.initialize();
      
      return {
        testName: 'iOS Manager Initialization',
        success: true,
        message: 'iOS notification manager initialized successfully',
      };
    } catch (error) {
      return {
        testName: 'iOS Manager Initialization',
        success: false,
        message: `Initialization failed: ${error}`,
      };
    }
  }

  /**
   * Test permission request
   */
  private async testPermissionRequest(): Promise<iOSTestResult> {
    try {
      const settings = await iOSNotificationManager.requestPermissions({
        allowAlert: true,
        allowBadge: true,
        allowSound: true,
        allowCriticalAlerts: false,
        allowProvisional: false,
      });

      const hasBasicPermissions = settings.allowAlert && settings.allowBadge && settings.allowSound;

      return {
        testName: 'Permission Request',
        success: hasBasicPermissions,
        message: hasBasicPermissions 
          ? 'Basic iOS notification permissions granted'
          : 'Some iOS notification permissions were denied',
        details: settings,
      };
    } catch (error) {
      return {
        testName: 'Permission Request',
        success: false,
        message: `Permission request failed: ${error}`,
      };
    }
  }

  /**
   * Test notification categories
   */
  private async testNotificationCategories(): Promise<iOSTestResult> {
    try {
      const categories = iOSNotificationManager.getNotificationCategories();
      const expectedCategories = ['BOOKING_NOTIFICATION', 'STAFF_MESSAGE', 'URGENT_NOTIFICATION'];
      
      const hasAllCategories = expectedCategories.every(categoryId => 
        categories.has(categoryId)
      );

      return {
        testName: 'Notification Categories',
        success: hasAllCategories,
        message: hasAllCategories
          ? `All ${expectedCategories.length} notification categories configured`
          : 'Some notification categories are missing',
        details: {
          expected: expectedCategories,
          configured: Array.from(categories.keys()),
        },
      };
    } catch (error) {
      return {
        testName: 'Notification Categories',
        success: false,
        message: `Category test failed: ${error}`,
      };
    }
  }

  /**
   * Test local notification with category
   */
  private async testLocalNotificationWithCategory(): Promise<iOSTestResult> {
    try {
      const categoryId = 'BOOKING_NOTIFICATION';
      
      const notificationId = await Notifications.scheduleNotificationAsync({
        content: {
          title: '🧪 iOS Test Notification',
          body: 'Testing iOS notification with category and actions',
          categoryIdentifier: categoryId,
          data: { test: true },
          badge: 1,
          sound: 'default',
        },
        trigger: null, // Send immediately
        identifier: `ios_test_notification_${Date.now()}`,
      });

      return {
        testName: 'Local Notification with Category',
        success: !!notificationId,
        message: notificationId 
          ? `Test notification sent with category: ${categoryId}`
          : 'Failed to send test notification',
        details: { notificationId, categoryId },
      };
    } catch (error) {
      return {
        testName: 'Local Notification with Category',
        success: false,
        message: `Test notification failed: ${error}`,
      };
    }
  }

  /**
   * Test interruption levels
   */
  private async testInterruptionLevels(): Promise<iOSTestResult> {
    try {
      const interruptionLevels = ['passive', 'active', 'timeSensitive', 'critical'];
      const testResults: any[] = [];

      for (const level of interruptionLevels) {
        try {
          const notificationId = await Notifications.scheduleNotificationAsync({
            content: {
              title: `🧪 ${level.toUpperCase()} Test`,
              body: `Testing ${level} interruption level`,
              interruptionLevel: level as any,
              data: { test: true, interruptionLevel: level },
              badge: 1,
            },
            trigger: { seconds: 1 }, // Delay slightly
            identifier: `ios_interruption_test_${level}_${Date.now()}`,
          });

          testResults.push({
            level,
            success: !!notificationId,
            notificationId,
          });

          // Small delay between notifications
          await new Promise(resolve => setTimeout(resolve, 500));

        } catch (error) {
          testResults.push({
            level,
            success: false,
            error: error.toString(),
          });
        }
      }

      const successfulTests = testResults.filter(result => result.success);

      return {
        testName: 'Interruption Levels',
        success: successfulTests.length === interruptionLevels.length,
        message: `${successfulTests.length}/${interruptionLevels.length} interruption levels tested successfully`,
        details: testResults,
      };
    } catch (error) {
      return {
        testName: 'Interruption Levels',
        success: false,
        message: `Interruption level test failed: ${error}`,
      };
    }
  }

  /**
   * Test action handlers setup
   */
  private async testActionHandlers(): Promise<iOSTestResult> {
    try {
      // Set up action handlers
      iOSNotificationManager.setupNotificationActionHandlers();

      // Send a notification with actions
      const notificationId = await Notifications.scheduleNotificationAsync({
        content: {
          title: '🧪 Action Test',
          body: 'Test notification with actions - try the action buttons!',
          categoryIdentifier: 'BOOKING_NOTIFICATION',
          data: { 
            test: true, 
            bookingId: 'test-booking-123',
            actionTest: true,
          },
          badge: 1,
        },
        trigger: null,
        identifier: `ios_action_test_${Date.now()}`,
      });

      return {
        testName: 'Action Handlers',
        success: !!notificationId,
        message: notificationId
          ? 'Action handlers configured and test notification sent'
          : 'Failed to send action test notification',
        details: { notificationId },
      };
    } catch (error) {
      return {
        testName: 'Action Handlers',
        success: false,
        message: `Action handler test failed: ${error}`,
      };
    }
  }

  /**
   * Test category mapping
   */
  async testCategoryMapping(): Promise<iOSTestResult> {
    try {
      const testCases = [
        { type: 'new_booking', expectedCategory: 'BOOKING_NOTIFICATION' },
        { type: 'staff_message', expectedCategory: 'STAFF_MESSAGE' },
        { type: 'urgent_booking', expectedCategory: 'URGENT_NOTIFICATION' },
        { type: 'booking_update', expectedCategory: 'BOOKING_NOTIFICATION' },
        { type: 'emergency', expectedCategory: 'URGENT_NOTIFICATION' },
      ];

      const results = testCases.map(testCase => {
        const actualCategory = iOSNotificationManager.getCategoryForNotificationType(testCase.type);
        return {
          type: testCase.type,
          expected: testCase.expectedCategory,
          actual: actualCategory,
          correct: actualCategory === testCase.expectedCategory,
        };
      });

      const allCorrect = results.every(result => result.correct);
      const incorrectMappings = results.filter(result => !result.correct);

      return {
        testName: 'Category Mapping',
        success: allCorrect,
        message: allCorrect
          ? `All ${testCases.length} category mappings are correct`
          : `${incorrectMappings.length} incorrect mappings found`,
        details: { results, incorrectMappings },
      };
    } catch (error) {
      return {
        testName: 'Category Mapping',
        success: false,
        message: `Category mapping test failed: ${error}`,
      };
    }
  }

  /**
   * Send test notifications for all categories
   */
  async sendTestNotificationsForAllCategories(): Promise<iOSTestResult[]> {
    const results: iOSTestResult[] = [];

    if (Platform.OS !== 'ios') {
      results.push({
        testName: 'Platform Check',
        success: false,
        message: 'Test notifications can only be sent on iOS',
      });
      return results;
    }

    const categories = iOSNotificationManager.getNotificationCategories();
    
    for (const [categoryId, categoryConfig] of categories) {
      try {
        const notificationId = await Notifications.scheduleNotificationAsync({
          content: {
            title: `🧪 Test: ${categoryConfig.identifier}`,
            body: `Testing iOS category: ${categoryId}`,
            categoryIdentifier: categoryId,
            data: { 
              test: true, 
              categoryId,
              timestamp: Date.now(),
            },
            badge: 1,
            sound: 'default',
          },
          trigger: null,
          identifier: `ios_category_test_${categoryId}_${Date.now()}`,
        });

        results.push({
          testName: `Test Category: ${categoryId}`,
          success: !!notificationId,
          message: notificationId 
            ? `Sent to category ${categoryId}`
            : `Failed to send to category ${categoryId}`,
          details: { categoryId, notificationId },
        });

        // Small delay between notifications
        await new Promise(resolve => setTimeout(resolve, 1000));

      } catch (error) {
        results.push({
          testName: `Test Category: ${categoryId}`,
          success: false,
          message: `Failed: ${error}`,
          details: { categoryId },
        });
      }
    }

    return results;
  }

  /**
   * Get iOS notification health status
   */
  getNotificationHealth(): {
    isHealthy: boolean;
    permissions: any;
    categories: number;
    recommendations: string[];
  } {
    const recommendations: string[] = [];
    let isHealthy = true;

    // Check categories
    const categories = iOSNotificationManager.getNotificationCategories();
    const expectedCategoryCount = 3; // BOOKING_NOTIFICATION, STAFF_MESSAGE, URGENT_NOTIFICATION
    
    if (categories.size < expectedCategoryCount) {
      isHealthy = false;
      recommendations.push(`Missing ${expectedCategoryCount - categories.size} notification categories`);
    }

    if (Platform.OS !== 'ios') {
      isHealthy = false;
      recommendations.push('iOS notification features only available on iOS platform');
    }

    if (recommendations.length === 0) {
      recommendations.push('All iOS notification features are properly configured');
    }

    return {
      isHealthy,
      permissions: null, // Would need to be checked asynchronously
      categories: categories.size,
      recommendations,
    };
  }
}

// Export singleton instance
export const iOSNotificationTester = new iOSNotificationTester();
