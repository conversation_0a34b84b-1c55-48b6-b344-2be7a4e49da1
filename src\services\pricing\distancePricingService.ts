/**
 * Ocean Soul Sparkles Mobile App - Distance-Based Pricing Service
 * Calculates pricing based on distance/kilometres for accurate quote generation
 */

import { Service, Customer, Booking } from '@/types/database';

export interface LocationData {
  address: string;
  city?: string;
  state?: string;
  postal_code?: string;
  latitude?: number;
  longitude?: number;
}

export interface DistanceCalculationResult {
  success: boolean;
  distance_km?: number;
  travel_time_minutes?: number;
  error?: string;
  calculation_method: 'coordinates' | 'postal_code' | 'estimated';
}

export interface PricingTier {
  name: string;
  min_distance_km: number;
  max_distance_km: number;
  base_multiplier: number;
  travel_fee: number;
  description: string;
}

export interface PricingCalculationResult {
  success: boolean;
  base_price: number;
  distance_km: number;
  pricing_tier: PricingTier;
  travel_fee: number;
  distance_multiplier: number;
  total_price: number;
  breakdown: {
    service_base: number;
    distance_adjustment: number;
    travel_fee: number;
    subtotal: number;
    total: number;
  };
  error?: string;
}

export class DistancePricingService {
  private static instance: DistancePricingService;

  // Ocean Soul Sparkles business location (update with actual coordinates)
  private readonly BUSINESS_LOCATION = {
    latitude: -33.8688, // Sydney coordinates as placeholder
    longitude: 151.2093,
    address: 'Ocean Soul Sparkles Studio, Sydney, NSW, Australia',
  };

  // Pricing tiers based on distance
  private readonly PRICING_TIERS: PricingTier[] = [
    {
      name: 'Local',
      min_distance_km: 0,
      max_distance_km: 10,
      base_multiplier: 1.0,
      travel_fee: 0,
      description: 'Within 10km - No additional charges',
    },
    {
      name: 'Metro',
      min_distance_km: 10,
      max_distance_km: 25,
      base_multiplier: 1.1,
      travel_fee: 25,
      description: '10-25km - 10% surcharge + $25 travel fee',
    },
    {
      name: 'Regional',
      min_distance_km: 25,
      max_distance_km: 50,
      base_multiplier: 1.2,
      travel_fee: 50,
      description: '25-50km - 20% surcharge + $50 travel fee',
    },
    {
      name: 'Extended',
      min_distance_km: 50,
      max_distance_km: 100,
      base_multiplier: 1.3,
      travel_fee: 100,
      description: '50-100km - 30% surcharge + $100 travel fee',
    },
    {
      name: 'Long Distance',
      min_distance_km: 100,
      max_distance_km: 999999,
      base_multiplier: 1.5,
      travel_fee: 200,
      description: '100km+ - 50% surcharge + $200 travel fee',
    },
  ];

  private constructor() {}

  public static getInstance(): DistancePricingService {
    if (!DistancePricingService.instance) {
      DistancePricingService.instance = new DistancePricingService();
    }
    return DistancePricingService.instance;
  }

  /**
   * Calculate distance between business location and customer location
   */
  async calculateDistance(customerLocation: LocationData): Promise<DistanceCalculationResult> {
    try {
      console.log('📍 Calculating distance to customer location...');

      // Method 1: Use coordinates if available
      if (customerLocation.latitude && customerLocation.longitude) {
        const distance = this.calculateHaversineDistance(
          this.BUSINESS_LOCATION.latitude,
          this.BUSINESS_LOCATION.longitude,
          customerLocation.latitude,
          customerLocation.longitude
        );

        return {
          success: true,
          distance_km: Math.round(distance * 10) / 10, // Round to 1 decimal place
          travel_time_minutes: this.estimateTravelTime(distance),
          calculation_method: 'coordinates',
        };
      }

      // Method 2: Estimate based on postal code (Australian postal codes)
      if (customerLocation.postal_code) {
        const estimatedDistance = this.estimateDistanceFromPostalCode(customerLocation.postal_code);
        if (estimatedDistance > 0) {
          return {
            success: true,
            distance_km: estimatedDistance,
            travel_time_minutes: this.estimateTravelTime(estimatedDistance),
            calculation_method: 'postal_code',
          };
        }
      }

      // Method 3: Rough estimate based on city/state
      if (customerLocation.city && customerLocation.state) {
        const estimatedDistance = this.estimateDistanceFromCity(
          customerLocation.city,
          customerLocation.state
        );
        
        return {
          success: true,
          distance_km: estimatedDistance,
          travel_time_minutes: this.estimateTravelTime(estimatedDistance),
          calculation_method: 'estimated',
        };
      }

      return {
        success: false,
        error: 'Insufficient location data for distance calculation',
        calculation_method: 'estimated',
      };

    } catch (error) {
      console.error('❌ Distance calculation error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Distance calculation failed',
        calculation_method: 'estimated',
      };
    }
  }

  /**
   * Calculate pricing based on service and distance
   */
  async calculatePricing(
    service: Service,
    customerLocation: LocationData
  ): Promise<PricingCalculationResult> {
    try {
      console.log('💰 Calculating distance-based pricing...');

      // Get base price from service
      const basePrice = service.base_price || 0;
      if (basePrice <= 0) {
        return {
          success: false,
          error: 'Service has no base price set',
          base_price: 0,
          distance_km: 0,
          pricing_tier: this.PRICING_TIERS[0],
          travel_fee: 0,
          distance_multiplier: 1,
          total_price: 0,
          breakdown: {
            service_base: 0,
            distance_adjustment: 0,
            travel_fee: 0,
            subtotal: 0,
            total: 0,
          },
        };
      }

      // Calculate distance
      const distanceResult = await this.calculateDistance(customerLocation);
      if (!distanceResult.success || distanceResult.distance_km === undefined) {
        // Use default local pricing if distance calculation fails
        const localTier = this.PRICING_TIERS[0];
        return this.buildPricingResult(basePrice, 0, localTier, true);
      }

      // Determine pricing tier
      const pricingTier = this.getPricingTier(distanceResult.distance_km);

      // Calculate final pricing
      return this.buildPricingResult(basePrice, distanceResult.distance_km, pricingTier, false);

    } catch (error) {
      console.error('❌ Pricing calculation error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Pricing calculation failed',
        base_price: 0,
        distance_km: 0,
        pricing_tier: this.PRICING_TIERS[0],
        travel_fee: 0,
        distance_multiplier: 1,
        total_price: 0,
        breakdown: {
          service_base: 0,
          distance_adjustment: 0,
          travel_fee: 0,
          subtotal: 0,
          total: 0,
        },
      };
    }
  }

  /**
   * Calculate pricing for booking with customer data
   */
  async calculateBookingPricing(
    booking: Booking,
    service: Service,
    customer: Customer
  ): Promise<PricingCalculationResult> {
    const customerLocation: LocationData = {
      address: customer.address || '',
      city: customer.city,
      state: customer.state,
      postal_code: customer.postal_code,
    };

    return this.calculatePricing(service, customerLocation);
  }

  /**
   * Get pricing tier based on distance
   */
  private getPricingTier(distanceKm: number): PricingTier {
    for (const tier of this.PRICING_TIERS) {
      if (distanceKm >= tier.min_distance_km && distanceKm < tier.max_distance_km) {
        return tier;
      }
    }
    // Default to last tier for very long distances
    return this.PRICING_TIERS[this.PRICING_TIERS.length - 1];
  }

  /**
   * Build pricing calculation result
   */
  private buildPricingResult(
    basePrice: number,
    distanceKm: number,
    pricingTier: PricingTier,
    isEstimate: boolean
  ): PricingCalculationResult {
    const serviceBase = basePrice;
    const distanceAdjustment = basePrice * (pricingTier.base_multiplier - 1);
    const travelFee = pricingTier.travel_fee;
    const subtotal = serviceBase + distanceAdjustment;
    const total = subtotal + travelFee;

    return {
      success: true,
      base_price: basePrice,
      distance_km: distanceKm,
      pricing_tier: pricingTier,
      travel_fee: travelFee,
      distance_multiplier: pricingTier.base_multiplier,
      total_price: Math.round(total * 100) / 100, // Round to 2 decimal places
      breakdown: {
        service_base: Math.round(serviceBase * 100) / 100,
        distance_adjustment: Math.round(distanceAdjustment * 100) / 100,
        travel_fee: travelFee,
        subtotal: Math.round(subtotal * 100) / 100,
        total: Math.round(total * 100) / 100,
      },
    };
  }

  /**
   * Calculate distance using Haversine formula
   */
  private calculateHaversineDistance(
    lat1: number,
    lon1: number,
    lat2: number,
    lon2: number
  ): number {
    const R = 6371; // Earth's radius in kilometers
    const dLat = this.toRadians(lat2 - lat1);
    const dLon = this.toRadians(lon2 - lon1);
    
    const a = 
      Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos(this.toRadians(lat1)) * Math.cos(this.toRadians(lat2)) *
      Math.sin(dLon / 2) * Math.sin(dLon / 2);
    
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    return R * c;
  }

  /**
   * Convert degrees to radians
   */
  private toRadians(degrees: number): number {
    return degrees * (Math.PI / 180);
  }

  /**
   * Estimate travel time based on distance
   */
  private estimateTravelTime(distanceKm: number): number {
    // Rough estimate: 50km/h average speed including traffic
    return Math.round(distanceKm * 1.2); // 1.2 minutes per km
  }

  /**
   * Estimate distance from Australian postal code
   */
  private estimateDistanceFromPostalCode(postalCode: string): number {
    const code = parseInt(postalCode);
    
    // Sydney metro area postal codes (rough estimates)
    if (code >= 2000 && code <= 2099) return 5;   // Sydney CBD
    if (code >= 2100 && code <= 2199) return 15;  // Inner suburbs
    if (code >= 2200 && code <= 2299) return 25;  // Outer suburbs
    if (code >= 2300 && code <= 2399) return 35;  // Extended suburbs
    
    // NSW regional areas
    if (code >= 2400 && code <= 2599) return 100; // Central Coast/Hunter
    if (code >= 2600 && code <= 2699) return 150; // Southern NSW
    if (code >= 2700 && code <= 2899) return 200; // Western NSW
    
    // Other states (rough estimates)
    if (code >= 3000 && code <= 3999) return 800; // Victoria
    if (code >= 4000 && code <= 4999) return 1000; // Queensland
    if (code >= 5000 && code <= 5999) return 1200; // South Australia
    if (code >= 6000 && code <= 6999) return 3000; // Western Australia
    if (code >= 7000 && code <= 7999) return 1500; // Tasmania
    
    return 50; // Default estimate
  }

  /**
   * Estimate distance from city/state
   */
  private estimateDistanceFromCity(city: string, state: string): number {
    const cityLower = city.toLowerCase();
    const stateLower = state.toLowerCase();
    
    // Sydney and surrounds
    if (stateLower.includes('nsw') || stateLower.includes('new south wales')) {
      if (cityLower.includes('sydney')) return 10;
      if (cityLower.includes('parramatta')) return 25;
      if (cityLower.includes('penrith')) return 50;
      if (cityLower.includes('newcastle')) return 150;
      if (cityLower.includes('wollongong')) return 80;
      return 100; // Default NSW
    }
    
    // Other major cities
    if (cityLower.includes('melbourne')) return 800;
    if (cityLower.includes('brisbane')) return 1000;
    if (cityLower.includes('adelaide')) return 1200;
    if (cityLower.includes('perth')) return 3000;
    if (cityLower.includes('hobart')) return 1500;
    if (cityLower.includes('darwin')) return 3500;
    if (cityLower.includes('canberra')) return 300;
    
    return 200; // Default estimate
  }

  /**
   * Get all pricing tiers for display
   */
  getPricingTiers(): PricingTier[] {
    return [...this.PRICING_TIERS];
  }

  /**
   * Format pricing breakdown for display
   */
  formatPricingBreakdown(result: PricingCalculationResult): string {
    if (!result.success) {
      return `Pricing calculation failed: ${result.error}`;
    }

    const lines = [
      `Service Base Price: $${result.breakdown.service_base.toFixed(2)}`,
    ];

    if (result.breakdown.distance_adjustment > 0) {
      lines.push(
        `Distance Adjustment (${result.pricing_tier.name}): $${result.breakdown.distance_adjustment.toFixed(2)}`
      );
    }

    if (result.breakdown.travel_fee > 0) {
      lines.push(`Travel Fee: $${result.breakdown.travel_fee.toFixed(2)}`);
    }

    lines.push(`Total: $${result.breakdown.total.toFixed(2)}`);
    lines.push(`Distance: ${result.distance_km}km (${result.pricing_tier.description})`);

    return lines.join('\n');
  }
}

// Export singleton instance
export const distancePricingService = DistancePricingService.getInstance();
