/**
 * Ocean Soul Sparkles Mobile App - Database Schema Debug Script
 * Investigates the actual database schema to identify property mismatches
 */

import { supabase } from '../src/services/database/supabase';

/**
 * Debug the quotes table schema
 */
async function debugQuotesTableSchema() {
  console.log('🔍 Debugging quotes table schema...');

  try {
    // Test 1: Try to get quotes table structure
    console.log('\n📋 Test 1: Basic quotes table access...');
    const { data: quotesData, error: quotesError } = await supabase
      .from('quotes')
      .select('*')
      .limit(1);

    if (quotesError) {
      console.error('❌ Quotes table error:', quotesError);
      console.log('🔍 Trying alternative table names...');
      
      // Try alternative table names
      const alternativeNames = ['quote', 'estimates', 'proposals'];
      for (const tableName of alternativeNames) {
        try {
          const { data, error } = await supabase
            .from(tableName)
            .select('*')
            .limit(1);
          
          if (!error) {
            console.log(`✅ Found alternative table: ${tableName}`);
            console.log('Sample data:', data);
            return;
          }
        } catch (err) {
          console.log(`❌ Table '${tableName}' not found`);
        }
      }
    } else {
      console.log('✅ Quotes table accessible');
      console.log('Sample quote data:', quotesData);
      
      if (quotesData && quotesData.length > 0) {
        console.log('\n📊 Quote object properties:');
        Object.keys(quotesData[0]).forEach(key => {
          console.log(`  - ${key}: ${typeof quotesData[0][key]} = ${quotesData[0][key]}`);
        });
      }
    }

    // Test 2: Try to create a test quote to see what properties are expected
    console.log('\n📝 Test 2: Testing quote creation with different property names...');
    
    // Test with total_amount
    try {
      const testQuoteWithTotalAmount = {
        customer_id: '00000000-0000-0000-0000-000000000000', // Dummy UUID
        title: 'Test Quote - Total Amount',
        description: 'Testing total_amount property',
        total_amount: 100.00,
        status: 'draft',
      };

      const { data: createData1, error: createError1 } = await supabase
        .from('quotes')
        .insert([testQuoteWithTotalAmount])
        .select('*');

      if (createError1) {
        console.log('❌ total_amount property error:', createError1.message);
      } else {
        console.log('✅ total_amount property works');
        // Clean up test data
        if (createData1 && createData1.length > 0) {
          await supabase.from('quotes').delete().eq('id', createData1[0].id);
        }
      }
    } catch (err) {
      console.log('❌ total_amount test failed:', err);
    }

    // Test with amount
    try {
      const testQuoteWithAmount = {
        customer_id: '00000000-0000-0000-0000-000000000000', // Dummy UUID
        title: 'Test Quote - Amount',
        description: 'Testing amount property',
        amount: 100.00,
        status: 'draft',
      };

      const { data: createData2, error: createError2 } = await supabase
        .from('quotes')
        .insert([testQuoteWithAmount])
        .select('*');

      if (createError2) {
        console.log('❌ amount property error:', createError2.message);
      } else {
        console.log('✅ amount property works');
        // Clean up test data
        if (createData2 && createData2.length > 0) {
          await supabase.from('quotes').delete().eq('id', createData2[0].id);
        }
      }
    } catch (err) {
      console.log('❌ amount test failed:', err);
    }

    // Test with quote_amount
    try {
      const testQuoteWithQuoteAmount = {
        customer_id: '00000000-0000-0000-0000-000000000000', // Dummy UUID
        title: 'Test Quote - Quote Amount',
        description: 'Testing quote_amount property',
        quote_amount: 100.00,
        status: 'draft',
      };

      const { data: createData3, error: createError3 } = await supabase
        .from('quotes')
        .insert([testQuoteWithQuoteAmount])
        .select('*');

      if (createError3) {
        console.log('❌ quote_amount property error:', createError3.message);
      } else {
        console.log('✅ quote_amount property works');
        // Clean up test data
        if (createData3 && createData3.length > 0) {
          await supabase.from('quotes').delete().eq('id', createData3[0].id);
        }
      }
    } catch (err) {
      console.log('❌ quote_amount test failed:', err);
    }

    // Test 3: Check table schema using information_schema
    console.log('\n🗄️ Test 3: Checking table schema...');
    try {
      const { data: schemaData, error: schemaError } = await supabase
        .rpc('get_table_schema', { table_name: 'quotes' });

      if (schemaError) {
        console.log('❌ Schema query error:', schemaError.message);
      } else {
        console.log('✅ Table schema:', schemaData);
      }
    } catch (err) {
      console.log('❌ Schema query not available');
    }

  } catch (error) {
    console.error('❌ Debug script error:', error);
  }
}

/**
 * Debug the app registration issue
 */
async function debugAppRegistration() {
  console.log('\n📱 Debugging app registration...');

  try {
    // Check if we're in the right environment
    console.log('Environment check:');
    console.log('- Platform:', process.env.EXPO_PUBLIC_PLATFORM || 'unknown');
    console.log('- Environment:', process.env.EXPO_PUBLIC_ENVIRONMENT || 'unknown');
    
    // Check if required modules are available
    console.log('\nModule availability check:');
    
    try {
      const expo = require('expo');
      console.log('✅ Expo module available:', !!expo);
    } catch (err) {
      console.log('❌ Expo module not available');
    }

    try {
      const { registerRootComponent } = require('expo');
      console.log('✅ registerRootComponent available:', !!registerRootComponent);
    } catch (err) {
      console.log('❌ registerRootComponent not available');
    }

    try {
      const App = require('../App');
      console.log('✅ App component available:', !!App);
      console.log('App export type:', typeof App.default);
    } catch (err) {
      console.log('❌ App component not available:', err.message);
    }

  } catch (error) {
    console.error('❌ App registration debug error:', error);
  }
}

/**
 * Main debug function
 */
async function runDatabaseDebug() {
  console.log('🚨 Ocean Soul Sparkles - Database Schema Debug');
  console.log('='.repeat(50));

  await debugQuotesTableSchema();
  await debugAppRegistration();

  console.log('\n✅ Debug script completed');
}

// Run if executed directly
if (require.main === module) {
  runDatabaseDebug().catch(console.error);
}

export { debugQuotesTableSchema, debugAppRegistration, runDatabaseDebug };
