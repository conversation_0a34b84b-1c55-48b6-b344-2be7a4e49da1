/**
 * Ocean Soul Sparkles Mobile App - Module Import Debugging
 * Systematically test each module import to isolate the total_amount property error
 */

console.log('🔍 Starting systematic module import debugging...');

// Test imports one by one to isolate the error
const testModuleImport = async (moduleName, importPath) => {
  try {
    console.log(`\n📦 Testing import: ${moduleName}`);
    console.log(`   Path: ${importPath}`);
    
    const startTime = Date.now();
    const module = require(importPath);
    const endTime = Date.now();
    
    console.log(`✅ ${moduleName} imported successfully (${endTime - startTime}ms)`);
    return { success: true, module, duration: endTime - startTime };
  } catch (error) {
    console.log(`❌ ${moduleName} import failed:`, error.message);
    console.log(`   Stack trace:`, error.stack);
    return { success: false, error: error.message, stack: error.stack };
  }
};

const runModuleTests = async () => {
  console.log('='.repeat(60));
  console.log('SYSTEMATIC MODULE IMPORT TESTING');
  console.log('='.repeat(60));

  // Test 1: Core database types
  await testModuleImport('Database Types', './src/types/database');

  // Test 2: Supabase client
  await testModuleImport('Supabase Client', './src/services/database/supabase');

  // Test 3: Quote Service
  await testModuleImport('Quote Service', './src/services/database/quoteService');

  // Test 4: Email Templates
  await testModuleImport('Email Templates', './src/services/email/emailTemplates');

  // Test 5: Email Service
  await testModuleImport('Email Service', './src/services/email/emailService');

  // Test 6: Quote Email Service
  await testModuleImport('Quote Email Service', './src/services/email/quoteEmailService');

  // Test 7: Booking Service
  await testModuleImport('Booking Service', './src/services/database/bookingService');

  // Test 8: Booking Workflow Service
  await testModuleImport('Booking Workflow Service', './src/services/workflow/bookingWorkflowService');

  // Test 9: Website Booking Service
  await testModuleImport('Website Booking Service', './src/services/booking/websiteBookingService');

  // Test 10: Distance Pricing Service
  await testModuleImport('Distance Pricing Service', './src/services/pricing/distancePricingService');

  // Test 11: Auth Store
  await testModuleImport('Auth Store', './src/store/authStore');

  // Test 12: Navigation Components
  await testModuleImport('Role Based Navigation', './src/components/navigation/RoleBasedNavigation');

  // Test 13: Main App Component
  await testModuleImport('App Component', './App');

  console.log('\n='.repeat(60));
  console.log('MODULE IMPORT TESTING COMPLETE');
  console.log('='.repeat(60));
  console.log('If any module failed above, that\'s likely where the total_amount property error is occurring.');
};

// Test specific quote object creation
const testQuoteObjectCreation = () => {
  console.log('\n🏗️ Testing Quote object creation...');
  
  try {
    // Test creating a quote object with the expected structure
    const testQuote = {
      id: 'test-quote-id',
      customer_id: 'test-customer-id',
      customer_first_name: 'John',
      customer_last_name: 'Doe',
      customer_email: '<EMAIL>',
      customer_phone: '+61123456789',
      service_name: 'Test Quote',
      service_description: 'Test quote description',
      estimated_total: 150.00,
      status: 'draft',
      expires_at: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
      notes: 'Test quote',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };

    // Test property access
    console.log('✅ Quote object created successfully');
    console.log(`✅ estimated_total access: ${testQuote.estimated_total}`);
    console.log(`✅ service_name access: ${testQuote.service_name}`);
    console.log(`✅ service_description access: ${testQuote.service_description}`);

    return { success: true, quote: testQuote };
  } catch (error) {
    console.log('❌ Quote object creation failed:', error.message);
    return { success: false, error: error.message };
  }
};

// Test database schema verification
const testDatabaseSchemaAccess = async () => {
  console.log('\n🗄️ Testing database schema access...');
  
  try {
    // Try to import supabase and test a simple query
    const { supabase } = require('./src/services/database/supabase');
    
    console.log('✅ Supabase client imported successfully');
    
    // Try a simple query to test database connectivity
    const { data, error } = await supabase
      .from('quotes')
      .select('*')
      .limit(1);
    
    if (error) {
      console.log('⚠️ Database query failed:', error.message);
      console.log('   This might indicate a schema mismatch');
      return { success: false, error: error.message };
    } else {
      console.log('✅ Database query successful');
      if (data && data.length > 0) {
        console.log('📊 Sample quote structure:', Object.keys(data[0]));
        console.log('🔍 Has total_amount?', 'total_amount' in data[0]);
        console.log('🔍 Has title?', 'title' in data[0]);
        console.log('🔍 Has description?', 'description' in data[0]);
      } else {
        console.log('📊 No quotes found in database');
      }
      return { success: true, data };
    }
  } catch (error) {
    console.log('❌ Database schema test failed:', error.message);
    return { success: false, error: error.message };
  }
};

// Main execution
const main = async () => {
  console.log('🌊 Ocean Soul Sparkles - Module Import Debugging');
  console.log('Timestamp:', new Date().toISOString());
  console.log('');

  // Test 1: Quote object creation
  testQuoteObjectCreation();

  // Test 2: Database schema access
  await testDatabaseSchemaAccess();

  // Test 3: Systematic module imports
  await runModuleTests();

  console.log('\n🎯 Debugging complete. Check the output above for any failed imports.');
  console.log('The module that fails is likely where the total_amount property error is occurring.');
};

// Run the debugging
main().catch(error => {
  console.error('❌ Debugging script failed:', error);
});
