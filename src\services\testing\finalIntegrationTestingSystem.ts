/**
 * Ocean Soul Sparkles Mobile App - Final Integration Testing System
 * Comprehensive integration testing across all implemented validation systems
 */

import { productionReadinessValidator } from '@/services/validation/productionReadinessValidator';
import { distancePricingValidator } from '@/services/validation/distancePricingValidator';
import { databaseIntegrationValidator } from '@/services/validation/databaseIntegrationValidator';
import { adminPortalIntegrationValidator } from '@/services/validation/adminPortalIntegrationValidator';
import { emailService } from '@/services/email/emailService';
import { distancePricingService } from '@/services/pricing/distancePricingService';
import { sqlFunctionsService } from '@/services/database/sqlFunctionsService';
import { realTimeDataSyncService } from '@/services/adminPortal/realTimeDataSyncService';

export interface IntegrationTestResult {
  testSuite: string;
  testName: string;
  status: 'pass' | 'fail' | 'warning';
  message: string;
  duration: number;
  details?: any;
  error?: string;
}

export interface SystemIntegrationStatus {
  bookingWorkflow: boolean;
  emailNotifications: boolean;
  distancePricing: boolean;
  databaseIntegration: boolean;
  adminPortalSync: boolean;
  productionReadiness: boolean;
  overall: boolean;
}

export interface FinalIntegrationReport {
  timestamp: string;
  overallStatus: 'production_ready' | 'needs_attention' | 'not_ready';
  systemStatus: SystemIntegrationStatus;
  testResults: IntegrationTestResult[];
  performanceMetrics: {
    totalTests: number;
    passedTests: number;
    failedTests: number;
    warningTests: number;
    successRate: number;
    totalDuration: number;
    averageTestTime: number;
  };
  criticalIssues: string[];
  recommendations: string[];
  deploymentReadiness: {
    score: number;
    blockers: string[];
    warnings: string[];
    readyForProduction: boolean;
  };
}

export class FinalIntegrationTestingSystem {
  private static instance: FinalIntegrationTestingSystem;

  private constructor() {}

  public static getInstance(): FinalIntegrationTestingSystem {
    if (!FinalIntegrationTestingSystem.instance) {
      FinalIntegrationTestingSystem.instance = new FinalIntegrationTestingSystem();
    }
    return FinalIntegrationTestingSystem.instance;
  }

  /**
   * Test Enhanced Booking-to-Quote Workflow integration
   */
  async testBookingWorkflowIntegration(): Promise<IntegrationTestResult[]> {
    const results: IntegrationTestResult[] = [];

    // Test booking workflow with distance pricing integration
    const workflowStartTime = Date.now();
    try {
      console.log('📅 Testing Enhanced Booking-to-Quote Workflow integration...');

      // Test distance pricing integration in booking workflow
      const testService = {
        id: 'test-service',
        name: 'Integration Test Service',
        base_price: 100,
        description: '',
        duration_minutes: 60,
        is_active: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };

      const testLocation = {
        address: 'Sydney, NSW, Australia',
        city: 'Sydney',
        state: 'NSW',
        postal_code: '2000',
        latitude: -33.8688,
        longitude: 151.2093,
      };

      const pricingResult = await distancePricingService.calculatePricing(testService, testLocation);
      
      results.push({
        testSuite: 'Booking Workflow',
        testName: 'Distance Pricing Integration',
        status: pricingResult.success ? 'pass' : 'fail',
        message: pricingResult.success ? 
          `Distance pricing calculated: $${pricingResult.total_price.toFixed(2)}` : 
          pricingResult.error || 'Distance pricing failed',
        duration: Date.now() - workflowStartTime,
        details: pricingResult.success ? {
          distance: pricingResult.distance_km,
          tier: pricingResult.pricing_tier.name,
          totalPrice: pricingResult.total_price,
        } : undefined,
        error: pricingResult.success ? undefined : pricingResult.error,
      });

    } catch (error) {
      results.push({
        testSuite: 'Booking Workflow',
        testName: 'Distance Pricing Integration',
        status: 'fail',
        message: 'Booking workflow integration test failed',
        duration: Date.now() - workflowStartTime,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }

    // Test email notification integration in booking workflow
    const emailStartTime = Date.now();
    try {
      const emailResult = await emailService.sendEmail({
        to: '<EMAIL>',
        subject: 'Integration Test - Booking Confirmation',
        template: 'booking_confirmation',
        variables: {
          customer_name: 'Integration Test',
          booking_date: new Date().toLocaleDateString(),
          service_name: 'Test Service',
        },
      });

      results.push({
        testSuite: 'Booking Workflow',
        testName: 'Email Notification Integration',
        status: emailResult.success ? 'pass' : 'fail',
        message: emailResult.success ? 
          'Email notification integration working' : 
          emailResult.error || 'Email integration failed',
        duration: Date.now() - emailStartTime,
        details: emailResult.success ? { emailId: emailResult.emailId } : undefined,
        error: emailResult.success ? undefined : emailResult.error,
      });

    } catch (error) {
      results.push({
        testSuite: 'Booking Workflow',
        testName: 'Email Notification Integration',
        status: 'fail',
        message: 'Email notification integration test failed',
        duration: Date.now() - emailStartTime,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }

    return results;
  }

  /**
   * Test Database Integration with SQL Functions
   */
  async testDatabaseIntegration(): Promise<IntegrationTestResult[]> {
    const results: IntegrationTestResult[] = [];

    // Test SQL functions integration
    const sqlStartTime = Date.now();
    try {
      console.log('🗄️ Testing Database Integration with SQL Functions...');

      const transactionResult = await sqlFunctionsService.generateTransactionNumber();
      
      results.push({
        testSuite: 'Database Integration',
        testName: 'SQL Functions Integration',
        status: transactionResult.success ? 'pass' : 'fail',
        message: transactionResult.success ? 
          `SQL functions working: ${transactionResult.transaction_number}` : 
          transactionResult.error || 'SQL functions failed',
        duration: Date.now() - sqlStartTime,
        details: transactionResult.success ? { 
          transactionNumber: transactionResult.transaction_number 
        } : undefined,
        error: transactionResult.success ? undefined : transactionResult.error,
      });

    } catch (error) {
      results.push({
        testSuite: 'Database Integration',
        testName: 'SQL Functions Integration',
        status: 'fail',
        message: 'SQL functions integration test failed',
        duration: Date.now() - sqlStartTime,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }

    // Test database performance
    const perfStartTime = Date.now();
    try {
      const dbValidationResults = await databaseIntegrationValidator.runCompleteDatabaseValidation();
      const passedTests = dbValidationResults.summary.passedTests;
      const totalTests = dbValidationResults.summary.totalTests;
      const successRate = dbValidationResults.summary.successRate;

      results.push({
        testSuite: 'Database Integration',
        testName: 'Database Performance Validation',
        status: successRate >= 90 ? 'pass' : successRate >= 75 ? 'warning' : 'fail',
        message: `Database validation: ${passedTests}/${totalTests} tests passed (${successRate.toFixed(1)}%)`,
        duration: Date.now() - perfStartTime,
        details: {
          passedTests,
          totalTests,
          successRate,
          duration: dbValidationResults.summary.totalDuration,
        },
      });

    } catch (error) {
      results.push({
        testSuite: 'Database Integration',
        testName: 'Database Performance Validation',
        status: 'fail',
        message: 'Database performance validation failed',
        duration: Date.now() - perfStartTime,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }

    return results;
  }

  /**
   * Test Admin Portal Integration
   */
  async testAdminPortalIntegration(): Promise<IntegrationTestResult[]> {
    const results: IntegrationTestResult[] = [];

    // Test admin portal connectivity
    const adminStartTime = Date.now();
    try {
      console.log('🌐 Testing Admin Portal Integration...');

      const adminStatus = await adminPortalIntegrationValidator.getAdminPortalIntegrationStatus();
      
      results.push({
        testSuite: 'Admin Portal Integration',
        testName: 'Admin Portal Connectivity',
        status: adminStatus.overall ? 'pass' : 'warning',
        message: adminStatus.overall ? 
          'Admin portal integration fully operational' : 
          'Admin portal integration has issues',
        duration: Date.now() - adminStartTime,
        details: {
          apiConnectivity: adminStatus.apiConnectivity,
          authentication: adminStatus.authentication,
          crudCompatibility: adminStatus.crudCompatibility,
          dataIntegrity: adminStatus.dataIntegrity,
        },
      });

    } catch (error) {
      results.push({
        testSuite: 'Admin Portal Integration',
        testName: 'Admin Portal Connectivity',
        status: 'fail',
        message: 'Admin portal integration test failed',
        duration: Date.now() - adminStartTime,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }

    // Test real-time synchronization
    const syncStartTime = Date.now();
    try {
      const syncConnectivity = await realTimeDataSyncService.testRealTimeConnectivity();
      
      results.push({
        testSuite: 'Admin Portal Integration',
        testName: 'Real-Time Synchronization',
        status: syncConnectivity.connected ? 'pass' : 'warning',
        message: syncConnectivity.connected ? 
          `Real-time sync operational (${syncConnectivity.latency}ms latency)` : 
          'Real-time sync not available',
        duration: Date.now() - syncStartTime,
        details: {
          connected: syncConnectivity.connected,
          latency: syncConnectivity.latency,
          channels: syncConnectivity.channels,
        },
      });

    } catch (error) {
      results.push({
        testSuite: 'Admin Portal Integration',
        testName: 'Real-Time Synchronization',
        status: 'warning',
        message: 'Real-time sync test failed',
        duration: Date.now() - syncStartTime,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }

    return results;
  }

  /**
   * Test Production Readiness Validation Framework
   */
  async testProductionReadinessFramework(): Promise<IntegrationTestResult[]> {
    const results: IntegrationTestResult[] = [];

    // Test production readiness validation
    const readinessStartTime = Date.now();
    try {
      console.log('🚀 Testing Production Readiness Validation Framework...');

      const readinessReport = await productionReadinessValidator.validateProductionReadiness();
      
      results.push({
        testSuite: 'Production Readiness',
        testName: 'Production Readiness Validation',
        status: readinessReport.readiness_score >= 90 ? 'pass' : 
                readinessReport.readiness_score >= 75 ? 'warning' : 'fail',
        message: `Production readiness: ${readinessReport.readiness_score}% (${readinessReport.status})`,
        duration: Date.now() - readinessStartTime,
        details: {
          readinessScore: readinessReport.readiness_score,
          status: readinessReport.status,
          criticalBlockers: readinessReport.critical_blockers.length,
          recommendations: readinessReport.recommendations.length,
        },
      });

    } catch (error) {
      results.push({
        testSuite: 'Production Readiness',
        testName: 'Production Readiness Validation',
        status: 'fail',
        message: 'Production readiness validation failed',
        duration: Date.now() - readinessStartTime,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }

    return results;
  }

  /**
   * Test Distance Pricing System Integration
   */
  async testDistancePricingIntegration(): Promise<IntegrationTestResult[]> {
    const results: IntegrationTestResult[] = [];

    // Test distance pricing validation
    const pricingStartTime = Date.now();
    try {
      console.log('💰 Testing Distance Pricing System Integration...');

      const pricingValidationResults = await distancePricingValidator.runComprehensiveDistancePricingValidation();
      const passedTests = pricingValidationResults.filter(r => r.status === 'pass').length;
      const totalTests = pricingValidationResults.length;
      const successRate = totalTests > 0 ? (passedTests / totalTests) * 100 : 0;

      results.push({
        testSuite: 'Distance Pricing',
        testName: 'Distance Pricing System Validation',
        status: successRate >= 90 ? 'pass' : successRate >= 75 ? 'warning' : 'fail',
        message: `Distance pricing validation: ${passedTests}/${totalTests} tests passed (${successRate.toFixed(1)}%)`,
        duration: Date.now() - pricingStartTime,
        details: {
          passedTests,
          totalTests,
          successRate,
          validationResults: pricingValidationResults.map(r => ({
            test: r.test,
            status: r.status,
            message: r.message,
          })),
        },
      });

    } catch (error) {
      results.push({
        testSuite: 'Distance Pricing',
        testName: 'Distance Pricing System Validation',
        status: 'fail',
        message: 'Distance pricing validation failed',
        duration: Date.now() - pricingStartTime,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }

    return results;
  }

  /**
   * Get system integration status
   */
  async getSystemIntegrationStatus(): Promise<SystemIntegrationStatus> {
    try {
      const [
        bookingResults,
        databaseResults,
        adminResults,
        readinessResults,
        pricingResults,
      ] = await Promise.all([
        this.testBookingWorkflowIntegration(),
        this.testDatabaseIntegration(),
        this.testAdminPortalIntegration(),
        this.testProductionReadinessFramework(),
        this.testDistancePricingIntegration(),
      ]);

      const status = {
        bookingWorkflow: bookingResults.every(r => r.status !== 'fail'),
        emailNotifications: bookingResults.some(r => r.testName.includes('Email') && r.status === 'pass'),
        distancePricing: pricingResults.every(r => r.status !== 'fail'),
        databaseIntegration: databaseResults.every(r => r.status !== 'fail'),
        adminPortalSync: adminResults.every(r => r.status !== 'fail'),
        productionReadiness: readinessResults.every(r => r.status !== 'fail'),
        overall: false,
      };

      // Overall status is true if all critical systems are working
      status.overall = status.bookingWorkflow && 
                      status.distancePricing && 
                      status.databaseIntegration && 
                      status.productionReadiness;

      return status;

    } catch (error) {
      console.error('❌ Failed to get system integration status:', error);
      return {
        bookingWorkflow: false,
        emailNotifications: false,
        distancePricing: false,
        databaseIntegration: false,
        adminPortalSync: false,
        productionReadiness: false,
        overall: false,
      };
    }
  }

  /**
   * Run comprehensive final integration testing
   */
  async runFinalIntegrationTesting(): Promise<FinalIntegrationReport> {
    console.log('🧪 Running comprehensive final integration testing...');

    const startTime = Date.now();

    try {
      const [
        bookingResults,
        databaseResults,
        adminResults,
        readinessResults,
        pricingResults,
      ] = await Promise.all([
        this.testBookingWorkflowIntegration(),
        this.testDatabaseIntegration(),
        this.testAdminPortalIntegration(),
        this.testProductionReadinessFramework(),
        this.testDistancePricingIntegration(),
      ]);

      const allResults = [
        ...bookingResults,
        ...databaseResults,
        ...adminResults,
        ...readinessResults,
        ...pricingResults,
      ];

      const passedTests = allResults.filter(r => r.status === 'pass').length;
      const failedTests = allResults.filter(r => r.status === 'fail').length;
      const warningTests = allResults.filter(r => r.status === 'warning').length;
      const successRate = allResults.length > 0 ? (passedTests / allResults.length) * 100 : 0;
      const totalDuration = Date.now() - startTime;
      const averageTestTime = allResults.length > 0 ? totalDuration / allResults.length : 0;

      // Get system status
      const systemStatus = await this.getSystemIntegrationStatus();

      // Determine overall status
      let overallStatus: 'production_ready' | 'needs_attention' | 'not_ready';
      if (successRate >= 95 && failedTests === 0) {
        overallStatus = 'production_ready';
      } else if (successRate >= 80 && failedTests <= 2) {
        overallStatus = 'needs_attention';
      } else {
        overallStatus = 'not_ready';
      }

      // Collect critical issues
      const criticalIssues = allResults
        .filter(r => r.status === 'fail')
        .map(r => `${r.testSuite} - ${r.testName}: ${r.message}`);

      // Generate recommendations
      const recommendations = this.generateRecommendations(allResults, systemStatus);

      // Calculate deployment readiness
      const deploymentReadiness = this.calculateDeploymentReadiness(
        successRate,
        failedTests,
        criticalIssues,
        systemStatus
      );

      const report: FinalIntegrationReport = {
        timestamp: new Date().toISOString(),
        overallStatus,
        systemStatus,
        testResults: allResults,
        performanceMetrics: {
          totalTests: allResults.length,
          passedTests,
          failedTests,
          warningTests,
          successRate: Math.round(successRate * 100) / 100,
          totalDuration,
          averageTestTime: Math.round(averageTestTime),
        },
        criticalIssues,
        recommendations,
        deploymentReadiness,
      };

      console.log(`✅ Final integration testing completed: ${passedTests}/${allResults.length} tests passed (${successRate.toFixed(1)}%)`);
      console.log(`🚀 Deployment readiness: ${deploymentReadiness.score}% - ${deploymentReadiness.readyForProduction ? 'READY' : 'NOT READY'}`);

      return report;

    } catch (error) {
      console.error('❌ Final integration testing failed:', error);
      
      return {
        timestamp: new Date().toISOString(),
        overallStatus: 'not_ready',
        systemStatus: {
          bookingWorkflow: false,
          emailNotifications: false,
          distancePricing: false,
          databaseIntegration: false,
          adminPortalSync: false,
          productionReadiness: false,
          overall: false,
        },
        testResults: [],
        performanceMetrics: {
          totalTests: 0,
          passedTests: 0,
          failedTests: 0,
          warningTests: 0,
          successRate: 0,
          totalDuration: Date.now() - startTime,
          averageTestTime: 0,
        },
        criticalIssues: [error instanceof Error ? error.message : 'Integration testing failed'],
        recommendations: ['Fix integration testing system and retry'],
        deploymentReadiness: {
          score: 0,
          blockers: ['Integration testing system failure'],
          warnings: [],
          readyForProduction: false,
        },
      };
    }
  }

  /**
   * Generate recommendations based on test results
   */
  private generateRecommendations(
    results: IntegrationTestResult[],
    systemStatus: SystemIntegrationStatus
  ): string[] {
    const recommendations: string[] = [];

    const failedTests = results.filter(r => r.status === 'fail');
    const warningTests = results.filter(r => r.status === 'warning');

    if (failedTests.length === 0 && warningTests.length === 0) {
      recommendations.push('All systems are fully integrated and ready for production deployment');
    } else {
      if (failedTests.length > 0) {
        recommendations.push(`Address ${failedTests.length} critical integration failures before deployment`);
      }
      
      if (warningTests.length > 0) {
        recommendations.push(`Review ${warningTests.length} integration warnings for optimization opportunities`);
      }

      if (!systemStatus.bookingWorkflow) {
        recommendations.push('Fix booking workflow integration issues');
      }
      
      if (!systemStatus.databaseIntegration) {
        recommendations.push('Resolve database integration problems');
      }
      
      if (!systemStatus.adminPortalSync) {
        recommendations.push('Improve admin portal synchronization reliability');
      }
    }

    return recommendations;
  }

  /**
   * Calculate deployment readiness score and status
   */
  private calculateDeploymentReadiness(
    successRate: number,
    failedTests: number,
    criticalIssues: string[],
    systemStatus: SystemIntegrationStatus
  ): {
    score: number;
    blockers: string[];
    warnings: string[];
    readyForProduction: boolean;
  } {
    let score = successRate;
    const blockers: string[] = [];
    const warnings: string[] = [];

    // Critical system checks
    if (!systemStatus.bookingWorkflow) {
      blockers.push('Booking workflow integration not working');
      score -= 20;
    }

    if (!systemStatus.databaseIntegration) {
      blockers.push('Database integration not working');
      score -= 25;
    }

    if (!systemStatus.productionReadiness) {
      blockers.push('Production readiness validation failing');
      score -= 15;
    }

    // Warning conditions
    if (!systemStatus.emailNotifications) {
      warnings.push('Email notifications may have issues');
      score -= 5;
    }

    if (!systemStatus.adminPortalSync) {
      warnings.push('Admin portal synchronization may be unreliable');
      score -= 10;
    }

    // Failed tests impact
    if (failedTests > 0) {
      score -= failedTests * 5;
    }

    // Ensure score doesn't go below 0
    score = Math.max(0, score);

    const readyForProduction = score >= 90 && blockers.length === 0;

    return {
      score: Math.round(score),
      blockers,
      warnings,
      readyForProduction,
    };
  }
}

// Export singleton instance
export const finalIntegrationTestingSystem = FinalIntegrationTestingSystem.getInstance();
