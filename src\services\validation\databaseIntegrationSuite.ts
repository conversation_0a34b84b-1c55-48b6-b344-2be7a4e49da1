/**
 * Ocean Soul Sparkles Mobile App - Database Integration Test Suite
 * Comprehensive validation of all database integration aspects
 */

import { databaseSchemaValidator, SchemaValidationResult, DatabaseSchemaStatus } from './databaseSchemaValidator';
import { sqlFunctionsValidator, SQLFunctionValidationResult } from './sqlFunctionsValidator';
import { databasePerformanceValidator, PerformanceValidationResult, DatabasePerformanceStatus } from './databasePerformanceValidator';

export interface DatabaseIntegrationReport {
  timestamp: string;
  overall: 'pass' | 'fail' | 'warning';
  summary: {
    totalTests: number;
    passedTests: number;
    failedTests: number;
    warningTests: number;
    successRate: number;
    duration: number;
  };
  sections: {
    schemaValidation: SchemaValidationResult[];
    sqlFunctions: SQLFunctionValidationResult[];
    performance: PerformanceValidationResult[];
  };
  status: {
    schema: DatabaseSchemaStatus;
    performance: DatabasePerformanceStatus;
    sqlFunctionsSummary: any;
  };
  recommendations: string[];
  criticalIssues: string[];
  performanceMetrics: {
    averageQueryTime: number;
    slowestQuery: string;
    fastestQuery: string;
    indexEfficiency: number;
  };
}

export class DatabaseIntegrationSuite {
  private static instance: DatabaseIntegrationSuite;

  private constructor() {}

  public static getInstance(): DatabaseIntegrationSuite {
    if (!DatabaseIntegrationSuite.instance) {
      DatabaseIntegrationSuite.instance = new DatabaseIntegrationSuite();
    }
    return DatabaseIntegrationSuite.instance;
  }

  /**
   * Run complete database integration validation
   */
  async runCompleteDatabaseValidation(): Promise<DatabaseIntegrationReport> {
    const startTime = Date.now();
    console.log('🚀 Starting comprehensive database integration validation...');

    try {
      // Run all validation components in parallel where possible
      const [
        schemaResults,
        sqlFunctionResults,
        performanceResults,
      ] = await Promise.allSettled([
        databaseSchemaValidator.runComprehensiveSchemaValidation(),
        sqlFunctionsValidator.runComprehensiveSQLValidation(),
        databasePerformanceValidator.runComprehensivePerformanceValidation(),
      ]);

      // Extract results from settled promises
      const schemaValidation = this.extractResults(schemaResults, []);
      const sqlFunctions = this.extractResults(sqlFunctionResults, []);
      const performance = this.extractResults(performanceResults, []);

      // Get status information
      const [schemaStatus, performanceStatus] = await Promise.all([
        databaseSchemaValidator.getDatabaseSchemaStatus(),
        databasePerformanceValidator.getDatabasePerformanceStatus(),
      ]);

      const sqlFunctionsSummary = sqlFunctionsValidator.getSQLFunctionsSummary(sqlFunctions);

      // Calculate overall metrics
      const allTests = [
        ...schemaValidation,
        ...sqlFunctions,
        ...performance,
      ];

      const passedTests = allTests.filter(t => t.status === 'pass').length;
      const failedTests = allTests.filter(t => t.status === 'fail').length;
      const warningTests = allTests.filter(t => t.status === 'warning').length;
      const totalTests = allTests.length;
      const successRate = totalTests > 0 ? (passedTests / totalTests) * 100 : 0;
      const duration = Date.now() - startTime;

      // Determine overall status
      const overall: 'pass' | 'fail' | 'warning' = 
        failedTests > totalTests * 0.15 ? 'fail' :  // More than 15% failed
        failedTests > 0 || warningTests > totalTests * 0.25 ? 'warning' : // Any failures or >25% warnings
        'pass';

      // Calculate performance metrics
      const performanceMetrics = this.calculatePerformanceMetrics(performance);

      // Generate recommendations and identify critical issues
      const { recommendations, criticalIssues } = this.generateRecommendations({
        schemaValidation,
        sqlFunctions,
        performance,
        schemaStatus,
        performanceStatus,
        sqlFunctionsSummary,
        overall,
      });

      const report: DatabaseIntegrationReport = {
        timestamp: new Date().toISOString(),
        overall,
        summary: {
          totalTests,
          passedTests,
          failedTests,
          warningTests,
          successRate: Math.round(successRate * 100) / 100,
          duration,
        },
        sections: {
          schemaValidation,
          sqlFunctions,
          performance,
        },
        status: {
          schema: schemaStatus,
          performance: performanceStatus,
          sqlFunctionsSummary,
        },
        recommendations,
        criticalIssues,
        performanceMetrics,
      };

      console.log(`✅ Database integration validation completed: ${passedTests}/${totalTests} tests passed (${report.summary.successRate}%)`);

      return report;

    } catch (error) {
      console.error('❌ Database integration validation failed:', error);
      
      return {
        timestamp: new Date().toISOString(),
        overall: 'fail',
        summary: {
          totalTests: 0,
          passedTests: 0,
          failedTests: 1,
          warningTests: 0,
          successRate: 0,
          duration: Date.now() - startTime,
        },
        sections: {
          schemaValidation: [],
          sqlFunctions: [],
          performance: [],
        },
        status: {
          schema: {
            coreTablesExist: false,
            transactionTablesExist: false,
            constraintsValid: false,
            indexesOptimal: false,
            rlsPoliciesActive: false,
            overall: false,
          },
          performance: {
            queryPerformance: false,
            indexOptimization: false,
            connectionStability: false,
            memoryUsage: false,
            overall: false,
          },
          sqlFunctionsSummary: {
            totalFunctions: 0,
            workingFunctions: 0,
            missingFunctions: 0,
            criticalMissing: 0,
            functionsByType: {},
          },
        },
        recommendations: ['Fix critical database validation suite error before proceeding'],
        criticalIssues: [error instanceof Error ? error.message : 'Unknown validation error'],
        performanceMetrics: {
          averageQueryTime: 0,
          slowestQuery: 'N/A',
          fastestQuery: 'N/A',
          indexEfficiency: 0,
        },
      };
    }
  }

  /**
   * Extract results from settled promises
   */
  private extractResults<T>(settledResult: PromiseSettledResult<T>, fallback: T): T {
    if (settledResult.status === 'fulfilled') {
      return settledResult.value;
    } else {
      console.warn('Promise rejected:', settledResult.reason);
      return fallback;
    }
  }

  /**
   * Calculate performance metrics from test results
   */
  private calculatePerformanceMetrics(performanceResults: PerformanceValidationResult[]): {
    averageQueryTime: number;
    slowestQuery: string;
    fastestQuery: string;
    indexEfficiency: number;
  } {
    const queryResults = performanceResults.filter(r => r.details?.executionTime !== undefined);
    
    if (queryResults.length === 0) {
      return {
        averageQueryTime: 0,
        slowestQuery: 'N/A',
        fastestQuery: 'N/A',
        indexEfficiency: 0,
      };
    }

    const executionTimes = queryResults.map(r => r.details.executionTime);
    const averageQueryTime = executionTimes.reduce((sum, time) => sum + time, 0) / executionTimes.length;
    
    const slowestResult = queryResults.reduce((slowest, current) => 
      current.details.executionTime > slowest.details.executionTime ? current : slowest
    );
    
    const fastestResult = queryResults.reduce((fastest, current) => 
      current.details.executionTime < fastest.details.executionTime ? current : fastest
    );

    // Calculate index efficiency based on performance ratings
    const excellentQueries = queryResults.filter(r => r.details.performanceRating === 'excellent').length;
    const goodQueries = queryResults.filter(r => r.details.performanceRating === 'good').length;
    const indexEfficiency = ((excellentQueries * 1.0 + goodQueries * 0.8) / queryResults.length) * 100;

    return {
      averageQueryTime: Math.round(averageQueryTime),
      slowestQuery: slowestResult.operation,
      fastestQuery: fastestResult.operation,
      indexEfficiency: Math.round(indexEfficiency),
    };
  }

  /**
   * Generate recommendations and identify critical issues
   */
  private generateRecommendations(data: {
    schemaValidation: SchemaValidationResult[];
    sqlFunctions: SQLFunctionValidationResult[];
    performance: PerformanceValidationResult[];
    schemaStatus: DatabaseSchemaStatus;
    performanceStatus: DatabasePerformanceStatus;
    sqlFunctionsSummary: any;
    overall: 'pass' | 'fail' | 'warning';
  }): { recommendations: string[]; criticalIssues: string[] } {
    
    const recommendations: string[] = [];
    const criticalIssues: string[] = [];

    // Check core tables
    if (!data.schemaStatus.coreTablesExist) {
      criticalIssues.push('Core database tables are missing or inaccessible');
      recommendations.push('Verify Supabase database setup and table creation scripts');
    }

    // Check constraints and relationships
    if (!data.schemaStatus.constraintsValid) {
      criticalIssues.push('Database constraints and relationships are invalid');
      recommendations.push('Review foreign key constraints and table relationships');
    }

    // Check RLS policies
    if (!data.schemaStatus.rlsPoliciesActive) {
      criticalIssues.push('Row Level Security policies are not properly configured');
      recommendations.push('Configure RLS policies for data security and access control');
    }

    // Check SQL functions
    if (data.sqlFunctionsSummary.criticalMissing > 0) {
      criticalIssues.push(`${data.sqlFunctionsSummary.criticalMissing} critical SQL functions are missing`);
      recommendations.push('Implement missing SQL functions and triggers');
    }

    // Check query performance
    if (!data.performanceStatus.queryPerformance) {
      recommendations.push('Optimize slow database queries and consider adding indexes');
    }

    // Check connection stability
    if (!data.performanceStatus.connectionStability) {
      criticalIssues.push('Database connection stability issues detected');
      recommendations.push('Investigate network connectivity and database server health');
    }

    // Check transaction tables
    if (!data.schemaStatus.transactionTablesExist) {
      recommendations.push('Consider implementing transaction tables for POS functionality');
    }

    // Overall recommendations based on status
    if (data.overall === 'pass') {
      recommendations.push('✅ Database integration is production-ready');
      recommendations.push('Consider implementing monitoring for ongoing database health');
    } else if (data.overall === 'warning') {
      recommendations.push('⚠️ Database integration has minor issues');
      recommendations.push('Address warnings before production deployment');
    } else {
      recommendations.push('❌ Database integration requires fixes before production');
      recommendations.push('Focus on resolving critical database issues first');
    }

    return { recommendations, criticalIssues };
  }

  /**
   * Generate detailed database validation report
   */
  generateDetailedReport(report: DatabaseIntegrationReport): string {
    const lines = ['# Ocean Soul Sparkles - Database Integration Validation Report'];
    lines.push('');
    lines.push(`**Generated:** ${new Date(report.timestamp).toLocaleString()}`);
    lines.push(`**Overall Status:** ${report.overall.toUpperCase()}`);
    lines.push(`**Success Rate:** ${report.summary.successRate}%`);
    lines.push(`**Duration:** ${report.summary.duration}ms`);
    lines.push('');

    // Executive Summary
    lines.push('## Executive Summary');
    lines.push(`- **Total Tests:** ${report.summary.totalTests}`);
    lines.push(`- **Passed:** ${report.summary.passedTests} ✅`);
    lines.push(`- **Failed:** ${report.summary.failedTests} ❌`);
    lines.push(`- **Warnings:** ${report.summary.warningTests} ⚠️`);
    lines.push('');

    // Performance Metrics
    lines.push('## Performance Metrics');
    lines.push(`- **Average Query Time:** ${report.performanceMetrics.averageQueryTime}ms`);
    lines.push(`- **Slowest Query:** ${report.performanceMetrics.slowestQuery}`);
    lines.push(`- **Fastest Query:** ${report.performanceMetrics.fastestQuery}`);
    lines.push(`- **Index Efficiency:** ${report.performanceMetrics.indexEfficiency}%`);
    lines.push('');

    // Critical Issues
    if (report.criticalIssues.length > 0) {
      lines.push('## 🚨 Critical Issues');
      report.criticalIssues.forEach(issue => {
        lines.push(`- ${issue}`);
      });
      lines.push('');
    }

    // Status Overview
    lines.push('## Status Overview');
    lines.push('### Database Schema');
    lines.push(`- **Core Tables:** ${report.status.schema.coreTablesExist ? '✅' : '❌'}`);
    lines.push(`- **Transaction Tables:** ${report.status.schema.transactionTablesExist ? '✅' : '❌'}`);
    lines.push(`- **Constraints:** ${report.status.schema.constraintsValid ? '✅' : '❌'}`);
    lines.push(`- **RLS Policies:** ${report.status.schema.rlsPoliciesActive ? '✅' : '❌'}`);
    lines.push('');

    lines.push('### Database Performance');
    lines.push(`- **Query Performance:** ${report.status.performance.queryPerformance ? '✅' : '❌'}`);
    lines.push(`- **Index Optimization:** ${report.status.performance.indexOptimization ? '✅' : '❌'}`);
    lines.push(`- **Connection Stability:** ${report.status.performance.connectionStability ? '✅' : '❌'}`);
    lines.push('');

    lines.push('### SQL Functions');
    lines.push(`- **Total Functions:** ${report.status.sqlFunctionsSummary.totalFunctions}`);
    lines.push(`- **Working Functions:** ${report.status.sqlFunctionsSummary.workingFunctions} ✅`);
    lines.push(`- **Missing Functions:** ${report.status.sqlFunctionsSummary.missingFunctions} ❌`);
    lines.push(`- **Critical Missing:** ${report.status.sqlFunctionsSummary.criticalMissing} 🚨`);
    lines.push('');

    // Detailed Test Results
    lines.push('## Detailed Test Results');
    
    // Schema Validation
    if (report.sections.schemaValidation.length > 0) {
      lines.push('### Database Schema Validation');
      report.sections.schemaValidation.forEach(test => {
        const icon = test.status === 'pass' ? '✅' : test.status === 'fail' ? '❌' : '⚠️';
        lines.push(`- **${test.test}:** ${icon} ${test.message} (${test.duration}ms)`);
      });
      lines.push('');
    }

    // SQL Functions
    if (report.sections.sqlFunctions.length > 0) {
      lines.push('### SQL Functions Validation');
      const groupedFunctions = report.sections.sqlFunctions.reduce((acc, test) => {
        if (!acc[test.test]) acc[test.test] = [];
        acc[test.test].push(test);
        return acc;
      }, {} as Record<string, SQLFunctionValidationResult[]>);

      Object.entries(groupedFunctions).forEach(([testType, tests]) => {
        lines.push(`#### ${testType}`);
        tests.forEach(test => {
          const icon = test.status === 'pass' ? '✅' : test.status === 'fail' ? '❌' : '⚠️';
          lines.push(`- **${test.functionName}:** ${icon} ${test.message} (${test.duration}ms)`);
        });
        lines.push('');
      });
    }

    // Performance Results
    if (report.sections.performance.length > 0) {
      lines.push('### Database Performance');
      const groupedPerformance = report.sections.performance.reduce((acc, test) => {
        if (!acc[test.test]) acc[test.test] = [];
        acc[test.test].push(test);
        return acc;
      }, {} as Record<string, PerformanceValidationResult[]>);

      Object.entries(groupedPerformance).forEach(([testType, tests]) => {
        lines.push(`#### ${testType}`);
        tests.forEach(test => {
          const icon = test.status === 'pass' ? '✅' : test.status === 'fail' ? '❌' : '⚠️';
          lines.push(`- **${test.operation}:** ${icon} ${test.message} (${test.duration}ms)`);
        });
        lines.push('');
      });
    }

    // Recommendations
    lines.push('## Recommendations');
    report.recommendations.forEach(rec => {
      lines.push(`- ${rec}`);
    });
    lines.push('');

    // Next Steps
    lines.push('## Next Steps');
    if (report.overall === 'pass') {
      lines.push('1. ✅ **Ready for Production** - All critical database components are working');
      lines.push('2. 📊 **Implement Monitoring** - Set up database performance monitoring');
      lines.push('3. 🔄 **Regular Validation** - Schedule periodic database health checks');
    } else if (report.overall === 'warning') {
      lines.push('1. ⚠️ **Address Warnings** - Fix non-critical database issues');
      lines.push('2. 🧪 **Re-run Validation** - Verify fixes with another test run');
      lines.push('3. 📈 **Optimize Performance** - Improve slow queries and indexing');
    } else {
      lines.push('1. 🚨 **Fix Critical Issues** - Address all failed database tests immediately');
      lines.push('2. 🔧 **Database Setup** - Verify table creation and constraint setup');
      lines.push('3. 🧪 **Incremental Testing** - Fix and test one component at a time');
    }

    return lines.join('\n');
  }
}

// Export singleton instance
export const databaseIntegrationSuite = DatabaseIntegrationSuite.getInstance();
