/**
 * Type declarations for react-native-square-in-app-payments
 */

declare module 'react-native-square-in-app-payments' {
  export interface CardDetails {
    nonce: string;
    cardDetails: {
      brand: string;
      last4: string;
      expirationMonth: number;
      expirationYear: number;
      postalCode?: string;
    };
  }

  export interface ApplePayConfig {
    price: string;
    summaryLabel: string;
    countryCode: string;
    currencyCode: string;
    paymentType: number;
    squareLocationId: string;
  }

  export interface GooglePayConfig {
    price: string;
    currencyCode: string;
    priceStatus: number;
    squareLocationId: string;
  }

  export interface CardEntryConfig {
    collectPostalCode: boolean;
    squareLocationId: string;
  }

  export const SqIPCore: {
    setSquareApplicationId(applicationId: string): Promise<void>;
  };

  export const SqIPCardEntry: {
    startCardEntryFlow(
      config: CardEntryConfig,
      onSuccess: (cardDetails: CardDetails) => void,
      onCancel: () => void
    ): Promise<CardDetails>;
  };

  export const SqIPApplePay: {
    PaymentTypeFinal: number;
    canUseApplePay(): Promise<boolean>;
    requestApplePayNonce(
      config: ApplePayConfig,
      onSuccess: (nonce: string) => void,
      onFailure: (error: any) => void,
      onComplete: () => void
    ): Promise<{ nonce: string }>;
  };

  export const SqIPGooglePay: {
    TotalPriceStatusFinal: number;
    canUseGooglePay(): Promise<boolean>;
    requestGooglePayNonce(
      config: GooglePayConfig,
      onSuccess: (nonce: string) => void,
      onFailure: (error: any) => void,
      onCancel: () => void
    ): Promise<{ nonce: string }>;
  };
}
