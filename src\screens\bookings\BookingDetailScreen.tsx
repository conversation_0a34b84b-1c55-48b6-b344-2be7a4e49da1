/**
 * Ocean Soul Sparkles Mobile App - Booking Detail Screen
 * Complete booking management with viewing, editing, and status updates
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  Modal,
  TextInput,
  Switch,
} from 'react-native';
import { Booking, Customer, Service, AdminUser } from '@/types/database';
import { bookingService } from '@/services/database/bookingService';
import { customerService } from '@/services/database/customerService';
import { serviceService } from '@/services/database/serviceService';
import { staffService } from '@/services/database/staffService';
import QuoteScreen from './QuoteScreen';

interface BookingDetailScreenProps {
  bookingId: string;
  onClose: () => void;
  onBookingUpdated: () => void;
}

const BookingDetailScreen: React.FC<BookingDetailScreenProps> = ({
  bookingId,
  onClose,
  onBookingUpdated,
}) => {
  // State management
  const [booking, setBooking] = useState<Booking | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [editing, setEditing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showQuoteScreen, setShowQuoteScreen] = useState(false);
  const [showStatusPicker, setShowStatusPicker] = useState(false);

  // Form data for editing
  const [formData, setFormData] = useState({
    booking_date: '',
    start_time: '',
    end_time: '',
    status: 'pending' as Booking['status'],
    total_amount: '',
    notes: '',
    customer_id: '',
    service_id: '',
    staff_id: '',
  });

  // Dropdown data
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [services, setServices] = useState<Service[]>([]);
  const [staff, setStaff] = useState<AdminUser[]>([]);

  useEffect(() => {
    loadBookingDetails();
    loadDropdownData();
  }, [bookingId]);

  const loadBookingDetails = async () => {
    try {
      setLoading(true);
      setError(null);

      console.log('📋 Loading booking details for ID:', bookingId);
      const result = await bookingService.getBookingById(bookingId);
      
      if (result.error) {
        throw new Error(result.error.message);
      }

      if (result.data) {
        setBooking(result.data);
        setFormData({
          booking_date: result.data.booking_date,
          start_time: result.data.start_time,
          end_time: result.data.end_time || '',
          status: result.data.status,
          total_amount: result.data.total_amount?.toString() || '',
          notes: result.data.notes || '',
          customer_id: result.data.customer_id,
          service_id: result.data.service_id || '',
          staff_id: result.data.staff_id || '',
        });
        console.log('✅ Booking details loaded successfully');
      }
    } catch (err) {
      console.error('❌ Failed to load booking details:', err);
      setError(err instanceof Error ? err.message : 'Failed to load booking');
    } finally {
      setLoading(false);
    }
  };

  const loadDropdownData = async () => {
    try {
      console.log('📋 Loading dropdown data...');
      const [customersResult, servicesResult, staffResult] = await Promise.all([
        customerService.getCustomers({ limit: 100 }),
        serviceService.getServices({ limit: 100 }),
        staffService.getStaff({ limit: 100 }),
      ]);

      if (customersResult.data) setCustomers(customersResult.data);
      if (servicesResult.data) setServices(servicesResult.data);
      if (staffResult.data) setStaff(staffResult.data);
      
      console.log('✅ Dropdown data loaded successfully');
    } catch (err) {
      console.error('❌ Failed to load dropdown data:', err);
    }
  };

  const handleSave = async () => {
    if (!booking) return;

    try {
      setSaving(true);
      setError(null);

      console.log('💾 Saving booking changes...');
      const updateData = {
        booking_date: formData.booking_date,
        start_time: formData.start_time,
        end_time: formData.end_time,
        status: formData.status,
        total_amount: formData.total_amount ? parseFloat(formData.total_amount) : undefined,
        notes: formData.notes,
        customer_id: formData.customer_id,
        service_id: formData.service_id || undefined,
        staff_id: formData.staff_id || undefined,
      };

      const result = await bookingService.updateBooking(booking.id, updateData);
      if (result.error) {
        throw new Error(result.error.message);
      }

      Alert.alert('Success! ✅', 'Booking updated successfully!');
      setEditing(false);
      onBookingUpdated();
      await loadBookingDetails(); // Reload to get updated data
      console.log('✅ Booking saved successfully');
    } catch (err) {
      console.error('❌ Failed to save booking:', err);
      setError(err instanceof Error ? err.message : 'Failed to save booking');
      Alert.alert('Error ❌', 'Failed to save booking. Please try again.');
    } finally {
      setSaving(false);
    }
  };

  const handleDelete = () => {
    Alert.alert(
      'Delete Booking ⚠️',
      'Are you sure you want to delete this booking? This action cannot be undone.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              setSaving(true);
              console.log('🗑️ Deleting booking...');
              
              const result = await bookingService.deleteBooking(bookingId);
              if (result.error) {
                throw new Error(result.error.message);
              }
              
              Alert.alert('Success! ✅', 'Booking deleted successfully!');
              onBookingUpdated();
              onClose();
              console.log('✅ Booking deleted successfully');
            } catch (err) {
              console.error('❌ Failed to delete booking:', err);
              Alert.alert('Error ❌', 'Failed to delete booking. Please try again.');
            } finally {
              setSaving(false);
            }
          },
        },
      ]
    );
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'confirmed': return '#10b981';
      case 'pending': return '#f59e0b';
      case 'cancelled': return '#ef4444';
      case 'completed': return '#6366f1';
      case 'in_progress': return '#8b5cf6';
      default: return '#6b7280';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status.toLowerCase()) {
      case 'confirmed': return '✅';
      case 'pending': return '⏳';
      case 'cancelled': return '❌';
      case 'completed': return '🎉';
      case 'in_progress': return '🔄';
      default: return '📅';
    }
  };

  const getQuoteStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'accepted': return '#4CAF50';
      case 'sent': return '#2196F3';
      case 'declined': return '#f44336';
      case 'expired': return '#9E9E9E';
      case 'draft':
      default: return '#FF9800';
    }
  };

  const formatDateTime = (dateString: string, timeString?: string) => {
    try {
      const date = new Date(dateString);
      const dateStr = date.toLocaleDateString('en-US', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric',
      });

      if (timeString) {
        const time = new Date(`${dateString}T${timeString}`);
        const timeStr = time.toLocaleTimeString('en-US', {
          hour: 'numeric',
          minute: '2-digit',
          hour12: true,
        });
        return `${dateStr} at ${timeStr}`;
      }

      return dateStr;
    } catch {
      return dateString;
    }
  };

  const formatTime = (timeString: string) => {
    try {
      const time = new Date(`2000-01-01T${timeString}`);
      return time.toLocaleTimeString('en-US', {
        hour: 'numeric',
        minute: '2-digit',
        hour12: true,
      });
    } catch {
      return timeString;
    }
  };

  // Loading state
  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#FF9A8B" />
          <Text style={styles.loadingText}>Loading booking details...</Text>
        </View>
      </SafeAreaView>
    );
  }

  // Error state
  if (error || !booking) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.errorContainer}>
          <Text style={styles.errorIcon}>⚠️</Text>
          <Text style={styles.errorTitle}>Failed to Load Booking</Text>
          <Text style={styles.errorText}>{error || 'Booking not found'}</Text>
          <TouchableOpacity onPress={loadBookingDetails} style={styles.retryButton}>
            <Text style={styles.retryButtonText}>🔄 Retry</Text>
          </TouchableOpacity>
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <Text style={styles.closeButtonText}>Close</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={onClose} style={styles.backButton}>
          <Text style={styles.backButtonText}>‹ Back</Text>
        </TouchableOpacity>
        
        <Text style={styles.headerTitle}>Booking Details</Text>
        
        <TouchableOpacity 
          onPress={() => setEditing(!editing)} 
          style={styles.editButton}
          disabled={saving}
        >
          <Text style={styles.editButtonText}>
            {editing ? 'Cancel' : 'Edit'}
          </Text>
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Status Badge */}
        <View style={styles.statusSection}>
          <TouchableOpacity
            style={[styles.statusBadge, { backgroundColor: getStatusColor(booking.status) }]}
            onPress={() => editing && setShowStatusPicker(true)}
            disabled={!editing}
          >
            <Text style={styles.statusIcon}>{getStatusIcon(booking.status)}</Text>
            <Text style={styles.statusText}>
              {booking.status.charAt(0).toUpperCase() + booking.status.slice(1).replace('_', ' ')}
            </Text>
            {editing && <Text style={styles.editIndicator}>✏️</Text>}
          </TouchableOpacity>
        </View>

        {/* Customer Information */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>👤 Customer Information</Text>
          <View style={styles.infoCard}>
            <Text style={styles.infoLabel}>Name</Text>
            <Text style={styles.infoValue}>
              {booking.customer?.full_name || 'Unknown Customer'}
            </Text>

            {booking.customer?.email && (
              <>
                <Text style={styles.infoLabel}>Email</Text>
                <Text style={styles.infoValue}>{booking.customer.email}</Text>
              </>
            )}

            {booking.customer?.phone && (
              <>
                <Text style={styles.infoLabel}>Phone</Text>
                <Text style={styles.infoValue}>{booking.customer.phone}</Text>
              </>
            )}

            {booking.customer?.address && (
              <>
                <Text style={styles.infoLabel}>Address</Text>
                <Text style={styles.infoValue}>
                  {booking.customer.address}
                  {booking.customer.city && `, ${booking.customer.city}`}
                  {booking.customer.state && `, ${booking.customer.state}`}
                  {booking.customer.postal_code && ` ${booking.customer.postal_code}`}
                </Text>
              </>
            )}
          </View>
        </View>

        {/* Service Information */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>💅 Service Details</Text>
          <View style={styles.infoCard}>
            <Text style={styles.infoLabel}>Service</Text>
            <Text style={styles.infoValue}>
              {booking.service?.name || 'No service selected'}
            </Text>

            {booking.service?.description && (
              <>
                <Text style={styles.infoLabel}>Description</Text>
                <Text style={styles.infoValue}>{booking.service.description}</Text>
              </>
            )}

            {booking.service?.base_price && (
              <>
                <Text style={styles.infoLabel}>Base Price</Text>
                <Text style={styles.infoValue}>${booking.service.base_price.toFixed(2)}</Text>
              </>
            )}

            {booking.service?.duration_minutes && (
              <>
                <Text style={styles.infoLabel}>Duration</Text>
                <Text style={styles.infoValue}>{booking.service.duration_minutes} minutes</Text>
              </>
            )}

            {booking.service?.category && (
              <>
                <Text style={styles.infoLabel}>Category</Text>
                <Text style={styles.infoValue}>{booking.service.category}</Text>
              </>
            )}
          </View>
        </View>

        {/* Appointment Details */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>📅 Appointment Details</Text>
          <View style={styles.infoCard}>
            {editing ? (
              <>
                <Text style={styles.infoLabel}>Date</Text>
                <TextInput
                  style={styles.input}
                  value={formData.booking_date}
                  onChangeText={(text) => setFormData(prev => ({ ...prev, booking_date: text }))}
                  placeholder="YYYY-MM-DD"
                />

                <Text style={styles.infoLabel}>Start Time</Text>
                <TextInput
                  style={styles.input}
                  value={formData.start_time}
                  onChangeText={(text) => setFormData(prev => ({ ...prev, start_time: text }))}
                  placeholder="HH:MM"
                />

                <Text style={styles.infoLabel}>End Time</Text>
                <TextInput
                  style={styles.input}
                  value={formData.end_time}
                  onChangeText={(text) => setFormData(prev => ({ ...prev, end_time: text }))}
                  placeholder="HH:MM (optional)"
                />
              </>
            ) : (
              <>
                <Text style={styles.infoLabel}>Date & Time</Text>
                <Text style={styles.infoValue}>
                  {formatDateTime(booking.booking_date, booking.start_time)}
                </Text>

                {booking.end_time && (
                  <>
                    <Text style={styles.infoLabel}>End Time</Text>
                    <Text style={styles.infoValue}>
                      {formatTime(booking.end_time)}
                    </Text>
                  </>
                )}
              </>
            )}

            {editing ? (
              <>
                <Text style={styles.infoLabel}>Total Amount ($)</Text>
                <TextInput
                  style={styles.input}
                  value={formData.total_amount}
                  onChangeText={(text) => setFormData(prev => ({ ...prev, total_amount: text }))}
                  placeholder="0.00"
                  keyboardType="numeric"
                />
              </>
            ) : booking.total_amount ? (
              <>
                <Text style={styles.infoLabel}>Total Amount</Text>
                <Text style={styles.infoValue}>${booking.total_amount.toFixed(2)}</Text>
              </>
            ) : null}

            {editing ? (
              <>
                <Text style={styles.infoLabel}>Notes</Text>
                <TextInput
                  style={[styles.input, styles.textArea]}
                  value={formData.notes}
                  onChangeText={(text) => setFormData(prev => ({ ...prev, notes: text }))}
                  placeholder="Additional notes..."
                  multiline
                  numberOfLines={3}
                />
              </>
            ) : booking.notes ? (
              <>
                <Text style={styles.infoLabel}>Notes</Text>
                <Text style={styles.infoValue}>{booking.notes}</Text>
              </>
            ) : null}
          </View>
        </View>

        {/* Staff Information */}
        {booking.staff && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>👨‍💼 Assigned Staff</Text>
            <View style={styles.infoCard}>
              <Text style={styles.infoLabel}>Staff Member</Text>
              <Text style={styles.infoValue}>
                {booking.staff.first_name} {booking.staff.last_name}
              </Text>

              <Text style={styles.infoLabel}>Role</Text>
              <Text style={styles.infoValue}>{booking.staff.role}</Text>

              {booking.staff.email && (
                <>
                  <Text style={styles.infoLabel}>Email</Text>
                  <Text style={styles.infoValue}>{booking.staff.email}</Text>
                </>
              )}
            </View>
          </View>
        )}

        {/* Quotes Information */}
        {booking.quotes && booking.quotes.length > 0 && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>💰 Quotes</Text>
            {booking.quotes.map((quote, index) => (
              <View key={quote.id} style={styles.infoCard}>
                <View style={styles.quoteHeader}>
                  <Text style={styles.quoteTitle}>{quote.service_name}</Text>
                  <View style={[styles.quoteBadge, { backgroundColor: getQuoteStatusColor(quote.status) }]}>
                    <Text style={styles.quoteBadgeText}>{quote.status.toUpperCase()}</Text>
                  </View>
                </View>

                <Text style={styles.quoteAmount}>${quote.estimated_total.toFixed(2)}</Text>

                {quote.service_description && (
                  <Text style={styles.quoteDescription} numberOfLines={3}>
                    {quote.service_description}
                  </Text>
                )}

                <Text style={styles.quoteDate}>
                  Created: {new Date(quote.created_at).toLocaleDateString('en-AU')}
                </Text>

                {quote.valid_until && (
                  <Text style={styles.quoteDate}>
                    Valid Until: {new Date(quote.valid_until).toLocaleDateString('en-AU')}
                  </Text>
                )}
              </View>
            ))}
          </View>
        )}

        {/* Action Buttons */}
        <View style={styles.actionSection}>
          {editing ? (
            <>
              <TouchableOpacity
                style={[styles.saveButton, saving && styles.disabledButton]}
                onPress={handleSave}
                disabled={saving}
              >
                {saving ? (
                  <ActivityIndicator color="#fff" />
                ) : (
                  <Text style={styles.saveButtonText}>💾 Save Changes</Text>
                )}
              </TouchableOpacity>
            </>
          ) : (
            <>
              {booking.status === 'pending' && (
                <TouchableOpacity
                  style={styles.primaryButton}
                  onPress={() => setShowQuoteScreen(true)}
                >
                  <Text style={styles.primaryButtonText}>
                    💰 {booking.quotes && booking.quotes.length > 0 ? 'Create New Quote' : 'Create Quote'}
                  </Text>
                </TouchableOpacity>
              )}

              <TouchableOpacity
                style={styles.secondaryButton}
                onPress={handleDelete}
                disabled={saving}
              >
                <Text style={styles.secondaryButtonText}>🗑️ Delete Booking</Text>
              </TouchableOpacity>
            </>
          )}
        </View>
      </ScrollView>

      {/* Status Picker Modal */}
      <Modal
        visible={showStatusPicker}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowStatusPicker(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.pickerModal}>
            <View style={styles.pickerHeader}>
              <Text style={styles.pickerTitle}>Select Status</Text>
              <TouchableOpacity onPress={() => setShowStatusPicker(false)}>
                <Text style={styles.closeModalButton}>✕</Text>
              </TouchableOpacity>
            </View>

            {(['pending', 'confirmed', 'in_progress', 'completed', 'cancelled'] as const).map(status => (
              <TouchableOpacity
                key={status}
                style={[
                  styles.pickerOption,
                  formData.status === status && styles.pickerOptionSelected
                ]}
                onPress={() => {
                  setFormData(prev => ({ ...prev, status }));
                  setShowStatusPicker(false);
                }}
              >
                <Text style={styles.pickerOptionIcon}>{getStatusIcon(status)}</Text>
                <Text style={[
                  styles.pickerOptionText,
                  formData.status === status && styles.pickerOptionTextSelected
                ]}>
                  {status.charAt(0).toUpperCase() + status.slice(1).replace('_', ' ')}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>
      </Modal>

      {/* Quote Screen Modal */}
      {showQuoteScreen && (
        <Modal
          visible={showQuoteScreen}
          animationType="slide"
          presentationStyle="pageSheet"
          onRequestClose={() => setShowQuoteScreen(false)}
        >
          <QuoteScreen
            booking={booking}
            onClose={() => setShowQuoteScreen(false)}
            onQuoteCreated={() => {
              setShowQuoteScreen(false);
              onBookingUpdated();
              loadBookingDetails(); // Reload to show updated booking
            }}
          />
        </Modal>
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  errorIcon: {
    fontSize: 64,
    marginBottom: 16,
  },
  errorTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#ef4444',
    marginBottom: 8,
    textAlign: 'center',
  },
  errorText: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: 20,
  },
  retryButton: {
    backgroundColor: '#FF9A8B',
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 24,
    marginBottom: 12,
  },
  retryButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  closeButton: {
    backgroundColor: '#6b7280',
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 24,
  },
  closeButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: '#FF9A8B',
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  backButton: {
    paddingVertical: 8,
    paddingHorizontal: 12,
  },
  backButtonText: {
    fontSize: 18,
    color: '#fff',
    fontWeight: 'bold',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#fff',
  },
  editButton: {
    paddingVertical: 8,
    paddingHorizontal: 12,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 8,
  },
  editButtonText: {
    fontSize: 14,
    color: '#fff',
    fontWeight: 'bold',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  statusSection: {
    alignItems: 'center',
    marginBottom: 24,
  },
  statusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 20,
  },
  statusIcon: {
    fontSize: 16,
    marginRight: 8,
  },
  statusText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  editIndicator: {
    fontSize: 14,
    marginLeft: 8,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 12,
  },
  infoCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  infoLabel: {
    fontSize: 12,
    color: '#666',
    fontWeight: '600',
    marginTop: 12,
    marginBottom: 4,
  },
  infoValue: {
    fontSize: 16,
    color: '#333',
    fontWeight: '500',
    lineHeight: 22,
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    fontSize: 16,
    backgroundColor: '#fff',
    marginTop: 4,
  },
  textArea: {
    height: 80,
    textAlignVertical: 'top',
  },
  actionSection: {
    marginTop: 24,
    marginBottom: 32,
  },
  primaryButton: {
    backgroundColor: '#10b981',
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
    marginBottom: 12,
  },
  primaryButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  saveButton: {
    backgroundColor: '#007AFF',
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
    marginBottom: 12,
  },
  saveButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  secondaryButton: {
    backgroundColor: '#ef4444',
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
  },
  secondaryButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  disabledButton: {
    opacity: 0.6,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  pickerModal: {
    backgroundColor: '#fff',
    borderRadius: 16,
    margin: 20,
    width: '80%',
    maxHeight: '60%',
  },
  pickerHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#e5e5e5',
  },
  pickerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  closeModalButton: {
    fontSize: 18,
    color: '#666',
    fontWeight: 'bold',
  },
  pickerOption: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  pickerOptionSelected: {
    backgroundColor: '#f0f9ff',
  },
  pickerOptionIcon: {
    fontSize: 20,
    marginRight: 12,
  },
  pickerOptionText: {
    fontSize: 16,
    color: '#333',
    fontWeight: '500',
  },
  pickerOptionTextSelected: {
    color: '#007AFF',
    fontWeight: 'bold',
  },
  quoteHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  quoteTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    flex: 1,
    marginRight: 8,
  },
  quoteBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  quoteBadgeText: {
    fontSize: 12,
    color: '#fff',
    fontWeight: '600',
  },
  quoteAmount: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FF9A8B',
    marginBottom: 8,
  },
  quoteDescription: {
    fontSize: 14,
    color: '#666',
    lineHeight: 18,
    marginBottom: 8,
  },
  quoteDate: {
    fontSize: 12,
    color: '#999',
    marginBottom: 2,
  },
});

export default BookingDetailScreen;
