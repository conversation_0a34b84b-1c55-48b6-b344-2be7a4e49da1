/**
 * Ocean Soul Sparkles Mobile App - Minimal Authentication Store
 * Temporary minimal version to isolate runtime errors
 */

import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';

interface AuthState {
  // State
  user: any | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  requiresMFA: boolean;

  // Actions
  signIn: (credentials: any) => Promise<any>;
  signOut: () => Promise<void>;
  verifyToken: () => Promise<boolean>;
  refreshToken: () => Promise<void>;
  initialize: () => Promise<void>;
  hasRole: (allowedRoles: string[]) => boolean;
  isAdmin: () => boolean;
  canAccessPOS: () => boolean;
}

export const useAuth = create<AuthState>()(
  persist(
    (set, get) => ({
      // Initial state
      user: null,
      isLoading: false,
      isAuthenticated: false,
      requiresMFA: false,

      // Sign in with admin authentication (mock)
      signIn: async (credentials: any) => {
        set({ isLoading: true, requiresMFA: false });

        try {
          // Mock authentication
          const result = {
            success: true,
            user: {
              id: 'test-user',
              email: credentials.email,
              role: 'Admin', // Changed to match expected role format
              first_name: 'Test',
              last_name: 'User',
              is_active: true
            },
          };

          set({
            user: result.user,
            isAuthenticated: true,
            isLoading: false,
            requiresMFA: false,
          });

          console.log('✅ Authentication successful (mock)');
          return result;
        } catch (error) {
          set({
            isLoading: false,
            requiresMFA: false,
          });

          console.error('❌ Authentication error:', error);
          return {
            success: false,
            error: 'Authentication failed',
          };
        }
      },

      // Sign out action
      signOut: async () => {
        set({ isLoading: true });

        try {
          console.log('🔓 Signing out (mock)');
        } catch (error) {
          console.error('Sign out error:', error);
        } finally {
          set({
            user: null,
            isAuthenticated: false,
            isLoading: false,
            requiresMFA: false,
          });
        }
      },

      // Verify token (mock)
      verifyToken: async () => {
        try {
          return true; // Mock verification
        } catch (error) {
          console.error('Token verification error:', error);
          return false;
        }
      },

      // Refresh token (mock)
      refreshToken: async () => {
        try {
          console.log('🔄 Token refreshed (mock)');
        } catch (error) {
          console.error('Token refresh error:', error);
          get().signOut();
        }
      },

      // Initialize auth state (mock)
      initialize: async () => {
        set({ isLoading: true });

        try {
          console.log('🔐 Initializing auth (mock)');
          // Mock initialization - no user by default
          set({
            user: null,
            isAuthenticated: false,
            isLoading: false,
            requiresMFA: false,
          });
        } catch (error) {
          console.error('Auth initialization error:', error);
          set({
            user: null,
            isAuthenticated: false,
            isLoading: false,
            requiresMFA: false,
          });
        }
      },

      // Role-based access helpers (mock)
      hasRole: (allowedRoles: string[]) => {
        const { user } = get();
        return user && allowedRoles.includes(user.role);
      },

      isAdmin: () => {
        const { user } = get();
        return user && user.role === 'admin';
      },

      canAccessPOS: () => {
        const { user } = get();
        return user && ['admin', 'manager', 'staff'].includes(user.role);
      },
    }),
    {
      name: 'ocean-soul-auth',
      storage: createJSONStorage(() => AsyncStorage),
      partialize: (state) => ({
        user: state.user,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
);

// Export store for external access
export const useAuthStore = () => useAuth((state) => state);

// Export user for external access
export const getUser = () => {
  const { user } = useAuth.getState();
  return { user };
};

// Export role helpers
export const hasRole = (allowedRoles: string[]) => {
  const { user } = useAuth.getState();
  return user && allowedRoles.includes(user.role);
};
