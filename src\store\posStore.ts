/**
 * Ocean Soul Sparkles Mobile App - POS Store
 * State management for Point of Sale functionality
 */

import { create } from 'zustand';
import { Product, Service, CartItem, Customer } from '@/types/database';

interface POSState {
  // Cart state
  cartItems: CartItem[];
  cartTotal: number;
  cartItemCount: number;
  
  // Customer state
  currentCustomer: Customer | null;
  
  // UI state
  isLoading: boolean;
  selectedCategory: string | null;
  searchQuery: string;
  
  // Actions
  addToCart: (item: Product | Service, type: 'product' | 'service', quantity?: number) => void;
  removeFromCart: (cartItemId: string) => void;
  updateCartItemQuantity: (cartItemId: string, quantity: number) => void;
  clearCart: () => void;
  
  // Customer actions
  setCurrentCustomer: (customer: Customer | null) => void;
  
  // UI actions
  setLoading: (loading: boolean) => void;
  setSelectedCategory: (category: string | null) => void;
  setSearchQuery: (query: string) => void;
  
  // Computed values
  getCartItemById: (id: string) => CartItem | undefined;
  getCartItemByItemId: (itemId: string, type: 'product' | 'service') => CartItem | undefined;
}

export const usePOSStore = create<POSState>((set, get) => ({
  // Initial state
  cartItems: [],
  cartTotal: 0,
  cartItemCount: 0,
  currentCustomer: null,
  isLoading: false,
  selectedCategory: null,
  searchQuery: '',

  // Cart actions
  addToCart: (item: Product | Service, type: 'product' | 'service', quantity = 1) => {
    const state = get();
    const existingItem = state.getCartItemByItemId(item.id, type);

    if (existingItem) {
      // Update existing item quantity
      state.updateCartItemQuantity(existingItem.id, existingItem.quantity + quantity);
    } else {
      // Add new item to cart
      const unitPrice = type === 'product' ? (item as Product).price : (item as Service).base_price;
      const safeUnitPrice = unitPrice || 0; // Handle undefined prices
      const newCartItem: CartItem = {
        id: `${type}_${item.id}_${Date.now()}`,
        type,
        item_id: item.id,
        item,
        name: item.name,
        quantity,
        unit_price: safeUnitPrice,
        total_price: safeUnitPrice * quantity,
      };

      const newCartItems = [...state.cartItems, newCartItem];
      const newTotal = newCartItems.reduce((sum, cartItem) => sum + (cartItem.total_price || 0), 0);
      const newItemCount = newCartItems.reduce((sum, cartItem) => sum + (cartItem.quantity || 0), 0);

      set({
        cartItems: newCartItems,
        cartTotal: newTotal,
        cartItemCount: newItemCount,
      });
    }
  },

  removeFromCart: (cartItemId: string) => {
    const state = get();
    const newCartItems = state.cartItems.filter(item => item.id !== cartItemId);
    const newTotal = newCartItems.reduce((sum, item) => sum + (item.total_price || 0), 0);
    const newItemCount = newCartItems.reduce((sum, item) => sum + (item.quantity || 0), 0);

    set({
      cartItems: newCartItems,
      cartTotal: newTotal,
      cartItemCount: newItemCount,
    });
  },

  updateCartItemQuantity: (cartItemId: string, quantity: number) => {
    if (quantity <= 0) {
      get().removeFromCart(cartItemId);
      return;
    }

    const state = get();
    const newCartItems = state.cartItems.map(item => {
      if (item.id === cartItemId) {
        const newTotalPrice = item.unit_price * quantity;
        return {
          ...item,
          quantity,
          total_price: newTotalPrice,
        };
      }
      return item;
    });

    const newTotal = newCartItems.reduce((sum, item) => sum + (item.total_price || 0), 0);
    const newItemCount = newCartItems.reduce((sum, item) => sum + (item.quantity || 0), 0);

    set({
      cartItems: newCartItems,
      cartTotal: newTotal,
      cartItemCount: newItemCount,
    });
  },

  clearCart: () => {
    set({
      cartItems: [],
      cartTotal: 0,
      cartItemCount: 0,
    });
  },

  // Customer actions
  setCurrentCustomer: (customer: Customer | null) => {
    set({ currentCustomer: customer });
  },

  // UI actions
  setLoading: (loading: boolean) => {
    set({ isLoading: loading });
  },

  setSelectedCategory: (category: string | null) => {
    set({ selectedCategory: category });
  },

  setSearchQuery: (query: string) => {
    set({ searchQuery: query });
  },

  // Computed values
  getCartItemById: (id: string) => {
    return get().cartItems.find(item => item.id === id);
  },

  getCartItemByItemId: (itemId: string, type: 'product' | 'service') => {
    return get().cartItems.find(item =>
      item.item_id === itemId && item.type === type
    );
  },
}));

// Selector hooks for better performance
export const useCartItems = () => usePOSStore(state => state.cartItems);
export const useCartTotal = () => usePOSStore(state => state.cartTotal);
export const useCartItemCount = () => usePOSStore(state => state.cartItemCount);
export const useCurrentCustomer = () => usePOSStore(state => state.currentCustomer);
export const usePOSLoading = () => usePOSStore(state => state.isLoading);
export const useSelectedCategory = () => usePOSStore(state => state.selectedCategory);
export const useSearchQuery = () => usePOSStore(state => state.searchQuery);
