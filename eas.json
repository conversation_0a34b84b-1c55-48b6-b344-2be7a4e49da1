{"cli": {"version": ">= 16.17.3", "appVersionSource": "remote"}, "build": {"development": {"developmentClient": true, "distribution": "internal", "android": {"buildType": "apk", "gradleCommand": ":app:assembleDebug"}, "env": {"EXPO_PUBLIC_ENVIRONMENT": "development"}}, "preview": {"distribution": "internal", "android": {"buildType": "apk"}, "env": {"EXPO_PUBLIC_ENVIRONMENT": "staging"}}, "production": {"autoIncrement": true, "android": {"buildType": "aab"}, "env": {"EXPO_PUBLIC_ENVIRONMENT": "production"}}}, "submit": {"production": {}}}