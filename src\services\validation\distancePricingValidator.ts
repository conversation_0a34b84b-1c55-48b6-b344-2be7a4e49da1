/**
 * Ocean Soul Sparkles Mobile App - Distance Pricing Validation System
 * Validates distance pricing calculations, configurations, and system accuracy
 */

import { distancePricingService, PricingCalculationResult, LocationData } from '@/services/pricing/distancePricingService';
import { pricingConfigurationManager, PricingConfiguration } from '@/services/pricing/pricingConfigurationManager';
import { locationCaptureService } from '@/services/location/locationCaptureService';
import { Service } from '@/types/database';

export interface PricingValidationResult {
  test: string;
  status: 'pass' | 'fail' | 'warning';
  message: string;
  duration: number;
  details?: any;
  error?: string;
}

export interface DistancePricingStatus {
  configurationValid: boolean;
  calculationAccuracy: boolean;
  locationServices: boolean;
  performanceOptimal: boolean;
  overall: boolean;
}

export interface PricingAccuracyTest {
  testCase: string;
  expectedPrice: number;
  actualPrice: number;
  accuracy: number; // percentage
  withinTolerance: boolean;
}

export class DistancePricingValidator {
  private static instance: DistancePricingValidator;

  // Test tolerance for pricing accuracy (5%)
  private readonly PRICING_TOLERANCE = 0.05;

  // Performance thresholds
  private readonly PERFORMANCE_THRESHOLDS = {
    calculationTime: 2000, // 2 seconds
    geocodingTime: 3000,   // 3 seconds
    configLoadTime: 1000,  // 1 second
  };

  // Test locations for validation
  private readonly TEST_LOCATIONS: LocationData[] = [
    {
      address: 'Sydney CBD, NSW, Australia',
      city: 'Sydney',
      state: 'NSW',
      postal_code: '2000',
      latitude: -33.8688,
      longitude: 151.2093,
    },
    {
      address: 'Parramatta, NSW, Australia',
      city: 'Parramatta',
      state: 'NSW',
      postal_code: '2150',
      latitude: -33.8150,
      longitude: 151.0000,
    },
    {
      address: 'Newcastle, NSW, Australia',
      city: 'Newcastle',
      state: 'NSW',
      postal_code: '2300',
      latitude: -32.9267,
      longitude: 151.7789,
    },
  ];

  private constructor() {}

  public static getInstance(): DistancePricingValidator {
    if (!DistancePricingValidator.instance) {
      DistancePricingValidator.instance = new DistancePricingValidator();
    }
    return DistancePricingValidator.instance;
  }

  /**
   * Validate pricing configuration
   */
  async validatePricingConfiguration(): Promise<PricingValidationResult> {
    const startTime = Date.now();
    
    try {
      console.log('⚙️ Validating pricing configuration...');

      const config = await pricingConfigurationManager.getActivePricingConfiguration();
      
      if (!config) {
        return {
          test: 'Pricing Configuration',
          status: 'fail',
          message: 'No active pricing configuration found',
          duration: Date.now() - startTime,
          error: 'Missing configuration',
        };
      }

      // Validate configuration structure
      const validation = pricingConfigurationManager.validatePricingConfiguration(config);
      
      if (!validation.isValid) {
        return {
          test: 'Pricing Configuration',
          status: 'fail',
          message: `Configuration validation failed: ${validation.errors.join(', ')}`,
          duration: Date.now() - startTime,
          details: { errors: validation.errors, warnings: validation.warnings },
          error: 'Invalid configuration',
        };
      }

      // Check for warnings
      const status = validation.warnings.length > 0 ? 'warning' : 'pass';
      const message = validation.warnings.length > 0 
        ? `Configuration valid with warnings: ${validation.warnings.join(', ')}`
        : 'Pricing configuration is valid';

      return {
        test: 'Pricing Configuration',
        status,
        message,
        duration: Date.now() - startTime,
        details: {
          config: pricingConfigurationManager.getConfigurationSummary(config),
          warnings: validation.warnings,
        },
      };

    } catch (error) {
      return {
        test: 'Pricing Configuration',
        status: 'fail',
        message: 'Failed to validate pricing configuration',
        duration: Date.now() - startTime,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Validate pricing calculation accuracy
   */
  async validatePricingAccuracy(): Promise<PricingValidationResult> {
    const startTime = Date.now();
    
    try {
      console.log('🎯 Validating pricing calculation accuracy...');

      // Create test service
      const testService: Service = {
        id: 'test-service',
        name: 'Test Service',
        description: 'Test service for pricing validation',
        base_price: 100,
        duration_minutes: 60,
        is_active: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };

      const accuracyTests: PricingAccuracyTest[] = [];
      let totalAccuracy = 0;

      // Test pricing for different locations
      for (const location of this.TEST_LOCATIONS) {
        try {
          const result = await distancePricingService.calculatePricing(testService, location);
          
          if (result.success) {
            // Calculate expected price based on distance and tier
            const expectedPrice = this.calculateExpectedPrice(testService.base_price, result.distance_km);
            const accuracy = this.calculateAccuracy(expectedPrice, result.total_price);
            const withinTolerance = accuracy >= (1 - this.PRICING_TOLERANCE);

            accuracyTests.push({
              testCase: `${location.city} (${result.distance_km}km)`,
              expectedPrice,
              actualPrice: result.total_price,
              accuracy: accuracy * 100,
              withinTolerance,
            });

            totalAccuracy += accuracy;
          }
        } catch (testError) {
          console.warn(`Pricing test failed for ${location.city}:`, testError);
        }
      }

      if (accuracyTests.length === 0) {
        return {
          test: 'Pricing Accuracy',
          status: 'fail',
          message: 'No pricing calculations could be tested',
          duration: Date.now() - startTime,
          error: 'All test calculations failed',
        };
      }

      const averageAccuracy = totalAccuracy / accuracyTests.length;
      const failedTests = accuracyTests.filter(test => !test.withinTolerance);
      
      const status = failedTests.length === 0 ? 'pass' : 
                    failedTests.length <= accuracyTests.length * 0.2 ? 'warning' : 'fail';

      const message = status === 'pass' 
        ? `All pricing calculations accurate (${(averageAccuracy * 100).toFixed(1)}% average)`
        : `${failedTests.length}/${accuracyTests.length} calculations outside tolerance`;

      return {
        test: 'Pricing Accuracy',
        status,
        message,
        duration: Date.now() - startTime,
        details: {
          averageAccuracy: averageAccuracy * 100,
          tolerance: this.PRICING_TOLERANCE * 100,
          tests: accuracyTests,
          failedTests: failedTests.length,
        },
      };

    } catch (error) {
      return {
        test: 'Pricing Accuracy',
        status: 'fail',
        message: 'Failed to validate pricing accuracy',
        duration: Date.now() - startTime,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Validate location services functionality
   */
  async validateLocationServices(): Promise<PricingValidationResult> {
    const startTime = Date.now();
    
    try {
      console.log('📍 Validating location services...');

      // Test location services availability
      const servicesEnabled = await locationCaptureService.isLocationServicesEnabled();
      if (!servicesEnabled) {
        return {
          test: 'Location Services',
          status: 'warning',
          message: 'Location services not available on device',
          duration: Date.now() - startTime,
          details: { servicesEnabled: false },
        };
      }

      // Test geocoding functionality
      const testAddress = 'Sydney, NSW, Australia';
      const geocodeResult = await locationCaptureService.geocodeAddress(testAddress);
      
      if (!geocodeResult.success) {
        return {
          test: 'Location Services',
          status: 'fail',
          message: 'Geocoding service not working',
          duration: Date.now() - startTime,
          error: geocodeResult.error,
        };
      }

      // Test address validation
      const validationResult = await locationCaptureService.validateAddress({
        city: 'Sydney',
        state: 'NSW',
        postalCode: '2000',
      });

      const status = validationResult.isValid ? 'pass' : 'warning';
      const message = validationResult.isValid 
        ? 'Location services fully functional'
        : 'Location services working with limitations';

      return {
        test: 'Location Services',
        status,
        message,
        duration: Date.now() - startTime,
        details: {
          servicesEnabled,
          geocodingWorking: geocodeResult.success,
          validationWorking: validationResult.isValid,
          testLocation: geocodeResult.location,
        },
      };

    } catch (error) {
      return {
        test: 'Location Services',
        status: 'fail',
        message: 'Failed to validate location services',
        duration: Date.now() - startTime,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Validate pricing calculation performance
   */
  async validatePricingPerformance(): Promise<PricingValidationResult> {
    const startTime = Date.now();
    
    try {
      console.log('⚡ Validating pricing calculation performance...');

      const testService: Service = {
        id: 'perf-test-service',
        name: 'Performance Test Service',
        description: 'Service for performance testing',
        base_price: 100,
        duration_minutes: 60,
        is_active: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };

      const performanceTests = [];

      // Test configuration loading performance
      const configStartTime = Date.now();
      await pricingConfigurationManager.getActivePricingConfiguration();
      const configLoadTime = Date.now() - configStartTime;

      performanceTests.push({
        test: 'Configuration Loading',
        duration: configLoadTime,
        threshold: this.PERFORMANCE_THRESHOLDS.configLoadTime,
        passed: configLoadTime <= this.PERFORMANCE_THRESHOLDS.configLoadTime,
      });

      // Test pricing calculation performance
      const calcStartTime = Date.now();
      await distancePricingService.calculatePricing(testService, this.TEST_LOCATIONS[0]);
      const calcTime = Date.now() - calcStartTime;

      performanceTests.push({
        test: 'Pricing Calculation',
        duration: calcTime,
        threshold: this.PERFORMANCE_THRESHOLDS.calculationTime,
        passed: calcTime <= this.PERFORMANCE_THRESHOLDS.calculationTime,
      });

      // Test geocoding performance
      const geocodeStartTime = Date.now();
      await locationCaptureService.geocodeAddress('Sydney, NSW, Australia');
      const geocodeTime = Date.now() - geocodeStartTime;

      performanceTests.push({
        test: 'Geocoding',
        duration: geocodeTime,
        threshold: this.PERFORMANCE_THRESHOLDS.geocodingTime,
        passed: geocodeTime <= this.PERFORMANCE_THRESHOLDS.geocodingTime,
      });

      const failedTests = performanceTests.filter(test => !test.passed);
      const status = failedTests.length === 0 ? 'pass' : 
                    failedTests.length <= 1 ? 'warning' : 'fail';

      const message = status === 'pass' 
        ? 'All performance tests passed'
        : `${failedTests.length} performance tests failed`;

      return {
        test: 'Pricing Performance',
        status,
        message,
        duration: Date.now() - startTime,
        details: {
          tests: performanceTests,
          failedTests: failedTests.length,
          averageTime: performanceTests.reduce((sum, test) => sum + test.duration, 0) / performanceTests.length,
        },
      };

    } catch (error) {
      return {
        test: 'Pricing Performance',
        status: 'fail',
        message: 'Failed to validate pricing performance',
        duration: Date.now() - startTime,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Get distance pricing system status
   */
  async getDistancePricingStatus(): Promise<DistancePricingStatus> {
    try {
      const [configResult, accuracyResult, locationResult, performanceResult] = await Promise.all([
        this.validatePricingConfiguration(),
        this.validatePricingAccuracy(),
        this.validateLocationServices(),
        this.validatePricingPerformance(),
      ]);

      const status = {
        configurationValid: configResult.status === 'pass',
        calculationAccuracy: accuracyResult.status === 'pass',
        locationServices: locationResult.status === 'pass',
        performanceOptimal: performanceResult.status === 'pass',
        overall: false,
      };

      // Overall status is true if critical components are working
      status.overall = status.configurationValid && 
                      status.calculationAccuracy && 
                      (status.locationServices || locationResult.status === 'warning');

      return status;

    } catch (error) {
      console.error('❌ Failed to get distance pricing status:', error);
      return {
        configurationValid: false,
        calculationAccuracy: false,
        locationServices: false,
        performanceOptimal: false,
        overall: false,
      };
    }
  }

  /**
   * Run comprehensive distance pricing validation
   */
  async runComprehensiveDistancePricingValidation(): Promise<PricingValidationResult[]> {
    console.log('🧪 Running comprehensive distance pricing validation...');

    const results = await Promise.all([
      this.validatePricingConfiguration(),
      this.validatePricingAccuracy(),
      this.validateLocationServices(),
      this.validatePricingPerformance(),
    ]);

    const passedTests = results.filter(r => r.status === 'pass').length;
    const totalTests = results.length;

    console.log(`✅ Distance pricing validation completed: ${passedTests}/${totalTests} tests passed`);

    return results;
  }

  /**
   * Calculate expected price for validation
   */
  private calculateExpectedPrice(basePrice: number, distance: number): number {
    // This is a simplified calculation for validation
    // In practice, this would use the actual pricing configuration
    if (distance <= 10) return basePrice; // Local
    if (distance <= 25) return basePrice * 1.2 + 15; // Metro
    if (distance <= 50) return basePrice * 1.5 + 30; // Regional
    return basePrice * 2.0 + 50; // Extended
  }

  /**
   * Calculate accuracy percentage
   */
  private calculateAccuracy(expected: number, actual: number): number {
    if (expected === 0) return actual === 0 ? 1 : 0;
    return 1 - Math.abs(expected - actual) / expected;
  }
}

// Export singleton instance
export const distancePricingValidator = DistancePricingValidator.getInstance();
