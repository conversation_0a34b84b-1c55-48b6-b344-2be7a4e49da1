/**
 * Ocean Soul Sparkles Mobile App - Booking Service
 * Handles booking/appointment management and queries
 */

import { supabase } from './supabase';
import { Booking, Customer, Service, AdminUser, DatabaseResponse, DatabaseListResponse, QueryFilters } from '@/types/database';
import { DatabaseInspector } from '@/utils/databaseInspector';
import { queryPerformanceMonitor } from '@/utils/queryPerformanceMonitor';

export class BookingService {

  // Dynamic table names - will be determined at runtime
  private static BOOKING_TABLE = 'bookings';
  private static tableInitialized = false;
  private static queryType: 'full' | 'no_relations' | 'basic' = 'full';

  /**
   * Initialize the correct table names by testing the database
   */
  private static async initializeTables(): Promise<void> {
    if (this.tableInitialized) return;

    try {
      console.log('🔍 Initializing booking tables...');
      
      // Test different possible booking table names
      const possibleTables = ['bookings', 'appointments', 'reservations', 'schedules'];
      
      for (const tableName of possibleTables) {
        try {
          const { data, error } = await supabase
            .from(tableName)
            .select('*')
            .limit(1);
          
          if (!error) {
            this.BOOKING_TABLE = tableName;
            console.log(`✅ Found booking table: ${tableName}`);
            break;
          }
        } catch (err) {
          console.log(`❌ Table '${tableName}' not accessible`);
        }
      }

      // Test what relationships work
      await this.testRelationships();
      
      this.tableInitialized = true;
    } catch (error) {
      console.error('❌ Error initializing booking tables:', error);
      // Keep default table names
    }
  }

  /**
   * Test which relationships work with the booking table
   */
  private static async testRelationships(): Promise<void> {
    try {
      // Test full relationships
      const { data, error } = await supabase
        .from(this.BOOKING_TABLE)
        .select(`
          *,
          customer:customers(*),
          staff:admin_users(*),
          service:services(*),
          quotes:quotes(*)
        `)
        .limit(1);

      if (!error) {
        this.queryType = 'full';
        console.log('✅ Full relationships work');
        return;
      }

      // Test without relationships
      const { data: basicData, error: basicError } = await supabase
        .from(this.BOOKING_TABLE)
        .select('*')
        .limit(1);

      if (!basicError) {
        this.queryType = 'basic';
        console.log('✅ Basic query works, will enrich manually');
      } else {
        console.log('❌ No booking table accessible');
      }
    } catch (error) {
      console.error('❌ Error testing relationships:', error);
    }
  }

  /**
   * Build the appropriate select query based on available relationships
   */
  private static getSelectQuery(): string {
    switch (this.queryType) {
      case 'full':
        return `
          *,
          customer:customers(*),
          staff:admin_users(*),
          service:services(*),
          quotes:quotes(*)
        `;
      case 'basic':
      default:
        return '*';
    }
  }

  /**
   * Enrich booking data with missing relationships
   */
  private static async enrichBookingData(bookings: any[]): Promise<Booking[]> {
    if (!bookings || bookings.length === 0) return [];

    // If we already have full data, return as is
    if (this.queryType === 'full') {
      return bookings.map(b => this.mapBookingFields(b));
    }

    // For basic queries, manually fetch related data
    const enrichedBookings = await Promise.all(
      bookings.map(async (booking) => {
        let customer = null;
        let staff = null;
        let service = null;

        // Fetch customer data
        if (booking.customer_id) {
          try {
            const { data: customerData } = await supabase
              .from('customers')
              .select('*')
              .eq('id', booking.customer_id)
              .single();
            customer = customerData;
          } catch (error) {
            console.log('Could not fetch customer data for booking:', booking.id);
          }
        }

        // Fetch staff data
        if (booking.staff_id) {
          try {
            const { data: staffData } = await supabase
              .from('admin_users')
              .select('*')
              .eq('id', booking.staff_id)
              .single();
            staff = staffData;
          } catch (error) {
            console.log('Could not fetch staff data for booking:', booking.id);
          }
        }

        // Fetch service data
        if (booking.service_id) {
          try {
            const { data: serviceData } = await supabase
              .from('services')
              .select('*')
              .eq('id', booking.service_id)
              .single();
            service = serviceData;
          } catch (error) {
            console.log('Could not fetch service data for booking:', booking.id);
          }
        }

        return this.mapBookingFields({
          ...booking,
          customer,
          staff,
          service
        });
      })
    );

    return enrichedBookings;
  }

  /**
   * Map booking fields to handle different possible column names
   */
  private static mapBookingFields(booking: any): Booking {
    return {
      id: booking.id,
      customer_id: booking.customer_id,
      staff_id: booking.staff_id || booking.user_id,
      service_id: booking.service_id,
      booking_date: booking.booking_date || booking.appointment_date || booking.date,
      start_time: booking.start_time || booking.time,
      end_time: booking.end_time,
      status: booking.status || 'pending',
      total_amount: booking.total_amount || booking.amount,
      notes: booking.notes || booking.description,
      created_at: booking.created_at,
      updated_at: booking.updated_at || booking.created_at,
      // Relations
      customer: booking.customer,
      staff: booking.staff,
      service: booking.service,
    };
  }

  /**
   * Get all bookings with optional filtering
   */
  async getBookings(filters?: QueryFilters): Promise<DatabaseListResponse<Booking>> {
    try {
      // Ensure tables are initialized
      await BookingService.initializeTables();

      console.log('📅 Loading bookings...');
      
      let query = supabase
        .from(BookingService.BOOKING_TABLE)
        .select(BookingService.getSelectQuery());

      // Apply search filter
      if (filters?.search) {
        query = query.or(`notes.ilike.%${filters.search}%`);
      }

      // Apply date filters
      if (filters?.filters?.date_from) {
        query = query.gte('booking_date', filters.filters.date_from);
      }
      if (filters?.filters?.date_to) {
        query = query.lte('booking_date', filters.filters.date_to);
      }

      // Apply status filter
      if (filters?.filters?.status) {
        query = query.eq('status', filters.filters.status);
      }

      // Apply ordering
      const orderBy = filters?.order_by || 'booking_date';
      const orderDirection = filters?.order_direction || 'asc';
      query = query.order(orderBy, { ascending: orderDirection === 'asc' });

      // Apply pagination
      if (filters?.limit) {
        query = query.limit(filters.limit);
      }
      if (filters?.offset) {
        query = query.range(filters.offset, filters.offset + (filters.limit || 20) - 1);
      }

      const { data, error, count } = await queryPerformanceMonitor.monitorQuery(
        'getBookings',
        BookingService.BOOKING_TABLE,
        () => query,
        {
          hasFilters: !!(filters?.filters),
          hasSearch: !!(filters?.search),
          limit: filters?.limit || 50,
        }
      );

      if (error) {
        console.error('❌ Get bookings error:', error);
        return { data: null, error, count: 0 };
      }

      // Enrich the data with missing relationships
      const enrichedData = await BookingService.enrichBookingData(data || []);

      console.log(`✅ Loaded ${enrichedData.length} bookings`);
      return { data: enrichedData, error: null, count: count || enrichedData.length };
    } catch (error) {
      console.error('❌ Get bookings service error:', error);
      return { data: null, error: error as Error, count: 0 };
    }
  }

  /**
   * Get booking by ID with all relations
   */
  async getBookingById(id: string): Promise<DatabaseResponse<Booking>> {
    try {
      // Ensure tables are initialized
      await BookingService.initializeTables();

      const { data, error } = await supabase
        .from(BookingService.BOOKING_TABLE)
        .select(BookingService.getSelectQuery())
        .eq('id', id)
        .single();

      if (error) {
        console.error('❌ Get booking error:', error);
        return { data: null, error };
      }

      // Enrich the single booking data
      const enrichedData = data ? (await BookingService.enrichBookingData([data]))[0] : null;

      return { data: enrichedData, error: null };
    } catch (error) {
      console.error('❌ Get booking service error:', error);
      return { data: null, error: error as Error };
    }
  }

  /**
   * Get today's bookings
   */
  async getTodaysBookings(): Promise<DatabaseListResponse<Booking>> {
    const today = new Date().toISOString().split('T')[0];
    return this.getBookings({
      filters: {
        date_from: today,
        date_to: today,
      },
      order_by: 'start_time',
      order_direction: 'asc',
    });
  }

  /**
   * Get upcoming bookings
   */
  async getUpcomingBookings(days: number = 7): Promise<DatabaseListResponse<Booking>> {
    const today = new Date().toISOString().split('T')[0];
    const futureDate = new Date();
    futureDate.setDate(futureDate.getDate() + days);
    const futureDateStr = futureDate.toISOString().split('T')[0];

    return this.getBookings({
      filters: {
        date_from: today,
        date_to: futureDateStr,
        status: 'confirmed',
      },
      order_by: 'booking_date',
      order_direction: 'asc',
      limit: 20,
    });
  }

  /**
   * Create a new booking
   */
  async createBooking(bookingData: Partial<Booking>): Promise<DatabaseResponse<Booking>> {
    try {
      await BookingService.initializeTables();

      console.log('📝 Creating new booking...');

      const { data, error } = await supabase
        .from(BookingService.BOOKING_TABLE)
        .insert([{
          customer_id: bookingData.customer_id,
          staff_id: bookingData.staff_id,
          service_id: bookingData.service_id,
          booking_date: bookingData.booking_date,
          start_time: bookingData.start_time,
          end_time: bookingData.end_time,
          status: bookingData.status || 'pending',
          total_amount: bookingData.total_amount,
          notes: bookingData.notes,
        }])
        .select(BookingService.getSelectQuery())
        .single();

      if (error) {
        console.error('❌ Create booking error:', error);
        return { data: null, error };
      }

      const enrichedData = data ? (await BookingService.enrichBookingData([data]))[0] : null;
      console.log('✅ Booking created successfully');
      return { data: enrichedData, error: null };
    } catch (error) {
      console.error('❌ Create booking service error:', error);
      return { data: null, error: error as Error };
    }
  }

  /**
   * Update an existing booking
   */
  async updateBooking(id: string, updateData: Partial<Booking>): Promise<DatabaseResponse<Booking>> {
    try {
      await BookingService.initializeTables();

      console.log('📝 Updating booking:', id);

      const { data, error } = await supabase
        .from(BookingService.BOOKING_TABLE)
        .update({
          customer_id: updateData.customer_id,
          staff_id: updateData.staff_id,
          service_id: updateData.service_id,
          booking_date: updateData.booking_date,
          start_time: updateData.start_time,
          end_time: updateData.end_time,
          status: updateData.status,
          total_amount: updateData.total_amount,
          notes: updateData.notes,
          updated_at: new Date().toISOString(),
        })
        .eq('id', id)
        .select(BookingService.getSelectQuery())
        .single();

      if (error) {
        console.error('❌ Update booking error:', error);
        return { data: null, error };
      }

      const enrichedData = data ? (await BookingService.enrichBookingData([data]))[0] : null;
      console.log('✅ Booking updated successfully');
      return { data: enrichedData, error: null };
    } catch (error) {
      console.error('❌ Update booking service error:', error);
      return { data: null, error: error as Error };
    }
  }

  /**
   * Delete a booking
   */
  async deleteBooking(id: string): Promise<DatabaseResponse<boolean>> {
    try {
      await BookingService.initializeTables();

      console.log('🗑️ Deleting booking:', id);

      const { error } = await supabase
        .from(BookingService.BOOKING_TABLE)
        .delete()
        .eq('id', id);

      if (error) {
        console.error('❌ Delete booking error:', error);
        return { data: null, error };
      }

      console.log('✅ Booking deleted successfully');
      return { data: true, error: null };
    } catch (error) {
      console.error('❌ Delete booking service error:', error);
      return { data: null, error: error as Error };
    }
  }

  /**
   * Update booking status
   */
  async updateBookingStatus(id: string, status: Booking['status']): Promise<DatabaseResponse<Booking>> {
    try {
      await BookingService.initializeTables();

      console.log('📝 Updating booking status:', id, 'to', status);

      const { data, error } = await supabase
        .from(BookingService.BOOKING_TABLE)
        .update({
          status,
          updated_at: new Date().toISOString(),
        })
        .eq('id', id)
        .select(BookingService.getSelectQuery())
        .single();

      if (error) {
        console.error('❌ Update booking status error:', error);
        return { data: null, error };
      }

      const enrichedData = data ? (await BookingService.enrichBookingData([data]))[0] : null;
      console.log('✅ Booking status updated successfully');
      return { data: enrichedData, error: null };
    } catch (error) {
      console.error('❌ Update booking status service error:', error);
      return { data: null, error: error as Error };
    }
  }
}

// Export singleton instance
export const bookingService = new BookingService();
