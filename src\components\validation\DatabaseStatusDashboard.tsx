/**
 * Ocean Soul Sparkles Mobile App - Database Status Dashboard
 * Shows comprehensive database integration status and validation results
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  ScrollView,
  Modal,
} from 'react-native';
import { databaseIntegrationSuite, DatabaseIntegrationReport } from '@/services/validation/databaseIntegrationSuite';
import { databaseSchemaValidator, DatabaseSchemaStatus } from '@/services/validation/databaseSchemaValidator';
import { databasePerformanceValidator, DatabasePerformanceStatus } from '@/services/validation/databasePerformanceValidator';

interface DatabaseStatusDashboardProps {
  showDetails?: boolean;
  onStatusChange?: (status: DatabaseSchemaStatus & DatabasePerformanceStatus) => void;
  autoRefresh?: boolean;
  refreshInterval?: number;
}

const DatabaseStatusDashboard: React.FC<DatabaseStatusDashboardProps> = ({
  showDetails = true,
  onStatusChange,
  autoRefresh = false,
  refreshInterval = 60000, // 1 minute
}) => {
  const [schemaStatus, setSchemaStatus] = useState<DatabaseSchemaStatus | null>(null);
  const [performanceStatus, setPerformanceStatus] = useState<DatabasePerformanceStatus | null>(null);
  const [loading, setLoading] = useState(false);
  const [lastChecked, setLastChecked] = useState<Date | null>(null);
  const [showDetailedReport, setShowDetailedReport] = useState(false);
  const [fullReport, setFullReport] = useState<DatabaseIntegrationReport | null>(null);

  useEffect(() => {
    checkDatabaseStatus();
    
    if (autoRefresh) {
      const interval = setInterval(checkDatabaseStatus, refreshInterval);
      return () => clearInterval(interval);
    }
  }, [autoRefresh, refreshInterval]);

  const checkDatabaseStatus = async () => {
    try {
      setLoading(true);
      
      const [schema, performance] = await Promise.all([
        databaseSchemaValidator.getDatabaseSchemaStatus(),
        databasePerformanceValidator.getDatabasePerformanceStatus(),
      ]);
      
      setSchemaStatus(schema);
      setPerformanceStatus(performance);
      setLastChecked(new Date());
      
      if (onStatusChange) {
        onStatusChange({ ...schema, ...performance });
      }
    } catch (error) {
      console.error('❌ Failed to check database status:', error);
      setSchemaStatus({
        coreTablesExist: false,
        transactionTablesExist: false,
        constraintsValid: false,
        indexesOptimal: false,
        rlsPoliciesActive: false,
        overall: false,
      });
      setPerformanceStatus({
        queryPerformance: false,
        indexOptimization: false,
        connectionStability: false,
        memoryUsage: false,
        overall: false,
      });
    } finally {
      setLoading(false);
    }
  };

  const runComprehensiveValidation = async () => {
    try {
      setLoading(true);
      
      Alert.alert(
        'Running Database Validation',
        'This will test all database integration aspects including schema, functions, and performance. This may take a few minutes.',
        [
          { text: 'Cancel', style: 'cancel' },
          {
            text: 'Run Tests',
            onPress: async () => {
              try {
                const report = await databaseIntegrationSuite.runCompleteDatabaseValidation();
                setFullReport(report);
                setSchemaStatus(report.status.schema);
                setPerformanceStatus(report.status.performance);
                setLastChecked(new Date());
                
                if (onStatusChange) {
                  onStatusChange({ ...report.status.schema, ...report.status.performance });
                }

                // Show summary alert
                Alert.alert(
                  `Database Validation ${report.overall.toUpperCase()}`,
                  `${report.summary.passedTests}/${report.summary.totalTests} tests passed (${report.summary.successRate}%)\n\n` +
                  `Average Query Time: ${report.performanceMetrics.averageQueryTime}ms\n` +
                  `${report.criticalIssues.length > 0 ? `Critical Issues: ${report.criticalIssues.length}\n` : ''}` +
                  'Tap "View Report" to see detailed results.',
                  [
                    { text: 'OK' },
                    {
                      text: 'View Report',
                      onPress: () => setShowDetailedReport(true),
                    },
                  ]
                );
              } catch (error) {
                Alert.alert('Validation Failed', 'Failed to run comprehensive database validation');
              } finally {
                setLoading(false);
              }
            },
          },
        ]
      );
    } catch (error) {
      console.error('❌ Comprehensive database validation error:', error);
      Alert.alert('Error', 'Failed to start comprehensive database validation');
      setLoading(false);
    }
  };

  const getOverallStatus = () => {
    if (!schemaStatus || !performanceStatus) return 'unknown';
    if (schemaStatus.overall && performanceStatus.overall) return 'excellent';
    if (schemaStatus.coreTablesExist && performanceStatus.connectionStability) return 'good';
    if (schemaStatus.coreTablesExist) return 'warning';
    return 'critical';
  };

  const getStatusColor = () => {
    const status = getOverallStatus();
    switch (status) {
      case 'excellent': return '#10b981';
      case 'good': return '#3b82f6';
      case 'warning': return '#f59e0b';
      case 'critical': return '#ef4444';
      default: return '#6b7280';
    }
  };

  const getStatusText = () => {
    if (loading) return 'Checking Database...';
    const status = getOverallStatus();
    switch (status) {
      case 'excellent': return 'Database Excellent';
      case 'good': return 'Database Good';
      case 'warning': return 'Database Issues';
      case 'critical': return 'Database Critical';
      default: return 'Database Unknown';
    }
  };

  const getStatusIcon = () => {
    if (loading) return '⏳';
    const status = getOverallStatus();
    switch (status) {
      case 'excellent': return '🚀';
      case 'good': return '✅';
      case 'warning': return '⚠️';
      case 'critical': return '🚨';
      default: return '❓';
    }
  };

  if (!showDetails) {
    return (
      <View style={[styles.simpleIndicator, { backgroundColor: getStatusColor() }]}>
        <Text style={styles.simpleText}>
          {getStatusIcon()} Database
        </Text>
      </View>
    );
  }

  return (
    <>
      <View style={styles.container}>
        <View style={styles.statusRow}>
          <View style={[styles.statusDot, { backgroundColor: getStatusColor() }]} />
          <Text style={styles.statusText}>{getStatusText()}</Text>
          {loading && <ActivityIndicator size="small" color={getStatusColor()} />}
        </View>

        {schemaStatus && performanceStatus && (
          <View style={styles.detailsContainer}>
            <Text style={styles.sectionTitle}>Schema Status</Text>
            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Core Tables:</Text>
              <Text style={[styles.detailValue, { color: schemaStatus.coreTablesExist ? '#10b981' : '#ef4444' }]}>
                {schemaStatus.coreTablesExist ? '✅ Available' : '❌ Missing'}
              </Text>
            </View>
            
            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Constraints:</Text>
              <Text style={[styles.detailValue, { color: schemaStatus.constraintsValid ? '#10b981' : '#ef4444' }]}>
                {schemaStatus.constraintsValid ? '✅ Valid' : '❌ Invalid'}
              </Text>
            </View>
            
            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>RLS Policies:</Text>
              <Text style={[styles.detailValue, { color: schemaStatus.rlsPoliciesActive ? '#10b981' : '#ef4444' }]}>
                {schemaStatus.rlsPoliciesActive ? '✅ Active' : '❌ Inactive'}
              </Text>
            </View>

            <Text style={[styles.sectionTitle, { marginTop: 16 }]}>Performance Status</Text>
            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Query Performance:</Text>
              <Text style={[styles.detailValue, { color: performanceStatus.queryPerformance ? '#10b981' : '#ef4444' }]}>
                {performanceStatus.queryPerformance ? '✅ Good' : '❌ Poor'}
              </Text>
            </View>
            
            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Connection:</Text>
              <Text style={[styles.detailValue, { color: performanceStatus.connectionStability ? '#10b981' : '#ef4444' }]}>
                {performanceStatus.connectionStability ? '✅ Stable' : '❌ Unstable'}
              </Text>
            </View>
            
            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Indexes:</Text>
              <Text style={[styles.detailValue, { color: performanceStatus.indexOptimization ? '#10b981' : '#f59e0b' }]}>
                {performanceStatus.indexOptimization ? '✅ Optimized' : '⚠️ Needs Work'}
              </Text>
            </View>
          </View>
        )}

        <View style={styles.buttonRow}>
          <TouchableOpacity
            style={[styles.button, styles.refreshButton]}
            onPress={checkDatabaseStatus}
            disabled={loading}
          >
            <Text style={styles.buttonText}>Refresh</Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[styles.button, styles.testButton]}
            onPress={runComprehensiveValidation}
            disabled={loading}
          >
            <Text style={styles.buttonText}>Full Test</Text>
          </TouchableOpacity>
        </View>

        {fullReport && (
          <TouchableOpacity
            style={[styles.button, styles.reportButton]}
            onPress={() => setShowDetailedReport(true)}
          >
            <Text style={styles.buttonText}>
              View Report ({fullReport.summary.successRate}% success)
            </Text>
          </TouchableOpacity>
        )}

        {lastChecked && (
          <Text style={styles.lastChecked}>
            Last checked: {lastChecked.toLocaleTimeString()}
          </Text>
        )}
      </View>

      {/* Detailed Report Modal */}
      <Modal
        visible={showDetailedReport}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setShowDetailedReport(false)}
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Database Integration Report</Text>
            <TouchableOpacity
              style={styles.closeButton}
              onPress={() => setShowDetailedReport(false)}
            >
              <Text style={styles.closeButtonText}>✕</Text>
            </TouchableOpacity>
          </View>
          
          <ScrollView style={styles.modalContent}>
            {fullReport && (
              <View>
                <View style={styles.reportSummary}>
                  <Text style={styles.reportTitle}>
                    Overall Status: {fullReport.overall.toUpperCase()}
                  </Text>
                  <Text style={styles.reportStats}>
                    {fullReport.summary.passedTests}/{fullReport.summary.totalTests} tests passed 
                    ({fullReport.summary.successRate}%)
                  </Text>
                  <Text style={styles.reportDuration}>
                    Duration: {fullReport.summary.duration}ms
                  </Text>
                </View>

                <View style={styles.performanceMetricsSection}>
                  <Text style={styles.sectionTitle}>📊 Performance Metrics</Text>
                  <View style={styles.metricsGrid}>
                    <View style={styles.metricCard}>
                      <Text style={styles.metricLabel}>Avg Query Time</Text>
                      <Text style={styles.metricValue}>{fullReport.performanceMetrics.averageQueryTime}ms</Text>
                    </View>
                    <View style={styles.metricCard}>
                      <Text style={styles.metricLabel}>Index Efficiency</Text>
                      <Text style={styles.metricValue}>{fullReport.performanceMetrics.indexEfficiency}%</Text>
                    </View>
                  </View>
                  <Text style={styles.metricDetail}>
                    Slowest: {fullReport.performanceMetrics.slowestQuery}
                  </Text>
                  <Text style={styles.metricDetail}>
                    Fastest: {fullReport.performanceMetrics.fastestQuery}
                  </Text>
                </View>

                {fullReport.criticalIssues.length > 0 && (
                  <View style={styles.criticalIssuesSection}>
                    <Text style={styles.sectionTitle}>🚨 Critical Issues</Text>
                    {fullReport.criticalIssues.map((issue, index) => (
                      <Text key={index} style={styles.criticalIssue}>• {issue}</Text>
                    ))}
                  </View>
                )}

                <View style={styles.statusSection}>
                  <Text style={styles.sectionTitle}>📋 Status Overview</Text>
                  
                  <View style={styles.statusGrid}>
                    <View style={styles.statusCard}>
                      <Text style={styles.statusCardTitle}>Schema</Text>
                      <Text style={styles.statusCardValue}>
                        {fullReport.status.schema.overall ? '✅ Ready' : '❌ Issues'}
                      </Text>
                    </View>
                    
                    <View style={styles.statusCard}>
                      <Text style={styles.statusCardTitle}>Performance</Text>
                      <Text style={styles.statusCardValue}>
                        {fullReport.status.performance.overall ? '✅ Good' : '❌ Poor'}
                      </Text>
                    </View>
                    
                    <View style={styles.statusCard}>
                      <Text style={styles.statusCardTitle}>Functions</Text>
                      <Text style={styles.statusCardValue}>
                        {fullReport.status.sqlFunctionsSummary.workingFunctions}/
                        {fullReport.status.sqlFunctionsSummary.totalFunctions}
                      </Text>
                    </View>
                  </View>
                </View>

                <View style={styles.recommendationsSection}>
                  <Text style={styles.sectionTitle}>💡 Recommendations</Text>
                  {fullReport.recommendations.map((rec, index) => (
                    <Text key={index} style={styles.recommendation}>• {rec}</Text>
                  ))}
                </View>
              </View>
            )}
          </ScrollView>
        </View>
      </Modal>
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 16,
    margin: 8,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  simpleIndicator: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    alignSelf: 'flex-start',
  },
  simpleText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '600',
  },
  statusRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  statusDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 8,
  },
  statusText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    flex: 1,
  },
  detailsContainer: {
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 4,
  },
  detailLabel: {
    fontSize: 14,
    color: '#666',
    fontWeight: '500',
  },
  detailValue: {
    fontSize: 14,
    fontWeight: '600',
  },
  buttonRow: {
    flexDirection: 'row',
    gap: 8,
    marginBottom: 8,
  },
  button: {
    flex: 1,
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 6,
    alignItems: 'center',
  },
  refreshButton: {
    backgroundColor: '#6b7280',
  },
  testButton: {
    backgroundColor: '#3b82f6',
  },
  reportButton: {
    backgroundColor: '#8b5cf6',
    marginTop: 8,
  },
  buttonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
  },
  lastChecked: {
    fontSize: 12,
    color: '#9ca3af',
    textAlign: 'center',
    marginTop: 8,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
  },
  closeButton: {
    padding: 8,
  },
  closeButtonText: {
    fontSize: 18,
    color: '#666',
  },
  modalContent: {
    flex: 1,
    padding: 16,
  },
  reportSummary: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
  },
  reportTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  reportStats: {
    fontSize: 16,
    color: '#666',
    marginBottom: 4,
  },
  reportDuration: {
    fontSize: 14,
    color: '#9ca3af',
  },
  performanceMetricsSection: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
  },
  metricsGrid: {
    flexDirection: 'row',
    gap: 12,
    marginBottom: 12,
  },
  metricCard: {
    flex: 1,
    backgroundColor: '#f8f9fa',
    borderRadius: 6,
    padding: 12,
    alignItems: 'center',
  },
  metricLabel: {
    fontSize: 12,
    color: '#666',
    marginBottom: 4,
  },
  metricValue: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
  },
  metricDetail: {
    fontSize: 12,
    color: '#666',
    marginBottom: 2,
  },
  criticalIssuesSection: {
    backgroundColor: '#fef2f2',
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
    borderLeftWidth: 4,
    borderLeftColor: '#ef4444',
  },
  criticalIssue: {
    fontSize: 14,
    color: '#dc2626',
    marginBottom: 4,
  },
  statusSection: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
  },
  statusGrid: {
    flexDirection: 'row',
    gap: 12,
  },
  statusCard: {
    flex: 1,
    backgroundColor: '#f8f9fa',
    borderRadius: 6,
    padding: 12,
  },
  statusCardTitle: {
    fontSize: 12,
    color: '#666',
    marginBottom: 4,
  },
  statusCardValue: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
  },
  recommendationsSection: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 16,
  },
  recommendation: {
    fontSize: 14,
    color: '#333',
    marginBottom: 4,
  },
});

export default DatabaseStatusDashboard;
