/**
 * Ocean Soul Sparkles Mobile App - Authentication Store
 * Zustand store for authentication state management
 */

import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
// Temporarily commenting out to isolate the error
// import { AdminUser } from '@/types/database';
// Temporarily commenting out to isolate the error
// import { supabaseAuth, LoginCredentials, AuthResult } from '@/services/auth/supabaseAuth';

interface AuthState {
  // State
  user: any | null; // Temporarily using any to isolate the error
  isLoading: boolean;
  isAuthenticated: boolean;
  requiresMFA: boolean;

  // Actions
  signIn: (credentials: any) => Promise<any>;
  signOut: () => Promise<void>;
  verifyToken: () => Promise<boolean>;
  refreshToken: () => Promise<void>;
  setUser: (user: AdminUser | null) => void;
  setLoading: (loading: boolean) => void;
  setRequiresMFA: (requires: boolean) => void;
  initialize: () => Promise<void>;

  // Role-based access helpers
  hasRole: (allowedRoles: string[]) => boolean;
  isAdmin: () => boolean;
  canAccessPOS: () => boolean;
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      // Initial state
      user: null,
      isLoading: false,
      isAuthenticated: false,
      requiresMFA: false,

      // Sign in with admin authentication
      signIn: async (credentials: LoginCredentials) => {
        set({ isLoading: true, requiresMFA: false });

        try {
          const result = await supabaseAuth.signIn(credentials);

          if (result.success && result.user) {
            set({
              user: result.user,
              isAuthenticated: true,
              isLoading: false,
              requiresMFA: false,
            });
          } else if (result.requiresMFA) {
            set({
              requiresMFA: true,
              isLoading: false,
            });
          } else {
            set({
              isLoading: false,
              requiresMFA: false,
            });
          }

          return result;
        } catch (error) {
          console.error('Sign in error:', error);
          set({
            isLoading: false,
            requiresMFA: false,
          });
          return {
            success: false,
            error: 'Network error. Please try again.'
          };
        }
      },

      // Sign out action
      signOut: async () => {
        set({ isLoading: true });

        try {
          await supabaseAuth.signOut();
        } catch (error) {
          console.error('Sign out error:', error);
        } finally {
          set({
            user: null,
            isAuthenticated: false,
            isLoading: false,
            requiresMFA: false,
          });
        }
      },

      // Verify token
      verifyToken: async () => {
        try {
          const isValid = await supabaseAuth.verifyToken();
          if (!isValid) {
            get().signOut();
          }
          return isValid;
        } catch (error) {
          console.error('Token verification error:', error);
          get().signOut();
          return false;
        }
      },

      // Refresh token
      refreshToken: async () => {
        try {
          await supabaseAuth.refreshToken();
        } catch (error) {
          console.error('Token refresh error:', error);
          get().signOut();
        }
      },

      // Setters
      setUser: (user) => set({ user, isAuthenticated: !!user }),
      setLoading: (isLoading) => set({ isLoading }),
      setRequiresMFA: (requiresMFA) => set({ requiresMFA }),

      // Initialize auth state
      initialize: async () => {
        set({ isLoading: true });

        try {
          const user = await supabaseAuth.initialize();

          if (user && user.is_active) {
            set({
              user,
              isAuthenticated: true,
              isLoading: false,
              requiresMFA: false,
            });
          } else {
            set({
              user: null,
              isAuthenticated: false,
              isLoading: false,
              requiresMFA: false,
            });
          }
        } catch (error) {
          console.error('Auth initialization error:', error);
          set({
            user: null,
            isAuthenticated: false,
            isLoading: false,
            requiresMFA: false,
          });
        }
      },

      // Role-based access helpers
      hasRole: (allowedRoles: string[]) => {
        const { user } = get();
        return supabaseAuth.hasRole(user, allowedRoles);
      },

      isAdmin: () => {
        const { user } = get();
        return supabaseAuth.isAdmin(user);
      },

      canAccessPOS: () => {
        const { user } = get();
        return supabaseAuth.canAccessPOS(user);
      },
    }),
    {
      name: 'ocean-soul-auth',
      storage: createJSONStorage(() => AsyncStorage),
      // Only persist non-sensitive data
      partialize: (state) => ({
        user: state.user,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
);

// Auth helper functions
export const useAuth = () => {
  const store = useAuthStore();
  return {
    user: store.user,
    isLoading: store.isLoading,
    isAuthenticated: store.isAuthenticated,
    requiresMFA: store.requiresMFA,
    signIn: store.signIn,
    signOut: store.signOut,
    verifyToken: store.verifyToken,
    refreshToken: store.refreshToken,
    initialize: store.initialize,
    hasRole: store.hasRole,
    isAdmin: store.isAdmin,
    canAccessPOS: store.canAccessPOS,
  };
};

export const requireAuth = () => {
  const { isAuthenticated, user } = useAuthStore();

  if (!isAuthenticated || !user) {
    throw new Error('Authentication required');
  }

  return { user };
};

export const hasRole = (allowedRoles: string[]) => {
  const { user } = useAuthStore();
  return supabaseAuth.hasRole(user, allowedRoles);
};
