/**
 * Ocean Soul Sparkles Mobile App - Admin Portal Status Indicator
 * Shows the current status of admin portal integration and validation
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  ScrollView,
  Modal,
} from 'react-native';
import { adminPortalIntegrationSuite, AdminPortalIntegrationReport } from '@/services/validation/adminPortalIntegrationSuite';
import { adminPortalValidator, AdminPortalSyncStatus } from '@/services/validation/adminPortalValidator';

interface AdminPortalStatusIndicatorProps {
  showDetails?: boolean;
  onStatusChange?: (status: AdminPortalSyncStatus) => void;
  autoRefresh?: boolean;
  refreshInterval?: number;
}

const AdminPortalStatusIndicator: React.FC<AdminPortalStatusIndicatorProps> = ({
  showDetails = false,
  onStatusChange,
  autoRefresh = false,
  refreshInterval = 30000, // 30 seconds
}) => {
  const [status, setStatus] = useState<AdminPortalSyncStatus | null>(null);
  const [loading, setLoading] = useState(false);
  const [lastChecked, setLastChecked] = useState<Date | null>(null);
  const [showDetailedReport, setShowDetailedReport] = useState(false);
  const [fullReport, setFullReport] = useState<AdminPortalIntegrationReport | null>(null);

  useEffect(() => {
    checkAdminPortalStatus();
    
    if (autoRefresh) {
      const interval = setInterval(checkAdminPortalStatus, refreshInterval);
      return () => clearInterval(interval);
    }
  }, [autoRefresh, refreshInterval]);

  const checkAdminPortalStatus = async () => {
    try {
      setLoading(true);
      const adminPortalStatus = await adminPortalValidator.getAdminPortalSyncStatus();
      setStatus(adminPortalStatus);
      setLastChecked(new Date());
      
      if (onStatusChange) {
        onStatusChange(adminPortalStatus);
      }
    } catch (error) {
      console.error('❌ Failed to check admin portal status:', error);
      setStatus({
        connectivity: false,
        authentication: false,
        dataSync: false,
        apiEndpoints: false,
        overall: false,
      });
    } finally {
      setLoading(false);
    }
  };

  const runComprehensiveValidation = async () => {
    try {
      setLoading(true);
      
      Alert.alert(
        'Running Comprehensive Validation',
        'This will test all admin portal integration aspects. This may take a few minutes.',
        [
          { text: 'Cancel', style: 'cancel' },
          {
            text: 'Run Tests',
            onPress: async () => {
              try {
                const report = await adminPortalIntegrationSuite.runCompleteIntegrationValidation();
                setFullReport(report);
                setStatus(report.status.adminPortal);
                setLastChecked(new Date());
                
                if (onStatusChange) {
                  onStatusChange(report.status.adminPortal);
                }

                // Show summary alert
                Alert.alert(
                  `Validation ${report.overall.toUpperCase()}`,
                  `${report.summary.passedTests}/${report.summary.totalTests} tests passed (${report.summary.successRate}%)\n\n` +
                  `${report.criticalIssues.length > 0 ? `Critical Issues: ${report.criticalIssues.length}\n` : ''}` +
                  'Tap "View Report" to see detailed results.',
                  [
                    { text: 'OK' },
                    {
                      text: 'View Report',
                      onPress: () => setShowDetailedReport(true),
                    },
                  ]
                );
              } catch (error) {
                Alert.alert('Validation Failed', 'Failed to run comprehensive validation');
              } finally {
                setLoading(false);
              }
            },
          },
        ]
      );
    } catch (error) {
      console.error('❌ Comprehensive validation error:', error);
      Alert.alert('Error', 'Failed to start comprehensive validation');
      setLoading(false);
    }
  };

  const getStatusColor = () => {
    if (!status) return '#6b7280';
    if (status.overall) return '#10b981';
    if (status.connectivity && status.dataSync) return '#f59e0b';
    return '#ef4444';
  };

  const getStatusText = () => {
    if (loading) return 'Checking...';
    if (!status) return 'Unknown';
    if (status.overall) return 'Admin Portal Ready';
    if (status.connectivity && status.dataSync) return 'Admin Portal Partial';
    return 'Admin Portal Issues';
  };

  const getStatusIcon = () => {
    if (loading) return '⏳';
    if (!status) return '❓';
    if (status.overall) return '✅';
    if (status.connectivity && status.dataSync) return '⚠️';
    return '❌';
  };

  if (!showDetails) {
    return (
      <View style={[styles.simpleIndicator, { backgroundColor: getStatusColor() }]}>
        <Text style={styles.simpleText}>
          {getStatusIcon()} Admin Portal
        </Text>
      </View>
    );
  }

  return (
    <>
      <View style={styles.container}>
        <View style={styles.statusRow}>
          <View style={[styles.statusDot, { backgroundColor: getStatusColor() }]} />
          <Text style={styles.statusText}>{getStatusText()}</Text>
          {loading && <ActivityIndicator size="small" color={getStatusColor()} />}
        </View>

        {status && (
          <View style={styles.detailsContainer}>
            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Connectivity:</Text>
              <Text style={[styles.detailValue, { color: status.connectivity ? '#10b981' : '#ef4444' }]}>
                {status.connectivity ? '✅ Connected' : '❌ Disconnected'}
              </Text>
            </View>
            
            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Authentication:</Text>
              <Text style={[styles.detailValue, { color: status.authentication ? '#10b981' : '#ef4444' }]}>
                {status.authentication ? '✅ Authenticated' : '❌ Not Authenticated'}
              </Text>
            </View>
            
            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Data Sync:</Text>
              <Text style={[styles.detailValue, { color: status.dataSync ? '#10b981' : '#ef4444' }]}>
                {status.dataSync ? '✅ Syncing' : '❌ Not Syncing'}
              </Text>
            </View>
            
            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>API Endpoints:</Text>
              <Text style={[styles.detailValue, { color: status.apiEndpoints ? '#10b981' : '#ef4444' }]}>
                {status.apiEndpoints ? '✅ Available' : '❌ Unavailable'}
              </Text>
            </View>
          </View>
        )}

        <View style={styles.buttonRow}>
          <TouchableOpacity
            style={[styles.button, styles.refreshButton]}
            onPress={checkAdminPortalStatus}
            disabled={loading}
          >
            <Text style={styles.buttonText}>Refresh</Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[styles.button, styles.testButton]}
            onPress={runComprehensiveValidation}
            disabled={loading}
          >
            <Text style={styles.buttonText}>Full Test</Text>
          </TouchableOpacity>
        </View>

        {fullReport && (
          <TouchableOpacity
            style={[styles.button, styles.reportButton]}
            onPress={() => setShowDetailedReport(true)}
          >
            <Text style={styles.buttonText}>
              View Report ({fullReport.summary.successRate}% success)
            </Text>
          </TouchableOpacity>
        )}

        {lastChecked && (
          <Text style={styles.lastChecked}>
            Last checked: {lastChecked.toLocaleTimeString()}
          </Text>
        )}
      </View>

      {/* Detailed Report Modal */}
      <Modal
        visible={showDetailedReport}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setShowDetailedReport(false)}
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Admin Portal Integration Report</Text>
            <TouchableOpacity
              style={styles.closeButton}
              onPress={() => setShowDetailedReport(false)}
            >
              <Text style={styles.closeButtonText}>✕</Text>
            </TouchableOpacity>
          </View>
          
          <ScrollView style={styles.modalContent}>
            {fullReport && (
              <View>
                <View style={styles.reportSummary}>
                  <Text style={styles.reportTitle}>
                    Overall Status: {fullReport.overall.toUpperCase()}
                  </Text>
                  <Text style={styles.reportStats}>
                    {fullReport.summary.passedTests}/{fullReport.summary.totalTests} tests passed 
                    ({fullReport.summary.successRate}%)
                  </Text>
                  <Text style={styles.reportDuration}>
                    Duration: {fullReport.summary.duration}ms
                  </Text>
                </View>

                {fullReport.criticalIssues.length > 0 && (
                  <View style={styles.criticalIssuesSection}>
                    <Text style={styles.sectionTitle}>🚨 Critical Issues</Text>
                    {fullReport.criticalIssues.map((issue, index) => (
                      <Text key={index} style={styles.criticalIssue}>• {issue}</Text>
                    ))}
                  </View>
                )}

                <View style={styles.statusSection}>
                  <Text style={styles.sectionTitle}>📊 Status Overview</Text>
                  
                  <View style={styles.statusGrid}>
                    <View style={styles.statusCard}>
                      <Text style={styles.statusCardTitle}>Admin Portal</Text>
                      <Text style={styles.statusCardValue}>
                        {fullReport.status.adminPortal.overall ? '✅ Ready' : '❌ Issues'}
                      </Text>
                    </View>
                    
                    <View style={styles.statusCard}>
                      <Text style={styles.statusCardTitle}>Email System</Text>
                      <Text style={styles.statusCardValue}>
                        {fullReport.status.emailSystem.overall ? '✅ Ready' : '❌ Issues'}
                      </Text>
                    </View>
                  </View>
                </View>

                <View style={styles.recommendationsSection}>
                  <Text style={styles.sectionTitle}>💡 Recommendations</Text>
                  {fullReport.recommendations.map((rec, index) => (
                    <Text key={index} style={styles.recommendation}>• {rec}</Text>
                  ))}
                </View>
              </View>
            )}
          </ScrollView>
        </View>
      </Modal>
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 16,
    margin: 8,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  simpleIndicator: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    alignSelf: 'flex-start',
  },
  simpleText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '600',
  },
  statusRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  statusDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 8,
  },
  statusText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    flex: 1,
  },
  detailsContainer: {
    marginBottom: 16,
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 4,
  },
  detailLabel: {
    fontSize: 14,
    color: '#666',
    fontWeight: '500',
  },
  detailValue: {
    fontSize: 14,
    fontWeight: '600',
  },
  buttonRow: {
    flexDirection: 'row',
    gap: 8,
    marginBottom: 8,
  },
  button: {
    flex: 1,
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 6,
    alignItems: 'center',
  },
  refreshButton: {
    backgroundColor: '#6b7280',
  },
  testButton: {
    backgroundColor: '#3b82f6',
  },
  reportButton: {
    backgroundColor: '#8b5cf6',
    marginTop: 8,
  },
  buttonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
  },
  lastChecked: {
    fontSize: 12,
    color: '#9ca3af',
    textAlign: 'center',
    marginTop: 8,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
  },
  closeButton: {
    padding: 8,
  },
  closeButtonText: {
    fontSize: 18,
    color: '#666',
  },
  modalContent: {
    flex: 1,
    padding: 16,
  },
  reportSummary: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
  },
  reportTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  reportStats: {
    fontSize: 16,
    color: '#666',
    marginBottom: 4,
  },
  reportDuration: {
    fontSize: 14,
    color: '#9ca3af',
  },
  criticalIssuesSection: {
    backgroundColor: '#fef2f2',
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
    borderLeftWidth: 4,
    borderLeftColor: '#ef4444',
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
  },
  criticalIssue: {
    fontSize: 14,
    color: '#dc2626',
    marginBottom: 4,
  },
  statusSection: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
  },
  statusGrid: {
    flexDirection: 'row',
    gap: 12,
  },
  statusCard: {
    flex: 1,
    backgroundColor: '#f8f9fa',
    borderRadius: 6,
    padding: 12,
  },
  statusCardTitle: {
    fontSize: 12,
    color: '#666',
    marginBottom: 4,
  },
  statusCardValue: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
  },
  recommendationsSection: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 16,
  },
  recommendation: {
    fontSize: 14,
    color: '#333',
    marginBottom: 4,
  },
});

export default AdminPortalStatusIndicator;
