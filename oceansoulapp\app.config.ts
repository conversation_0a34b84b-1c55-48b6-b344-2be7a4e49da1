import { ConfigContext, ExpoConfig } from 'expo/config';

export default ({ config }: ConfigContext): ExpoConfig => ({
  ...config,
  name: 'Ocean Soul Sparkles',
  slug: 'oceansoulsparkles-app',
  version: '1.0.0',
  orientation: 'portrait',
  userInterfaceStyle: 'automatic',
  
  // GitHub integration settings
  owner: 'your-github-username', // Replace with your GitHub username
  
  splash: {
    resizeMode: 'contain',
    backgroundColor: '#FF9A8B'
  },
  
  assetBundlePatterns: ['**/*'],
  
  ios: {
    supportsTablet: true,
    bundleIdentifier: 'com.oceansoulsparkles.app',
    buildNumber: '1',
    infoPlist: {
      NSCameraUsageDescription: 'This app uses the camera to scan QR codes and take photos for products.',
      NSMicrophoneUsageDescription: 'This app uses the microphone for voice notes and customer communication.',
      NSLocationWhenInUseUsageDescription: 'This app uses location to provide location-based services and delivery tracking.'
    }
  },
  
  android: {
    adaptiveIcon: {
      backgroundColor: '#FF9A8B'
    },
    package: 'com.oceansoulsparkles.app',
    versionCode: 1,
    permissions: [
      'CAMERA',
      'RECORD_AUDIO',
      'ACCESS_FINE_LOCATION',
      'ACCESS_COARSE_LOCATION',
      'INTERNET',
      'ACCESS_NETWORK_STATE',
      'WRITE_EXTERNAL_STORAGE',
      'READ_EXTERNAL_STORAGE',
      'android.permission.POST_NOTIFICATIONS'
    ]
  },
  
  web: {
    bundler: 'metro'
  },
  
  plugins: [
    [
      'expo-splash-screen',
      {
        backgroundColor: '#FF9A8B'
      }
    ],
    [
      'expo-notifications',
      {
        icon: './assets/notification-icon.png',
        color: '#FF9A8B',
        sounds: ['./assets/notification-sound.wav'],
        mode: 'production'
      }
    ]
  ],
  
  extra: {
    eas: {
      projectId: 'e1330832-16b4-4a58-98d0-378f18419267'
    }
  },
  
  // Build configuration for different environments
  hooks: {
    postPublish: [
      {
        file: 'sentry-expo/upload-sourcemaps',
        config: {
          organization: 'ocean-soul-sparkles',
          project: 'mobile-app'
        }
      }
    ]
  }
});