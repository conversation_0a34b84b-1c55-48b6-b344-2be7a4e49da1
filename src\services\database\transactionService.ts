/**
 * Ocean Soul Sparkles Mobile App - Transaction Service
 * Handles transaction processing, receipt generation, and transaction history
 */

import { supabase } from './supabase';
import { Transaction, TransactionItem, CartItem, Customer, AdminUser, DatabaseResponse, DatabaseListResponse, QueryFilters, Receipt } from '@/types/database';
import { DatabaseInspector } from '@/utils/databaseInspector';
import { testDatabaseStructure, testTransactionQuery } from '@/utils/databaseTest';

export class TransactionService {

  // Dynamic table names - will be determined at runtime
  private static TRANSACTION_TABLE = 'transactions';
  private static TRANSACTION_ITEMS_TABLE = 'transaction_items';
  private static tableInitialized = false;
  private static queryType: 'full' | 'no_staff' | 'basic' = 'full';

  /**
   * Initialize the correct table names by testing the database
   */
  private static async initializeTables(): Promise<void> {
    if (this.tableInitialized) return;

    try {
      console.log('🔍 Initializing transaction tables...');

      // Test the current query to see if it works
      const testResult = await testTransactionQuery();

      if (testResult.success) {
        this.TRANSACTION_TABLE = testResult.table;
        this.queryType = testResult.queryType || 'full';
        console.log(`✅ Using transaction table: ${this.TRANSACTION_TABLE} with query type: ${this.queryType}`);
      } else {
        // Fallback to database inspection
        const bestTable = await DatabaseInspector.findBestTransactionTable();
        if (bestTable) {
          this.TRANSACTION_TABLE = bestTable;
          console.log(`🔄 Fallback to table: ${this.TRANSACTION_TABLE}`);
        } else {
          console.log('❌ No suitable transaction table found, using default');
        }
      }

      this.tableInitialized = true;
    } catch (error) {
      console.error('❌ Error initializing tables:', error);
      // Keep default table names
    }
  }

  /**
   * Build the appropriate select query based on available relationships
   */
  private static getSelectQuery(): string {
    switch (this.queryType) {
      case 'full':
        return `
          *,
          customer:customers(*),
          staff:admin_users(*),
          transaction_items(*)
        `;
      case 'no_staff':
        return `
          *,
          customer:customers(*),
          transaction_items(*)
        `;
      case 'basic':
      default:
        return '*';
    }
  }

  /**
   * Map loyalty_transactions fields to standard transaction fields
   */
  private static mapLoyaltyTransaction(loyaltyTransaction: any): any {
    // Handle different possible field names in loyalty_transactions
    const mapped = {
      id: loyaltyTransaction.id,
      transaction_number: loyaltyTransaction.transaction_number || loyaltyTransaction.reference || loyaltyTransaction.id,
      customer_id: loyaltyTransaction.customer_id,
      staff_id: loyaltyTransaction.staff_id || loyaltyTransaction.user_id,
      subtotal: loyaltyTransaction.subtotal || loyaltyTransaction.amount || loyaltyTransaction.points || 0,
      tax_amount: loyaltyTransaction.tax_amount || 0,
      total_amount: loyaltyTransaction.total_amount || loyaltyTransaction.amount || loyaltyTransaction.points || 0,
      payment_method: loyaltyTransaction.payment_method || 'loyalty',
      payment_status: loyaltyTransaction.payment_status || loyaltyTransaction.status || 'completed',
      notes: loyaltyTransaction.notes || loyaltyTransaction.description,
      created_at: loyaltyTransaction.created_at,
      updated_at: loyaltyTransaction.updated_at || loyaltyTransaction.created_at,
      // Handle loyalty-specific fields
      points: loyaltyTransaction.points,
      transaction_type: loyaltyTransaction.transaction_type || 'loyalty',
      // Keep original data for reference
      ...loyaltyTransaction
    };

    return mapped;
  }

  /**
   * Enrich transaction data with missing relationships
   */
  private static async enrichTransactionData(transactions: any[]): Promise<Transaction[]> {
    if (!transactions || transactions.length === 0) return [];

    // Map loyalty transaction fields to standard format
    const mappedTransactions = transactions.map(t => this.mapLoyaltyTransaction(t));

    // If we already have staff data, return as is
    if (this.queryType === 'full') {
      return mappedTransactions;
    }

    // If we're missing staff data, fetch it efficiently with a single query
    if (this.queryType === 'no_staff') {
      // Get all unique staff IDs to avoid N+1 queries
      const staffIds = [...new Set(
        mappedTransactions
          .map(t => t.staff_id)
          .filter(id => id != null)
      )];

      // Fetch all staff data in a single query
      let staffMap = new Map();
      if (staffIds.length > 0) {
        try {
          const { data: staffData } = await supabase
            .from('admin_users')
            .select('*')
            .in('id', staffIds);

          if (staffData) {
            staffData.forEach(staff => {
              staffMap.set(staff.id, staff);
            });
          }
        } catch (error) {
          console.log('Could not fetch staff data for transactions');
        }
      }

      // Map staff data to transactions
      const enrichedTransactions = mappedTransactions.map(transaction => ({
        ...transaction,
        staff: transaction.staff_id ? staffMap.get(transaction.staff_id) || null : null
      }));

      return enrichedTransactions;
    }

    // For basic queries, return minimal data
    return mappedTransactions.map(transaction => ({
      ...transaction,
      customer: null,
      staff: null,
      transaction_items: []
    }));
  }

  /**
   * Create a new transaction from cart items
   */
  async createTransaction(
    cartItems: CartItem[],
    staffId: string,
    customerId?: string,
    paymentMethod: 'cash' | 'card' | 'digital_wallet' = 'cash',
    notes?: string
  ): Promise<DatabaseResponse<Transaction>> {
    try {
      // Ensure tables are initialized
      await TransactionService.initializeTables();

      console.log('💳 Creating new transaction...');

      // Calculate totals
      const subtotal = cartItems.reduce((sum, item) => sum + item.total_price, 0);
      const taxRate = 0.10; // 10% GST
      const taxAmount = subtotal * taxRate;
      const totalAmount = subtotal + taxAmount;

      // Generate transaction number
      const transactionNumber = await this.generateTransactionNumber();

      // Create transaction data using the proper schema
      const transactionData = {
        transaction_number: transactionNumber,
        customer_id: customerId || null,
        staff_id: staffId,
        subtotal: Number(subtotal.toFixed(2)),
        tax_amount: Number(taxAmount.toFixed(2)),
        total_amount: Number(totalAmount.toFixed(2)),
        payment_method: paymentMethod,
        payment_status: 'completed' as const,
        notes: notes || null,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };

      console.log('📝 Creating transaction with data:', transactionData);

      // Create the transaction
      const { data: transaction, error: transactionError } = await supabase
        .from(TransactionService.TRANSACTION_TABLE)
        .insert(transactionData)
        .select()
        .single();

      if (transactionError) {
        console.error('❌ Transaction creation error:', transactionError);
        return { data: null, error: transactionError };
      }

      console.log('✅ Transaction created:', transaction.id);

      // Create transaction items
      if (cartItems.length > 0) {
        const transactionItems = cartItems.map(item => ({
          transaction_id: transaction.id,
          item_type: item.type,
          item_id: item.item_id,
          item_name: item.name,
          quantity: item.quantity,
          unit_price: Number(item.unit_price.toFixed(2)),
          total_price: Number(item.total_price.toFixed(2)),
          notes: item.notes || null,
          created_at: new Date().toISOString(),
        }));

        console.log('📝 Creating transaction items:', transactionItems);

        const { error: itemsError } = await supabase
          .from(TransactionService.TRANSACTION_ITEMS_TABLE)
          .insert(transactionItems);

        if (itemsError) {
          console.error('❌ Transaction items creation error:', itemsError);
          // Don't fail the entire transaction for items error, but log it
          console.warn('⚠️ Transaction created but items failed to save');
        } else {
          console.log('✅ Transaction items created successfully');
        }
      }

      return { data: transaction, error: null };
    } catch (error) {
      console.error('❌ Transaction service error:', error);
      return { data: null, error: error as Error };
    }
  }

  /**
   * Get transaction by ID with all relations
   */
  async getTransactionById(id: string): Promise<DatabaseResponse<Transaction>> {
    try {
      // Ensure tables are initialized
      await TransactionService.initializeTables();

      const { data, error } = await supabase
        .from(TransactionService.TRANSACTION_TABLE)
        .select(TransactionService.getSelectQuery())
        .eq('id', id)
        .single();

      if (error) {
        console.error('❌ Get transaction error:', error);
        return { data: null, error };
      }

      // Enrich the single transaction data
      const enrichedData = data ? (await TransactionService.enrichTransactionData([data]))[0] : null;

      return { data: enrichedData, error: null };
    } catch (error) {
      console.error('❌ Get transaction service error:', error);
      return { data: null, error: error as Error };
    }
  }

  /**
   * Get transaction history with filters
   */
  async getTransactions(filters?: QueryFilters): Promise<DatabaseListResponse<Transaction>> {
    try {
      // Ensure tables are initialized
      await TransactionService.initializeTables();

      let query = supabase
        .from(TransactionService.TRANSACTION_TABLE)
        .select(TransactionService.getSelectQuery());

      // Apply search filter
      if (filters?.search) {
        query = query.or(`transaction_number.ilike.%${filters.search}%,notes.ilike.%${filters.search}%`);
      }

      // Apply date filters
      if (filters?.filters?.date_from) {
        query = query.gte('created_at', filters.filters.date_from);
      }
      if (filters?.filters?.date_to) {
        query = query.lte('created_at', filters.filters.date_to);
      }

      // Apply customer filter
      if (filters?.filters?.customer_id) {
        query = query.eq('customer_id', filters.filters.customer_id);
      }

      // Apply payment status filter
      if (filters?.filters?.payment_status) {
        query = query.eq('payment_status', filters.filters.payment_status);
      }

      // Apply ordering
      const orderBy = filters?.order_by || 'created_at';
      const orderDirection = filters?.order_direction || 'desc';
      query = query.order(orderBy, { ascending: orderDirection === 'asc' });

      // Apply pagination with performance-conscious defaults
      const limit = filters?.limit || 30; // Default limit for transactions
      const offset = filters?.offset || 0;

      query = query.limit(limit);
      if (offset > 0) {
        query = query.range(offset, offset + limit - 1);
      }

      const { data, error, count } = await query;

      if (error) {
        console.error('❌ Get transactions error:', error);
        return { data: null, error, count: 0 };
      }

      // Enrich the data with missing relationships
      const enrichedData = await TransactionService.enrichTransactionData(data || []);

      console.log(`✅ Loaded ${enrichedData.length} transactions`);
      return { data: enrichedData, error: null, count: count || enrichedData.length };
    } catch (error) {
      console.error('❌ Get transactions service error:', error);
      return { data: null, error: error as Error, count: 0 };
    }
  }

  /**
   * Generate unique transaction number
   */
  private async generateTransactionNumber(): Promise<string> {
    const now = new Date();
    const year = now.getFullYear().toString().slice(-2);
    const month = (now.getMonth() + 1).toString().padStart(2, '0');
    const day = now.getDate().toString().padStart(2, '0');

    // Get count of transactions today
    const startOfDay = new Date(now.getFullYear(), now.getMonth(), now.getDate()).toISOString();
    const endOfDay = new Date(now.getFullYear(), now.getMonth(), now.getDate() + 1).toISOString();

    const { count } = await supabase
      .from(TransactionService.TRANSACTION_TABLE)
      .select('id', { count: 'exact' })
      .gte('created_at', startOfDay)
      .lt('created_at', endOfDay);

    const sequence = ((count || 0) + 1).toString().padStart(3, '0');

    return `OSS${year}${month}${day}-${sequence}`;
  }

  /**
   * Generate receipt data
   */
  generateReceipt(transaction: Transaction): Receipt {
    const businessInfo = {
      name: 'Ocean Soul Sparkles',
      address: 'Australia', // Update with actual address
      phone: '+61 XXX XXX XXX', // Update with actual phone
      email: '<EMAIL>',
      website: 'oceansoulsparkles.com.au',
      abn: 'XXX XXX XXX XXX', // Update with actual ABN
    };

    return {
      transaction,
      business_info: businessInfo,
      generated_at: new Date().toISOString(),
      receipt_number: transaction.transaction_number,
    };
  }

  /**
   * Update transaction payment status
   */
  async updatePaymentStatus(
    transactionId: string,
    paymentStatus: 'pending' | 'completed' | 'failed' | 'refunded',
    squarePaymentId?: string
  ): Promise<DatabaseResponse<Transaction>> {
    try {
      const { data, error } = await supabase
        .from(TransactionService.TRANSACTION_TABLE)
        .update({
          payment_status: paymentStatus,
          square_payment_id: squarePaymentId,
          updated_at: new Date().toISOString(),
        })
        .eq('id', transactionId)
        .select()
        .single();

      if (error) {
        console.error('❌ Update payment status error:', error);
        return { data: null, error };
      }

      return { data, error: null };
    } catch (error) {
      console.error('❌ Update payment status service error:', error);
      return { data: null, error: error as Error };
    }
  }

  /**
   * Get daily sales summary
   */
  async getDailySummary(date?: string): Promise<{
    total_sales: number;
    transaction_count: number;
    average_sale: number;
    error: Error | null;
  }> {
    try {
      const targetDate = date || new Date().toISOString().split('T')[0];
      const startOfDay = `${targetDate}T00:00:00.000Z`;
      const endOfDay = `${targetDate}T23:59:59.999Z`;

      const { data, error } = await supabase
        .from(TransactionService.TRANSACTION_TABLE)
        .select('total_amount')
        .eq('payment_status', 'completed')
        .gte('created_at', startOfDay)
        .lte('created_at', endOfDay);

      if (error) {
        return { total_sales: 0, transaction_count: 0, average_sale: 0, error };
      }

      const totalSales = data.reduce((sum, t) => sum + t.total_amount, 0);
      const transactionCount = data.length;
      const averageSale = transactionCount > 0 ? totalSales / transactionCount : 0;

      return {
        total_sales: totalSales,
        transaction_count: transactionCount,
        average_sale: averageSale,
        error: null,
      };
    } catch (error) {
      return { total_sales: 0, transaction_count: 0, average_sale: 0, error: error as Error };
    }
  }
}

// Export singleton instance
export const transactionService = new TransactionService();
