# 🤖 Android Studio + Metro Development Setup

## Ocean <PERSON> Sparkles Mobile App - Android Development Guide

This guide explains how to use Android Studio for native Android development while maintaining the existing Expo web deployment.

---

## 🎯 **Development Modes**

### **1. Web Development (Unchanged)**
- **Command**: `npm run web`
- **Purpose**: Expo web development server
- **Deployment**: Vercel (https://oceansoulapp.vercel.app)
- **Use Case**: Web app development and testing

### **2. Android Studio Development (New)**
- **Command**: `npm run metro`
- **Purpose**: Metro bundler for Android Studio
- **Deployment**: Local Android development
- **Use Case**: Native Android development and debugging

---

## 🛠️ **Setup Requirements**

### **Prerequisites**
1. **Android Studio** - Download from [developer.android.com/studio](https://developer.android.com/studio)
2. **Android SDK** - Installed via Android Studio
3. **Java 11+** - Required for Android development
4. **Node.js** - Already installed for React Native

### **Environment Variables**
- **ANDROID_HOME** - Path to Android SDK
- **JAVA_HOME** - Path to Java installation

---

## 🚀 **Quick Start Guide**

### **Step 1: Verify Setup**
```bash
npm run setup:android
```
This script checks your development environment and reports any missing components.

### **Step 2: Start Metro Bundler**
```bash
npm run metro
```
This starts the Metro bundler on port 8081 for Android Studio to connect to.

### **Step 3: Open Android Studio**
1. Launch Android Studio
2. Open the `android/` folder as a project
3. Wait for Gradle sync to complete
4. Ensure Metro is running (from Step 2)

### **Step 4: Build and Run**
1. Select a device/emulator in Android Studio
2. Click the "Run" button (green play icon)
3. The app will build and install on your device
4. Metro will serve the JavaScript bundle

---

## 📋 **Available Commands**

### **Metro Bundler Commands**
```bash
npm run metro              # Start Metro bundler for Android Studio
npm run metro:reset        # Start Metro with cache reset
npm run clean:metro        # Clean Metro cache and restart
```

### **Android Development Commands**
```bash
npm run android:metro      # Run Android app with Metro
npm run dev:android        # Start Metro and Android simultaneously
npm run debug:android      # Run Android in debug mode
npm run logs:android       # View Android logs
```

### **Setup and Maintenance**
```bash
npm run setup:android      # Check development environment
```

---

## 🔧 **Configuration Details**

### **Metro Configuration**
- **Port**: 8081 (standard React Native port)
- **Host**: localhost
- **Config**: `metro.config.js` (enhanced for dual development)
- **Environment**: Development mode with debug logging

### **Android Build Configuration**
- **Package**: com.oceansoulsparkles.app
- **Build Tool**: Gradle with React Native plugin
- **Bundler**: Metro (development) / Expo (production)
- **JavaScript Engine**: Hermes (enabled)

### **Environment Variables**
Development environment uses `.env.development` with:
- Supabase production database (for consistency)
- Square payment sandbox
- Debug mode enabled
- Performance monitoring enabled

---

## 🐛 **Troubleshooting**

### **Metro Won't Start**
```bash
# Clear Metro cache
npm run clean:metro

# Check port availability
netstat -an | findstr :8081

# Kill existing Metro processes
taskkill /f /im node.exe
```

### **Android Studio Can't Connect**
1. Ensure Metro is running on port 8081
2. Check firewall settings
3. Verify Android emulator/device is connected
4. Try restarting Metro with cache reset

### **Build Errors**
```bash
# Clean Android build
cd android
./gradlew clean

# Reset React Native cache
npx react-native start --reset-cache
```

### **Environment Issues**
```bash
# Verify Android SDK
echo $ANDROID_HOME

# Check Java version
java -version

# Verify React Native CLI
npx react-native --version
```

---

## 🔄 **Development Workflow**

### **Daily Development**
1. Start Metro: `npm run metro`
2. Open Android Studio
3. Build and run the app
4. Make code changes
5. Hot reload automatically updates the app

### **Debugging**
1. Use Android Studio debugger for native code
2. Use Metro debugger for JavaScript code
3. View logs with `npm run logs:android`
4. Use React Native Flipper for advanced debugging

### **Testing**
1. Test on multiple Android devices/emulators
2. Test different Android API levels
3. Verify performance with Android profiler
4. Test offline functionality

---

## 🌐 **Maintaining Expo Web Compatibility**

### **Web Development (Unchanged)**
```bash
npm run web              # Expo web development
npm run build:web        # Build for web
npm run deploy           # Deploy to Vercel
```

### **Dual Development**
- Metro configuration supports both Expo and React Native CLI
- Environment variables work for both platforms
- Supabase and Square integrations work identically
- No conflicts between development modes

---

## 📱 **Production Builds**

### **Web Production**
- **Command**: `npm run deploy`
- **Platform**: Vercel
- **URL**: https://oceansoulapp.vercel.app
- **Environment**: Production

### **Android Production**
- **Command**: EAS Build (when ready)
- **Platform**: Google Play Store
- **Environment**: Production
- **Note**: Use EAS for production Android builds

---

## 🎉 **Success Indicators**

### **Metro Running Successfully**
- Metro bundler starts on port 8081
- No error messages in terminal
- "Metro is ready" message appears

### **Android Studio Connected**
- App builds without errors
- App installs on device/emulator
- Hot reload works when making changes
- JavaScript console shows in Metro terminal

### **Full Integration Working**
- Supabase database connections work
- Square payment integration functions
- Navigation between screens works
- All app features function correctly

---

## 📞 **Support**

If you encounter issues:
1. Check the troubleshooting section above
2. Run `npm run setup:android` to verify environment
3. Check Metro and Android Studio logs for error messages
4. Ensure all prerequisites are properly installed

**Happy Android Development!** 🚀
