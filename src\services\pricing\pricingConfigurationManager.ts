/**
 * Ocean Soul Sparkles Mobile App - Pricing Configuration Manager
 * Manages pricing tiers, configuration, and dynamic pricing updates
 */

import { supabase } from '@/services/database/supabase';
import { PricingTier } from './distancePricingService';

export interface PricingConfiguration {
  id: string;
  name: string;
  description: string;
  is_active: boolean;
  pricing_tiers: PricingTier[];
  base_location: {
    address: string;
    latitude: number;
    longitude: number;
  };
  created_at: string;
  updated_at: string;
}

export interface PricingConfigurationUpdate {
  name?: string;
  description?: string;
  is_active?: boolean;
  pricing_tiers?: Partial<PricingTier>[];
  base_location?: {
    address?: string;
    latitude?: number;
    longitude?: number;
  };
}

export interface PricingValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

export class PricingConfigurationManager {
  private static instance: PricingConfigurationManager;
  private cachedConfiguration: PricingConfiguration | null = null;
  private cacheExpiry: number = 0;
  private readonly CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

  private constructor() {}

  public static getInstance(): PricingConfigurationManager {
    if (!PricingConfigurationManager.instance) {
      PricingConfigurationManager.instance = new PricingConfigurationManager();
    }
    return PricingConfigurationManager.instance;
  }

  /**
   * Get current active pricing configuration
   */
  async getActivePricingConfiguration(): Promise<PricingConfiguration | null> {
    try {
      // Check cache first
      if (this.cachedConfiguration && Date.now() < this.cacheExpiry) {
        return this.cachedConfiguration;
      }

      console.log('📊 Fetching active pricing configuration...');

      const { data, error } = await supabase
        .from('pricing_configurations')
        .select('*')
        .eq('is_active', true)
        .order('updated_at', { ascending: false })
        .limit(1)
        .single();

      if (error) {
        console.error('❌ Error fetching pricing configuration:', error);
        return this.getDefaultConfiguration();
      }

      if (!data) {
        console.warn('⚠️ No active pricing configuration found, using default');
        return this.getDefaultConfiguration();
      }

      // Cache the result
      this.cachedConfiguration = data;
      this.cacheExpiry = Date.now() + this.CACHE_DURATION;

      return data;

    } catch (error) {
      console.error('❌ Failed to get pricing configuration:', error);
      return this.getDefaultConfiguration();
    }
  }

  /**
   * Get all pricing configurations
   */
  async getAllPricingConfigurations(): Promise<PricingConfiguration[]> {
    try {
      console.log('📊 Fetching all pricing configurations...');

      const { data, error } = await supabase
        .from('pricing_configurations')
        .select('*')
        .order('updated_at', { ascending: false });

      if (error) {
        console.error('❌ Error fetching pricing configurations:', error);
        return [];
      }

      return data || [];

    } catch (error) {
      console.error('❌ Failed to get pricing configurations:', error);
      return [];
    }
  }

  /**
   * Create new pricing configuration
   */
  async createPricingConfiguration(
    config: Omit<PricingConfiguration, 'id' | 'created_at' | 'updated_at'>
  ): Promise<{ success: boolean; configuration?: PricingConfiguration; error?: string }> {
    try {
      console.log('➕ Creating new pricing configuration...');

      // Validate configuration
      const validation = this.validatePricingConfiguration(config);
      if (!validation.isValid) {
        return {
          success: false,
          error: `Validation failed: ${validation.errors.join(', ')}`,
        };
      }

      // If setting as active, deactivate others first
      if (config.is_active) {
        await this.deactivateAllConfigurations();
      }

      const { data, error } = await supabase
        .from('pricing_configurations')
        .insert([config])
        .select()
        .single();

      if (error) {
        console.error('❌ Error creating pricing configuration:', error);
        return {
          success: false,
          error: error.message,
        };
      }

      // Clear cache
      this.clearCache();

      return {
        success: true,
        configuration: data,
      };

    } catch (error) {
      console.error('❌ Failed to create pricing configuration:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Update pricing configuration
   */
  async updatePricingConfiguration(
    id: string,
    updates: PricingConfigurationUpdate
  ): Promise<{ success: boolean; configuration?: PricingConfiguration; error?: string }> {
    try {
      console.log('✏️ Updating pricing configuration:', id);

      // If setting as active, deactivate others first
      if (updates.is_active) {
        await this.deactivateAllConfigurations();
      }

      const { data, error } = await supabase
        .from('pricing_configurations')
        .update({
          ...updates,
          updated_at: new Date().toISOString(),
        })
        .eq('id', id)
        .select()
        .single();

      if (error) {
        console.error('❌ Error updating pricing configuration:', error);
        return {
          success: false,
          error: error.message,
        };
      }

      // Clear cache
      this.clearCache();

      return {
        success: true,
        configuration: data,
      };

    } catch (error) {
      console.error('❌ Failed to update pricing configuration:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Delete pricing configuration
   */
  async deletePricingConfiguration(id: string): Promise<{ success: boolean; error?: string }> {
    try {
      console.log('🗑️ Deleting pricing configuration:', id);

      const { error } = await supabase
        .from('pricing_configurations')
        .delete()
        .eq('id', id);

      if (error) {
        console.error('❌ Error deleting pricing configuration:', error);
        return {
          success: false,
          error: error.message,
        };
      }

      // Clear cache
      this.clearCache();

      return { success: true };

    } catch (error) {
      console.error('❌ Failed to delete pricing configuration:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Activate pricing configuration
   */
  async activatePricingConfiguration(id: string): Promise<{ success: boolean; error?: string }> {
    try {
      console.log('🔄 Activating pricing configuration:', id);

      // Deactivate all others first
      await this.deactivateAllConfigurations();

      // Activate the specified configuration
      const { error } = await supabase
        .from('pricing_configurations')
        .update({
          is_active: true,
          updated_at: new Date().toISOString(),
        })
        .eq('id', id);

      if (error) {
        console.error('❌ Error activating pricing configuration:', error);
        return {
          success: false,
          error: error.message,
        };
      }

      // Clear cache
      this.clearCache();

      return { success: true };

    } catch (error) {
      console.error('❌ Failed to activate pricing configuration:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Validate pricing configuration
   */
  validatePricingConfiguration(config: Partial<PricingConfiguration>): PricingValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Check required fields
    if (!config.name?.trim()) {
      errors.push('Configuration name is required');
    }

    if (!config.pricing_tiers || config.pricing_tiers.length === 0) {
      errors.push('At least one pricing tier is required');
    }

    if (!config.base_location) {
      errors.push('Base location is required');
    }

    // Validate pricing tiers
    if (config.pricing_tiers) {
      const tierNames = new Set<string>();
      
      config.pricing_tiers.forEach((tier, index) => {
        // Check for duplicate names
        if (tierNames.has(tier.name)) {
          errors.push(`Duplicate tier name: ${tier.name}`);
        }
        tierNames.add(tier.name);

        // Validate tier structure
        if (!tier.name?.trim()) {
          errors.push(`Tier ${index + 1}: Name is required`);
        }

        if (tier.max_distance_km <= 0) {
          errors.push(`Tier ${index + 1}: Max distance must be positive`);
        }

        if (tier.multiplier < 0) {
          errors.push(`Tier ${index + 1}: Multiplier cannot be negative`);
        }

        if (tier.travel_fee < 0) {
          errors.push(`Tier ${index + 1}: Travel fee cannot be negative`);
        }
      });

      // Check tier ordering
      const sortedTiers = [...config.pricing_tiers].sort((a, b) => a.max_distance_km - b.max_distance_km);
      const isCorrectOrder = config.pricing_tiers.every((tier, index) => 
        tier.max_distance_km === sortedTiers[index].max_distance_km
      );

      if (!isCorrectOrder) {
        warnings.push('Pricing tiers should be ordered by distance for optimal performance');
      }
    }

    // Validate base location
    if (config.base_location) {
      if (!config.base_location.address?.trim()) {
        errors.push('Base location address is required');
      }

      if (!config.base_location.latitude || !config.base_location.longitude) {
        errors.push('Base location coordinates are required');
      }

      // Check coordinate ranges
      if (config.base_location.latitude < -90 || config.base_location.latitude > 90) {
        errors.push('Base location latitude must be between -90 and 90');
      }

      if (config.base_location.longitude < -180 || config.base_location.longitude > 180) {
        errors.push('Base location longitude must be between -180 and 180');
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
    };
  }

  /**
   * Get default pricing configuration
   */
  private getDefaultConfiguration(): PricingConfiguration {
    return {
      id: 'default',
      name: 'Default Pricing',
      description: 'Default distance-based pricing configuration',
      is_active: true,
      pricing_tiers: [
        {
          name: 'Local',
          description: 'Local area service',
          max_distance_km: 10,
          multiplier: 1.0,
          travel_fee: 0,
        },
        {
          name: 'Metro',
          description: 'Metropolitan area service',
          max_distance_km: 25,
          multiplier: 1.2,
          travel_fee: 15,
        },
        {
          name: 'Regional',
          description: 'Regional area service',
          max_distance_km: 50,
          multiplier: 1.5,
          travel_fee: 30,
        },
        {
          name: 'Extended',
          description: 'Extended area service',
          max_distance_km: 100,
          multiplier: 2.0,
          travel_fee: 50,
        },
      ],
      base_location: {
        address: 'Sydney, NSW, Australia',
        latitude: -33.8688,
        longitude: 151.2093,
      },
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };
  }

  /**
   * Deactivate all pricing configurations
   */
  private async deactivateAllConfigurations(): Promise<void> {
    try {
      await supabase
        .from('pricing_configurations')
        .update({ is_active: false })
        .neq('id', '');
    } catch (error) {
      console.error('❌ Error deactivating configurations:', error);
    }
  }

  /**
   * Clear configuration cache
   */
  private clearCache(): void {
    this.cachedConfiguration = null;
    this.cacheExpiry = 0;
  }

  /**
   * Get pricing tier by distance
   */
  getPricingTierByDistance(distance: number, tiers: PricingTier[]): PricingTier {
    // Sort tiers by max distance
    const sortedTiers = [...tiers].sort((a, b) => a.max_distance_km - b.max_distance_km);
    
    // Find the first tier that can handle this distance
    const tier = sortedTiers.find(t => distance <= t.max_distance_km);
    
    // If no tier found, use the highest tier
    return tier || sortedTiers[sortedTiers.length - 1];
  }

  /**
   * Calculate distance adjustment based on tier
   */
  calculateDistanceAdjustment(basePrice: number, tier: PricingTier): number {
    return basePrice * (tier.multiplier - 1);
  }

  /**
   * Get configuration summary for display
   */
  getConfigurationSummary(config: PricingConfiguration): {
    tierCount: number;
    maxDistance: number;
    baseLocation: string;
    lastUpdated: string;
  } {
    const maxDistance = Math.max(...config.pricing_tiers.map(t => t.max_distance_km));
    
    return {
      tierCount: config.pricing_tiers.length,
      maxDistance,
      baseLocation: config.base_location.address,
      lastUpdated: new Date(config.updated_at).toLocaleDateString(),
    };
  }
}

// Export singleton instance
export const pricingConfigurationManager = PricingConfigurationManager.getInstance();
