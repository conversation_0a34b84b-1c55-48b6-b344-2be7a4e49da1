/**
 * Ocean Soul Sparkles Mobile App - Supabase Database Service
 * Shared database connection with admin portal
 */

import { createClient, SupabaseClient } from '@supabase/supabase-js';
import Constants from 'expo-constants';

// Environment variables
const SUPABASE_URL = Constants.expoConfig?.extra?.EXPO_PUBLIC_SUPABASE_URL || process.env.EXPO_PUBLIC_SUPABASE_URL;
const SUPABASE_ANON_KEY = Constants.expoConfig?.extra?.EXPO_PUBLIC_SUPABASE_ANON_KEY || process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY;

if (!SUPABASE_URL || !SUPABASE_ANON_KEY) {
  throw new Error('Missing Supabase configuration. Please check your environment variables.');
}

// Create Supabase client
export const supabase: SupabaseClient = createClient(SUPABASE_URL, SUPABASE_ANON_KEY, {
  auth: {
    // Enable automatic token refresh
    autoRefreshToken: true,
    // Persist session in secure storage
    persistSession: true,
    // Detect session from URL (useful for deep links)
    detectSessionInUrl: false,
  },
  realtime: {
    // Enable real-time subscriptions
    params: {
      eventsPerSecond: 10,
    },
  },
});

// Database service class
export class DatabaseService {
  private client: SupabaseClient;

  constructor() {
    this.client = supabase;
  }

  // Authentication methods
  async signIn(email: string, password: string) {
    const { data, error } = await this.client.auth.signInWithPassword({
      email,
      password,
    });
    return { data, error };
  }

  async signOut() {
    const { error } = await this.client.auth.signOut();
    return { error };
  }

  async getCurrentUser() {
    const { data: { user }, error } = await this.client.auth.getUser();
    return { user, error };
  }

  async getCurrentSession() {
    const { data: { session }, error } = await this.client.auth.getSession();
    return { session, error };
  }

  // Generic CRUD operations
  async select<T>(
    table: string,
    columns: string = '*',
    filters?: Record<string, any>,
    options?: {
      limit?: number;
      offset?: number;
      orderBy?: string;
      orderDirection?: 'asc' | 'desc';
    }
  ) {
    let query = this.client.from(table).select(columns);

    // Apply filters
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          query = query.eq(key, value);
        }
      });
    }

    // Apply ordering
    if (options?.orderBy) {
      query = query.order(options.orderBy, { 
        ascending: options.orderDirection === 'asc' 
      });
    }

    // Apply pagination
    if (options?.limit) {
      query = query.limit(options.limit);
    }
    if (options?.offset) {
      query = query.range(options.offset, options.offset + (options.limit || 10) - 1);
    }

    const { data, error } = await query;
    return { data: data as T[], error };
  }

  async selectOne<T>(
    table: string,
    columns: string = '*',
    filters: Record<string, any>
  ) {
    let query = this.client.from(table).select(columns);

    // Apply filters
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        query = query.eq(key, value);
      }
    });

    const { data, error } = await query.single();
    return { data: data as T, error };
  }

  async insert<T>(table: string, data: Partial<T>) {
    const { data: result, error } = await this.client
      .from(table)
      .insert(data)
      .select()
      .single();
    
    return { data: result as T, error };
  }

  async update<T>(
    table: string,
    id: string,
    data: Partial<T>
  ) {
    const { data: result, error } = await this.client
      .from(table)
      .update(data)
      .eq('id', id)
      .select()
      .single();
    
    return { data: result as T, error };
  }

  async delete(table: string, id: string) {
    const { error } = await this.client
      .from(table)
      .delete()
      .eq('id', id);
    
    return { error };
  }

  // Real-time subscriptions
  subscribeToTable(
    table: string,
    callback: (payload: any) => void,
    filters?: Record<string, any>
  ) {
    let channel = this.client
      .channel(`public:${table}`)
      .on(
        'postgres_changes',
        { 
          event: '*', 
          schema: 'public', 
          table,
          ...(filters && { filter: Object.entries(filters).map(([key, value]) => `${key}=eq.${value}`).join(',') })
        },
        callback
      );

    return channel.subscribe();
  }

  // Utility methods
  async searchTable<T>(
    table: string,
    searchColumn: string,
    searchTerm: string,
    columns: string = '*',
    limit: number = 20
  ) {
    const { data, error } = await this.client
      .from(table)
      .select(columns)
      .ilike(searchColumn, `%${searchTerm}%`)
      .limit(limit);

    return { data: data as T[], error };
  }

  async count(table: string, filters?: Record<string, any>) {
    let query = this.client.from(table).select('*', { count: 'exact', head: true });

    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          query = query.eq(key, value);
        }
      });
    }

    const { count, error } = await query;
    return { count, error };
  }
}

// Export singleton instance
export const db = new DatabaseService();
