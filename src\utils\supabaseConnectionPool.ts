/**
 * Ocean Soul Sparkles Mobile App - Supabase Connection Pool Manager
 * Manages Supabase real-time connections efficiently to prevent connection exhaustion
 */

import { supabase } from '@/services/database/supabase';
import { RealtimeChannel } from '@supabase/supabase-js';

interface ChannelConfig {
  channelName: string;
  table: string;
  filter?: string;
  event?: 'INSERT' | 'UPDATE' | 'DELETE' | '*';
  callback: (payload: any) => void;
}

interface PooledChannel {
  channel: RealtimeChannel;
  subscribers: Map<string, (payload: any) => void>;
  lastActivity: number;
  config: ChannelConfig;
}

class SupabaseConnectionPool {
  private channels: Map<string, PooledChannel> = new Map();
  private readonly MAX_CHANNELS = 15; // Supabase limit is typically 20
  private readonly IDLE_TIMEOUT = 10 * 60 * 1000; // 10 minutes
  private readonly CLEANUP_INTERVAL = 5 * 60 * 1000; // 5 minutes
  private cleanupInterval: NodeJS.Timeout | null = null;
  private isInitialized = false;

  /**
   * Initialize the connection pool
   */
  initialize(): void {
    if (this.isInitialized) return;

    // Start cleanup interval
    this.cleanupInterval = setInterval(() => {
      this.cleanupIdleChannels();
    }, this.CLEANUP_INTERVAL);

    this.isInitialized = true;
    console.log('🔗 Supabase connection pool initialized');
  }

  /**
   * Subscribe to a table with connection pooling
   */
  subscribe(
    subscriberId: string,
    table: string,
    callback: (payload: any) => void,
    options: {
      event?: 'INSERT' | 'UPDATE' | 'DELETE' | '*';
      filter?: string;
    } = {}
  ): () => void {
    const channelKey = this.getChannelKey(table, options.filter, options.event);
    
    // Check if we already have a channel for this configuration
    let pooledChannel = this.channels.get(channelKey);
    
    if (pooledChannel) {
      // Reuse existing channel
      pooledChannel.subscribers.set(subscriberId, callback);
      pooledChannel.lastActivity = Date.now();
      console.log(`🔗 Reusing channel ${channelKey} for subscriber ${subscriberId}`);
    } else {
      // Create new channel if we haven't hit the limit
      if (this.channels.size >= this.MAX_CHANNELS) {
        this.evictOldestChannel();
      }
      
      pooledChannel = this.createChannel(channelKey, table, callback, options);
      this.channels.set(channelKey, pooledChannel);
      pooledChannel.subscribers.set(subscriberId, callback);
    }

    // Return unsubscribe function
    return () => this.unsubscribe(subscriberId, channelKey);
  }

  /**
   * Unsubscribe a specific subscriber
   */
  private unsubscribe(subscriberId: string, channelKey: string): void {
    const pooledChannel = this.channels.get(channelKey);
    if (!pooledChannel) return;

    // Remove subscriber
    pooledChannel.subscribers.delete(subscriberId);
    console.log(`🔗 Unsubscribed ${subscriberId} from channel ${channelKey}`);

    // If no more subscribers, mark for cleanup
    if (pooledChannel.subscribers.size === 0) {
      console.log(`🔗 Channel ${channelKey} has no more subscribers, will be cleaned up`);
    }
  }

  /**
   * Create a new pooled channel
   */
  private createChannel(
    channelKey: string,
    table: string,
    callback: (payload: any) => void,
    options: {
      event?: 'INSERT' | 'UPDATE' | 'DELETE' | '*';
      filter?: string;
    }
  ): PooledChannel {
    const channel = supabase.channel(channelKey);
    
    // Configure the channel
    const event = options.event || '*';
    const channelConfig: any = {
      event,
      schema: 'public',
      table,
    };
    
    if (options.filter) {
      channelConfig.filter = options.filter;
    }

    channel.on('postgres_changes', channelConfig, (payload) => {
      // Update activity timestamp
      const pooledChannel = this.channels.get(channelKey);
      if (pooledChannel) {
        pooledChannel.lastActivity = Date.now();
        
        // Notify all subscribers
        pooledChannel.subscribers.forEach((subscriberCallback) => {
          try {
            subscriberCallback(payload);
          } catch (error) {
            console.error(`❌ Error in subscriber callback for ${channelKey}:`, error);
          }
        });
      }
    });

    // Subscribe to the channel
    channel.subscribe((status) => {
      console.log(`🔗 Channel ${channelKey} status: ${status}`);
    });

    return {
      channel,
      subscribers: new Map(),
      lastActivity: Date.now(),
      config: {
        channelName: channelKey,
        table,
        filter: options.filter,
        event,
        callback,
      },
    };
  }

  /**
   * Generate a unique key for channel configuration
   */
  private getChannelKey(table: string, filter?: string, event?: string): string {
    const parts = [table, event || '*'];
    if (filter) {
      parts.push(filter);
    }
    return parts.join('_');
  }

  /**
   * Evict the oldest channel to make room for new ones
   */
  private evictOldestChannel(): void {
    let oldestChannel: string | null = null;
    let oldestTime = Date.now();

    for (const [channelKey, pooledChannel] of this.channels) {
      if (pooledChannel.lastActivity < oldestTime) {
        oldestTime = pooledChannel.lastActivity;
        oldestChannel = channelKey;
      }
    }

    if (oldestChannel) {
      this.removeChannel(oldestChannel);
      console.log(`🔗 Evicted oldest channel: ${oldestChannel}`);
    }
  }

  /**
   * Clean up idle channels
   */
  private cleanupIdleChannels(): void {
    const now = Date.now();
    const channelsToRemove: string[] = [];

    for (const [channelKey, pooledChannel] of this.channels) {
      const isIdle = now - pooledChannel.lastActivity > this.IDLE_TIMEOUT;
      const hasNoSubscribers = pooledChannel.subscribers.size === 0;

      if (isIdle || hasNoSubscribers) {
        channelsToRemove.push(channelKey);
      }
    }

    channelsToRemove.forEach(channelKey => {
      this.removeChannel(channelKey);
      console.log(`🧹 Cleaned up idle channel: ${channelKey}`);
    });

    if (channelsToRemove.length > 0) {
      console.log(`🧹 Cleaned up ${channelsToRemove.length} idle channels`);
    }
  }

  /**
   * Remove a channel from the pool
   */
  private removeChannel(channelKey: string): void {
    const pooledChannel = this.channels.get(channelKey);
    if (pooledChannel) {
      try {
        pooledChannel.channel.unsubscribe();
      } catch (error) {
        console.error(`❌ Error unsubscribing channel ${channelKey}:`, error);
      }
      this.channels.delete(channelKey);
    }
  }

  /**
   * Get connection pool statistics
   */
  getStats(): {
    totalChannels: number;
    totalSubscribers: number;
    channelDetails: Array<{
      channelKey: string;
      table: string;
      subscriberCount: number;
      lastActivity: Date;
      idleTime: number;
    }>;
  } {
    const now = Date.now();
    const channelDetails = Array.from(this.channels.entries()).map(([channelKey, pooledChannel]) => ({
      channelKey,
      table: pooledChannel.config.table,
      subscriberCount: pooledChannel.subscribers.size,
      lastActivity: new Date(pooledChannel.lastActivity),
      idleTime: now - pooledChannel.lastActivity,
    }));

    const totalSubscribers = channelDetails.reduce((sum, channel) => sum + channel.subscriberCount, 0);

    return {
      totalChannels: this.channels.size,
      totalSubscribers,
      channelDetails,
    };
  }

  /**
   * Force cleanup of all channels
   */
  cleanup(): void {
    console.log('🧹 Cleaning up all connection pool channels...');
    
    for (const [channelKey] of this.channels) {
      this.removeChannel(channelKey);
    }
    
    this.channels.clear();
    
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = null;
    }
    
    this.isInitialized = false;
    console.log('✅ Connection pool cleanup completed');
  }

  /**
   * Get health status of the connection pool
   */
  getHealthStatus(): {
    status: 'healthy' | 'warning' | 'critical';
    message: string;
    metrics: {
      channelUtilization: number;
      averageSubscribersPerChannel: number;
      idleChannels: number;
    };
  } {
    const stats = this.getStats();
    const channelUtilization = (stats.totalChannels / this.MAX_CHANNELS) * 100;
    const averageSubscribersPerChannel = stats.totalChannels > 0 ? stats.totalSubscribers / stats.totalChannels : 0;
    const idleChannels = stats.channelDetails.filter(c => c.idleTime > this.IDLE_TIMEOUT).length;

    let status: 'healthy' | 'warning' | 'critical' = 'healthy';
    let message = 'Connection pool is operating normally';

    if (channelUtilization > 90) {
      status = 'critical';
      message = 'Connection pool is near capacity';
    } else if (channelUtilization > 70 || idleChannels > 5) {
      status = 'warning';
      message = 'Connection pool needs attention';
    }

    return {
      status,
      message,
      metrics: {
        channelUtilization,
        averageSubscribersPerChannel,
        idleChannels,
      },
    };
  }
}

// Export singleton instance
export const supabaseConnectionPool = new SupabaseConnectionPool();
