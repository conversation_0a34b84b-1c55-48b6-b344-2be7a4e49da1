# Ocean Soul Sparkles Mobile App - Distance-Based Pricing Calculator

## 📋 Overview

The Distance-Based Pricing Calculator is a comprehensive system that automatically calculates service pricing based on the distance between the Ocean Soul Sparkles studio and the customer's location. This system ensures accurate, fair, and transparent pricing while accounting for travel costs and service delivery logistics.

## 🎯 Key Features

### Automated Distance Calculation
- **GPS Location Capture**: Real-time device location detection
- **Address Geocoding**: Convert addresses to coordinates for accurate distance calculation
- **Multiple Calculation Methods**: Coordinates, postal codes, and city-based estimation
- **Haversine Formula**: Precise distance calculation using Earth's curvature

### Dynamic Pricing Tiers
- **Local Zone (0-10km)**: Base pricing with no additional charges
- **Metro Zone (10-25km)**: 10% surcharge + $25 travel fee
- **Regional Zone (25-50km)**: 20% surcharge + $50 travel fee
- **Extended Zone (50-100km)**: 30% surcharge + $100 travel fee

### Intelligent Location Services
- **Address Validation**: Australian address format validation
- **Postal Code Estimation**: Distance estimation based on Australian postal codes
- **City/State Fallback**: Rough distance estimation when precise coordinates unavailable
- **Location Permission Management**: Proper handling of device location permissions

## 🏗️ System Architecture

### Core Components

#### 1. Location Capture Service
**File:** `src/services/location/locationCaptureService.ts`

**Purpose:** Handles location capture, geocoding, and address validation.

**Key Features:**
- Device GPS location capture with permission management
- Address geocoding using Expo Location services
- Australian address validation and formatting
- Multiple location input methods (GPS, manual entry, postal code)

**Usage:**
```typescript
import { locationCaptureService } from '@/services/location/locationCaptureService';

// Get current device location
const locationResult = await locationCaptureService.getCurrentLocation();

// Geocode an address
const geocodeResult = await locationCaptureService.geocodeAddress('Sydney, NSW, Australia');

// Validate address format
const validation = await locationCaptureService.validateAddress({
  city: 'Sydney',
  state: 'NSW',
  postalCode: '2000',
});
```

#### 2. Distance Pricing Calculator UI
**File:** `src/components/pricing/DistancePricingCalculator.tsx`

**Purpose:** Interactive React component for distance-based pricing calculation.

**Key Features:**
- Real-time pricing calculation display
- Location input with GPS and manual options
- Detailed pricing breakdown visualization
- Pricing tier information and descriptions

**Usage:**
```typescript
import DistancePricingCalculator from '@/components/pricing/DistancePricingCalculator';

<DistancePricingCalculator
  service={selectedService}
  onPricingCalculated={(result) => handlePricingUpdate(result)}
  onLocationChanged={(location) => handleLocationUpdate(location)}
  showLocationInput={true}
  showPricingBreakdown={true}
/>
```

#### 3. Pricing Configuration Manager
**File:** `src/services/pricing/pricingConfigurationManager.ts`

**Purpose:** Manages pricing tiers, configurations, and dynamic pricing updates.

**Key Features:**
- Configurable pricing tiers and rules
- Multiple pricing configuration support
- Configuration validation and management
- Cache management for optimal performance

**Usage:**
```typescript
import { pricingConfigurationManager } from '@/services/pricing/pricingConfigurationManager';

// Get active pricing configuration
const config = await pricingConfigurationManager.getActivePricingConfiguration();

// Create new pricing configuration
const result = await pricingConfigurationManager.createPricingConfiguration({
  name: 'Holiday Pricing',
  description: 'Special holiday pricing tiers',
  pricing_tiers: customTiers,
  base_location: studioLocation,
  is_active: true,
});
```

#### 4. Distance Pricing Validator
**File:** `src/services/validation/distancePricingValidator.ts`

**Purpose:** Validates pricing calculations, configurations, and system accuracy.

**Key Features:**
- Pricing accuracy validation with tolerance checking
- Configuration validation and error detection
- Performance monitoring and optimization
- Comprehensive system health assessment

**Usage:**
```typescript
import { distancePricingValidator } from '@/services/validation/distancePricingValidator';

// Validate pricing system
const status = await distancePricingValidator.getDistancePricingStatus();

// Run comprehensive validation
const results = await distancePricingValidator.runComprehensiveDistancePricingValidation();
```

#### 5. Quote Service Integration
**File:** `src/services/database/quoteService.ts` (Enhanced)

**Purpose:** Integrates distance pricing with quote generation and management.

**Key Features:**
- Automatic distance pricing calculation in quotes
- Detailed pricing breakdown in quote descriptions
- Location-based quote updates
- Pricing history and audit trail

**Usage:**
```typescript
import { quoteService } from '@/services/database/quoteService';

// Create quote with distance pricing
const quoteResult = await quoteService.createQuoteWithDistancePricing(
  quoteData,
  customerLocation,
  service
);

// Update quote pricing for new location
const updateResult = await quoteService.updateQuotePricingForLocation(
  quoteId,
  newLocation,
  service
);
```

## 💰 Pricing Structure

### Pricing Tiers

| Zone | Distance | Base Multiplier | Travel Fee | Description |
|------|----------|----------------|------------|-------------|
| **Local** | 0-10km | 1.0x | $0 | Within 10km - No additional charges |
| **Metro** | 10-25km | 1.1x | $25 | 10-25km - 10% surcharge + $25 travel fee |
| **Regional** | 25-50km | 1.2x | $50 | 25-50km - 20% surcharge + $50 travel fee |
| **Extended** | 50-100km | 1.3x | $100 | 50-100km - 30% surcharge + $100 travel fee |

### Pricing Calculation Formula

```
Total Price = (Base Price × Zone Multiplier) + Travel Fee

Example for Metro Zone (15km):
- Base Service: $100
- Zone Multiplier: 1.1 (10% increase)
- Travel Fee: $25
- Total: ($100 × 1.1) + $25 = $135
```

### Pricing Breakdown Display

```
PRICING BREAKDOWN:
• Service Base Price: $100.00
• Distance Adjustment (Metro Zone): +$10.00
• Travel Fee: +$25.00
─────────────────────────────────
TOTAL AMOUNT: $135.00

Distance: 15km (10-25km - 10% surcharge + $25 travel fee)
```

## 📍 Location Handling

### Location Input Methods

1. **GPS Location Capture**
   - Real-time device location using Expo Location
   - Automatic reverse geocoding to get formatted address
   - Permission management and error handling

2. **Manual Address Entry**
   - Text input with address validation
   - Geocoding to convert address to coordinates
   - Australian address format validation

3. **Customer Database Integration**
   - Automatic location extraction from customer records
   - Address validation and formatting
   - Fallback to postal code estimation

### Distance Calculation Methods

1. **Coordinate-Based (Most Accurate)**
   - Uses Haversine formula for precise distance calculation
   - Accounts for Earth's curvature
   - Accurate to within meters

2. **Postal Code Estimation**
   - Australian postal code distance mapping
   - Reasonable accuracy for pricing purposes
   - Fallback when coordinates unavailable

3. **City/State Estimation**
   - Rough distance estimation based on major cities
   - Last resort when other methods fail
   - Conservative estimates to ensure fair pricing

## 🔧 Configuration Management

### Pricing Configuration Structure

```typescript
interface PricingConfiguration {
  id: string;
  name: string;
  description: string;
  is_active: boolean;
  pricing_tiers: PricingTier[];
  base_location: {
    address: string;
    latitude: number;
    longitude: number;
  };
  created_at: string;
  updated_at: string;
}
```

### Configuration Validation

- **Required Fields**: Name, pricing tiers, base location
- **Tier Validation**: Positive distances, valid multipliers, non-negative fees
- **Location Validation**: Valid coordinates, formatted address
- **Ordering Validation**: Tiers ordered by distance for optimal performance

## 📊 Testing & Validation

### Comprehensive Testing Suite
**File:** `src/utils/distancePricingTesting.ts`

**Test Categories:**
1. **Configuration Tests**: Validation of pricing configurations
2. **Calculation Tests**: Accuracy of pricing calculations
3. **Location Tests**: Geocoding and location services functionality
4. **Integration Tests**: Quote service integration
5. **Performance Tests**: System performance and optimization

**Usage:**
```typescript
import { distancePricingTesting } from '@/utils/distancePricingTesting';

// Run complete test suite
const testResults = await distancePricingTesting.runCompleteDistancePricingTest();

// Generate test report
const report = distancePricingTesting.generateTestReport(testResults);
```

### Validation Metrics

- **Pricing Accuracy**: ±5% tolerance for pricing calculations
- **Performance Thresholds**: 
  - Calculation time: <2 seconds
  - Geocoding time: <3 seconds
  - Configuration loading: <1 second
- **Location Accuracy**: Coordinate-based calculations preferred

## 🚀 Integration with Booking System

### Quote Generation Integration

The distance pricing calculator seamlessly integrates with the quote generation system:

1. **Automatic Pricing**: Quotes automatically include distance-based pricing
2. **Detailed Breakdown**: Quote descriptions include full pricing breakdown
3. **Location Tracking**: Customer location stored with quote for future reference
4. **Price Updates**: Quotes can be updated if customer location changes

### Booking Workflow Integration

```typescript
// Enhanced booking review with distance pricing
const pricingResult = await distancePricingService.calculatePricing(service, customerLocation);

// Quote creation with calculated pricing
const quote = await quoteService.createQuoteWithDistancePricing(
  quoteData,
  customerLocation,
  service
);
```

## 📱 User Experience

### Customer-Facing Features

1. **Transparent Pricing**: Clear breakdown of all charges
2. **Location Options**: Multiple ways to provide location
3. **Real-Time Updates**: Instant pricing updates as location changes
4. **Zone Information**: Clear explanation of pricing zones

### Staff-Facing Features

1. **Quick Calculation**: Instant pricing for any location
2. **Override Options**: Ability to adjust pricing when needed
3. **Pricing History**: Track pricing changes and updates
4. **Validation Alerts**: Notifications for pricing issues

## 🔒 Security & Privacy

### Location Privacy
- Location data used only for pricing calculation
- No permanent storage of precise coordinates
- Customer consent for location access
- Fallback options when location unavailable

### Data Protection
- Encrypted transmission of location data
- Secure storage of pricing configurations
- Audit trail for pricing changes
- Access control for configuration management

## 📈 Performance Optimization

### Caching Strategy
- Configuration caching with 5-minute expiry
- Geocoding result caching for common addresses
- Distance calculation optimization

### Error Handling
- Graceful fallback for location service failures
- Default pricing when calculation fails
- User-friendly error messages
- Comprehensive logging for debugging

## 🎯 Success Metrics

### Pricing Accuracy
- **Target**: 95% of calculations within ±5% tolerance
- **Current**: Monitored through validation system
- **Tracking**: Automated accuracy testing

### Performance Metrics
- **Calculation Speed**: <2 seconds average
- **Geocoding Speed**: <3 seconds average
- **System Availability**: 99.9% uptime target

### User Satisfaction
- **Pricing Transparency**: Clear breakdown display
- **Location Convenience**: Multiple input options
- **Accuracy Confidence**: Validated calculations

The Distance-Based Pricing Calculator ensures fair, accurate, and transparent pricing for Ocean Soul Sparkles services while providing an excellent user experience for both customers and staff.
