/**
 * Ocean Soul Sparkles Mobile App - Distance Pricing Testing Utilities
 * Comprehensive testing for distance-based pricing system
 */

import { distancePricingService, LocationData, PricingCalculationResult } from '@/services/pricing/distancePricingService';
import { locationCaptureService } from '@/services/location/locationCaptureService';
import { distancePricingValidator } from '@/services/validation/distancePricingValidator';
import { pricingConfigurationManager } from '@/services/pricing/pricingConfigurationManager';
import { quoteService } from '@/services/database/quoteService';
import { Service } from '@/types/database';

export interface DistancePricingTestResult {
  test: string;
  success: boolean;
  message: string;
  duration: number;
  details?: any;
  error?: string;
}

export interface DistancePricingTestSuite {
  configurationTests: DistancePricingTestResult[];
  calculationTests: DistancePricingTestResult[];
  locationTests: DistancePricingTestResult[];
  integrationTests: DistancePricingTestResult[];
  performanceTests: DistancePricingTestResult[];
  summary: {
    totalTests: number;
    passedTests: number;
    failedTests: number;
    successRate: number;
    totalDuration: number;
  };
}

export class DistancePricingTestingService {
  /**
   * Test pricing configuration management
   */
  static async testPricingConfiguration(): Promise<DistancePricingTestResult[]> {
    const results: DistancePricingTestResult[] = [];

    // Test 1: Configuration loading
    try {
      const startTime = Date.now();
      const config = await pricingConfigurationManager.getActivePricingConfiguration();
      
      results.push({
        test: 'Configuration Loading',
        success: config !== null,
        message: config ? 'Active pricing configuration loaded successfully' : 'No active configuration found',
        duration: Date.now() - startTime,
        details: config ? {
          name: config.name,
          tierCount: config.pricing_tiers.length,
          baseLocation: config.base_location.address,
        } : undefined,
      });
    } catch (error) {
      results.push({
        test: 'Configuration Loading',
        success: false,
        message: 'Failed to load pricing configuration',
        duration: 0,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }

    // Test 2: Configuration validation
    try {
      const startTime = Date.now();
      const validationResult = await distancePricingValidator.validatePricingConfiguration();
      
      results.push({
        test: 'Configuration Validation',
        success: validationResult.status === 'pass',
        message: validationResult.message,
        duration: Date.now() - startTime,
        details: validationResult.details,
      });
    } catch (error) {
      results.push({
        test: 'Configuration Validation',
        success: false,
        message: 'Configuration validation failed',
        duration: 0,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }

    return results;
  }

  /**
   * Test distance pricing calculations
   */
  static async testPricingCalculations(): Promise<DistancePricingTestResult[]> {
    const results: DistancePricingTestResult[] = [];

    const testService: Service = {
      id: 'test-service',
      name: 'Test Service',
      description: 'Test service for pricing calculations',
      base_price: 100,
      duration_minutes: 60,
      is_active: true,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };

    const testLocations: LocationData[] = [
      {
        address: 'Sydney CBD, NSW, Australia',
        city: 'Sydney',
        state: 'NSW',
        postal_code: '2000',
        latitude: -33.8688,
        longitude: 151.2093,
      },
      {
        address: 'Parramatta, NSW, Australia',
        city: 'Parramatta',
        state: 'NSW',
        postal_code: '2150',
        latitude: -33.8150,
        longitude: 151.0000,
      },
      {
        address: 'Newcastle, NSW, Australia',
        city: 'Newcastle',
        state: 'NSW',
        postal_code: '2300',
        latitude: -32.9267,
        longitude: 151.7789,
      },
    ];

    // Test pricing calculations for different locations
    for (const location of testLocations) {
      try {
        const startTime = Date.now();
        const result = await distancePricingService.calculatePricing(testService, location);
        
        results.push({
          test: `Pricing Calculation - ${location.city}`,
          success: result.success,
          message: result.success 
            ? `Calculated price: $${result.total_price.toFixed(2)} for ${result.distance_km}km (${result.pricing_tier.name} zone)`
            : result.error || 'Calculation failed',
          duration: Date.now() - startTime,
          details: result.success ? {
            distance: result.distance_km,
            tier: result.pricing_tier.name,
            basePrice: result.base_price,
            totalPrice: result.total_price,
            breakdown: result.breakdown,
          } : undefined,
          error: result.success ? undefined : result.error,
        });
      } catch (error) {
        results.push({
          test: `Pricing Calculation - ${location.city}`,
          success: false,
          message: 'Pricing calculation failed',
          duration: 0,
          error: error instanceof Error ? error.message : 'Unknown error',
        });
      }
    }

    return results;
  }

  /**
   * Test location services functionality
   */
  static async testLocationServices(): Promise<DistancePricingTestResult[]> {
    const results: DistancePricingTestResult[] = [];

    // Test 1: Location services availability
    try {
      const startTime = Date.now();
      const servicesEnabled = await locationCaptureService.isLocationServicesEnabled();
      
      results.push({
        test: 'Location Services Availability',
        success: servicesEnabled,
        message: servicesEnabled ? 'Location services are available' : 'Location services not available',
        duration: Date.now() - startTime,
        details: { servicesEnabled },
      });
    } catch (error) {
      results.push({
        test: 'Location Services Availability',
        success: false,
        message: 'Failed to check location services',
        duration: 0,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }

    // Test 2: Geocoding functionality
    try {
      const startTime = Date.now();
      const testAddress = 'Sydney, NSW, Australia';
      const geocodeResult = await locationCaptureService.geocodeAddress(testAddress);
      
      results.push({
        test: 'Geocoding Functionality',
        success: geocodeResult.success,
        message: geocodeResult.success 
          ? `Successfully geocoded: ${testAddress}`
          : geocodeResult.error || 'Geocoding failed',
        duration: Date.now() - startTime,
        details: geocodeResult.success ? {
          address: geocodeResult.location?.address,
          coordinates: geocodeResult.location?.latitude && geocodeResult.location?.longitude 
            ? `${geocodeResult.location.latitude}, ${geocodeResult.location.longitude}`
            : 'Not available',
        } : undefined,
        error: geocodeResult.success ? undefined : geocodeResult.error,
      });
    } catch (error) {
      results.push({
        test: 'Geocoding Functionality',
        success: false,
        message: 'Geocoding test failed',
        duration: 0,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }

    // Test 3: Address validation
    try {
      const startTime = Date.now();
      const validationResult = await locationCaptureService.validateAddress({
        street: '123 Test Street',
        city: 'Sydney',
        state: 'NSW',
        postalCode: '2000',
      });
      
      results.push({
        test: 'Address Validation',
        success: validationResult.isValid,
        message: validationResult.isValid 
          ? 'Address validation working correctly'
          : 'Address validation failed',
        duration: Date.now() - startTime,
        details: {
          isValid: validationResult.isValid,
          confidence: validationResult.confidence,
          formattedAddress: validationResult.formattedAddress,
          suggestions: validationResult.suggestions,
        },
      });
    } catch (error) {
      results.push({
        test: 'Address Validation',
        success: false,
        message: 'Address validation test failed',
        duration: 0,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }

    return results;
  }

  /**
   * Test quote service integration
   */
  static async testQuoteIntegration(): Promise<DistancePricingTestResult[]> {
    const results: DistancePricingTestResult[] = [];

    const testService = {
      id: 'test-service-integration',
      name: 'Integration Test Service',
      base_price: 150,
    };

    const testLocation: LocationData = {
      address: 'Sydney CBD, NSW, Australia',
      city: 'Sydney',
      state: 'NSW',
      postal_code: '2000',
      latitude: -33.8688,
      longitude: 151.2093,
    };

    // Test 1: Quote creation with distance pricing
    try {
      const startTime = Date.now();
      
      // Note: This is a test simulation - in real implementation, 
      // we would need proper customer and staff IDs
      const mockQuoteData = {
        customer_id: 'test-customer',
        staff_id: 'test-staff',
        title: 'Test Quote with Distance Pricing',
        status: 'draft' as const,
      };

      // Test the pricing calculation part
      const pricingResult = await distancePricingService.calculatePricing(
        {
          id: testService.id,
          name: testService.name,
          base_price: testService.base_price,
          description: '',
          duration_minutes: 60,
          is_active: true,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        },
        testLocation
      );

      results.push({
        test: 'Quote Distance Pricing Integration',
        success: pricingResult.success,
        message: pricingResult.success 
          ? `Distance pricing integration working: $${pricingResult.total_price.toFixed(2)}`
          : pricingResult.error || 'Integration failed',
        duration: Date.now() - startTime,
        details: pricingResult.success ? {
          calculatedPrice: pricingResult.total_price,
          distance: pricingResult.distance_km,
          tier: pricingResult.pricing_tier.name,
          breakdown: pricingResult.breakdown,
        } : undefined,
        error: pricingResult.success ? undefined : pricingResult.error,
      });
    } catch (error) {
      results.push({
        test: 'Quote Distance Pricing Integration',
        success: false,
        message: 'Quote integration test failed',
        duration: 0,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }

    return results;
  }

  /**
   * Test system performance
   */
  static async testPerformance(): Promise<DistancePricingTestResult[]> {
    const results: DistancePricingTestResult[] = [];

    // Test 1: Pricing calculation performance
    try {
      const iterations = 10;
      const startTime = Date.now();
      
      const testService: Service = {
        id: 'perf-test',
        name: 'Performance Test',
        base_price: 100,
        description: '',
        duration_minutes: 60,
        is_active: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };

      const testLocation: LocationData = {
        address: 'Sydney, NSW, Australia',
        city: 'Sydney',
        state: 'NSW',
        postal_code: '2000',
      };

      for (let i = 0; i < iterations; i++) {
        await distancePricingService.calculatePricing(testService, testLocation);
      }

      const totalTime = Date.now() - startTime;
      const averageTime = totalTime / iterations;

      results.push({
        test: 'Pricing Calculation Performance',
        success: averageTime < 1000, // Should be under 1 second on average
        message: `Average calculation time: ${averageTime.toFixed(0)}ms (${iterations} iterations)`,
        duration: totalTime,
        details: {
          iterations,
          totalTime,
          averageTime,
          threshold: 1000,
        },
      });
    } catch (error) {
      results.push({
        test: 'Pricing Calculation Performance',
        success: false,
        message: 'Performance test failed',
        duration: 0,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }

    return results;
  }

  /**
   * Run complete distance pricing test suite
   */
  static async runCompleteDistancePricingTest(): Promise<DistancePricingTestSuite> {
    console.log('🧪 Running complete distance pricing test suite...');

    const startTime = Date.now();

    const configurationTests = await this.testPricingConfiguration();
    const calculationTests = await this.testPricingCalculations();
    const locationTests = await this.testLocationServices();
    const integrationTests = await this.testQuoteIntegration();
    const performanceTests = await this.testPerformance();

    const allTests = [
      ...configurationTests,
      ...calculationTests,
      ...locationTests,
      ...integrationTests,
      ...performanceTests,
    ];

    const passedTests = allTests.filter(t => t.success).length;
    const failedTests = allTests.length - passedTests;
    const successRate = allTests.length > 0 ? (passedTests / allTests.length) * 100 : 0;
    const totalDuration = Date.now() - startTime;

    const summary = {
      totalTests: allTests.length,
      passedTests,
      failedTests,
      successRate: Math.round(successRate * 100) / 100,
      totalDuration,
    };

    console.log(`✅ Distance pricing test suite completed: ${passedTests}/${allTests.length} tests passed (${summary.successRate}%)`);

    return {
      configurationTests,
      calculationTests,
      locationTests,
      integrationTests,
      performanceTests,
      summary,
    };
  }

  /**
   * Generate test report
   */
  static generateTestReport(testSuite: DistancePricingTestSuite): string {
    const lines = ['# Distance-Based Pricing Calculator Test Report'];
    lines.push('');
    lines.push(`**Generated:** ${new Date().toLocaleString()}`);
    lines.push(`**Total Tests:** ${testSuite.summary.totalTests}`);
    lines.push(`**Passed:** ${testSuite.summary.passedTests}`);
    lines.push(`**Failed:** ${testSuite.summary.failedTests}`);
    lines.push(`**Success Rate:** ${testSuite.summary.successRate}%`);
    lines.push(`**Duration:** ${testSuite.summary.totalDuration}ms`);
    lines.push('');

    const testCategories = [
      { name: 'Configuration Tests', tests: testSuite.configurationTests },
      { name: 'Calculation Tests', tests: testSuite.calculationTests },
      { name: 'Location Tests', tests: testSuite.locationTests },
      { name: 'Integration Tests', tests: testSuite.integrationTests },
      { name: 'Performance Tests', tests: testSuite.performanceTests },
    ];

    testCategories.forEach(category => {
      lines.push(`## ${category.name}`);
      lines.push('');
      
      category.tests.forEach(test => {
        const status = test.success ? '✅' : '❌';
        lines.push(`${status} **${test.test}** (${test.duration}ms)`);
        lines.push(`   ${test.message}`);
        if (test.error) {
          lines.push(`   Error: ${test.error}`);
        }
        lines.push('');
      });
    });

    return lines.join('\n');
  }
}

// Export for easy access
export const distancePricingTesting = DistancePricingTestingService;
