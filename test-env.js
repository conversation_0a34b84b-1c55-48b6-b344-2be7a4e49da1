#!/usr/bin/env node

/**
 * Ocean Soul Sparkles Mobile App - Environment Test
 */

require('dotenv').config({ path: '.env.local' });

console.log('🌊 Ocean Soul Sparkles Mobile App - Environment Test\n');

const envVars = [
  'EXPO_PUBLIC_SUPABASE_URL',
  'EXPO_PUBLIC_SUPABASE_ANON_KEY',
  'EXPO_PUBLIC_SQUARE_APPLICATION_ID',
  'EXPO_PUBLIC_SQUARE_LOCATION_ID',
  'EXPO_PUBLIC_API_BASE_URL',
  'EXPO_PUBLIC_ENVIRONMENT',
  'EXPO_PUBLIC_DEBUG_MODE'
];

console.log('📋 Environment Variables Status:');
envVars.forEach(varName => {
  const value = process.env[varName];
  const status = value ? '✅' : '❌';
  const displayValue = value ? 
    (varName.includes('KEY') || varName.includes('TOKEN') ? 
      `${value.substring(0, 10)}...` : value) : 
    'NOT SET';
  
  console.log(`${status} ${varName}: ${displayValue}`);
});

console.log('\n🔧 Configuration Status:');
const supabaseConfigured = !!(process.env.EXPO_PUBLIC_SUPABASE_URL && process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY);
const squareConfigured = !!(process.env.EXPO_PUBLIC_SQUARE_APPLICATION_ID && process.env.EXPO_PUBLIC_SQUARE_LOCATION_ID);

console.log(`${supabaseConfigured ? '✅' : '❌'} Supabase: ${supabaseConfigured ? 'Configured' : 'Missing credentials'}`);
console.log(`${squareConfigured ? '✅' : '❌'} Square: ${squareConfigured ? 'Configured' : 'Missing credentials'}`);

console.log('\n🚀 Next Steps:');
if (!supabaseConfigured) {
  console.log('   1. Add your Supabase URL and anon key to .env.local');
}
if (!squareConfigured) {
  console.log('   2. Add your Square application ID and location ID to .env.local');
}
if (supabaseConfigured && squareConfigured) {
  console.log('   ✅ All credentials configured! Ready to start development.');
} else {
  console.log('   3. Run npm start after updating .env.local');
}

console.log('\n📱 The app will start with basic functionality even without credentials.');
