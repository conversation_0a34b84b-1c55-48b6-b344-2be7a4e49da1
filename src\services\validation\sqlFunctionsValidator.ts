/**
 * Ocean Soul Sparkles Mobile App - SQL Functions & Stored Procedures Validator
 * Validates database functions, triggers, and stored procedures
 */

import { supabase } from '@/services/database/supabase';

export interface SQLFunctionValidationResult {
  test: string;
  functionName: string;
  status: 'pass' | 'fail' | 'warning';
  message: string;
  duration: number;
  details?: any;
  error?: string;
}

export interface DatabaseFunction {
  function_name: string;
  function_type: 'function' | 'trigger' | 'procedure';
  return_type?: string;
  parameters?: string[];
  description?: string;
}

export class SQLFunctionsValidator {
  private static instance: SQLFunctionsValidator;

  // Expected database functions for Ocean Soul Sparkles
  private readonly EXPECTED_FUNCTIONS: DatabaseFunction[] = [
    {
      function_name: 'update_updated_at_column',
      function_type: 'function',
      return_type: 'trigger',
      description: 'Updates the updated_at timestamp automatically',
    },
    {
      function_name: 'generate_transaction_number',
      function_type: 'function',
      return_type: 'text',
      description: 'Generates unique transaction numbers',
    },
    {
      function_name: 'calculate_booking_total',
      function_type: 'function',
      return_type: 'numeric',
      description: 'Calculates total amount for bookings',
    },
  ];

  // Expected triggers
  private readonly EXPECTED_TRIGGERS = [
    'update_transactions_updated_at',
    'update_bookings_updated_at',
    'update_customers_updated_at',
    'update_quotes_updated_at',
  ];

  private constructor() {}

  public static getInstance(): SQLFunctionsValidator {
    if (!SQLFunctionsValidator.instance) {
      SQLFunctionsValidator.instance = new SQLFunctionsValidator();
    }
    return SQLFunctionsValidator.instance;
  }

  /**
   * Validate core database functions existence
   */
  async validateCoreFunctions(): Promise<SQLFunctionValidationResult[]> {
    console.log('⚙️ Validating core database functions...');
    const results: SQLFunctionValidationResult[] = [];

    for (const expectedFunction of this.EXPECTED_FUNCTIONS) {
      const startTime = Date.now();
      
      try {
        // Check if function exists by querying information_schema
        const { data: functionInfo, error } = await supabase
          .rpc('check_function_exists', { function_name: expectedFunction.function_name })
          .catch(async () => {
            // Fallback: try to call the function with test parameters
            return await this.testFunctionExistence(expectedFunction);
          });

        if (error && !functionInfo) {
          // Function doesn't exist or can't be accessed
          results.push({
            test: 'Core Function Existence',
            functionName: expectedFunction.function_name,
            status: expectedFunction.function_name === 'update_updated_at_column' ? 'fail' : 'warning',
            message: `Function '${expectedFunction.function_name}' not found or not accessible`,
            duration: Date.now() - startTime,
            details: {
              expectedFunction,
              error: error?.message,
            },
            error: error?.message,
          });
        } else {
          // Function exists
          results.push({
            test: 'Core Function Existence',
            functionName: expectedFunction.function_name,
            status: 'pass',
            message: `Function '${expectedFunction.function_name}' exists and accessible`,
            duration: Date.now() - startTime,
            details: {
              expectedFunction,
              functionInfo,
            },
          });
        }

      } catch (error) {
        results.push({
          test: 'Core Function Existence',
          functionName: expectedFunction.function_name,
          status: 'fail',
          message: `Failed to validate function '${expectedFunction.function_name}'`,
          duration: Date.now() - startTime,
          error: error instanceof Error ? error.message : 'Unknown error',
        });
      }
    }

    return results;
  }

  /**
   * Test function existence by attempting to call it
   */
  private async testFunctionExistence(func: DatabaseFunction): Promise<{ data: any; error: any }> {
    try {
      switch (func.function_name) {
        case 'update_updated_at_column':
          // This is a trigger function, test by checking if triggers exist
          const { data: triggerData, error: triggerError } = await supabase
            .from('information_schema.triggers')
            .select('trigger_name')
            .ilike('trigger_name', '%updated_at%')
            .limit(1);
          return { data: triggerData, error: triggerError };

        case 'generate_transaction_number':
          // Test by calling with sample parameters
          const { data: transactionData, error: transactionError } = await supabase
            .rpc('generate_transaction_number', {});
          return { data: transactionData, error: transactionError };

        case 'calculate_booking_total':
          // Test by calling with sample booking ID
          const { data: bookingData, error: bookingError } = await supabase
            .rpc('calculate_booking_total', { booking_id: 'test' });
          return { data: bookingData, error: bookingError };

        default:
          return { data: null, error: { message: 'Unknown function' } };
      }
    } catch (error) {
      return { data: null, error };
    }
  }

  /**
   * Validate trigger functionality
   */
  async validateTriggers(): Promise<SQLFunctionValidationResult[]> {
    console.log('🔄 Validating database triggers...');
    const results: SQLFunctionValidationResult[] = [];

    // Test update_updated_at trigger by creating and updating a test record
    const startTime = Date.now();
    
    try {
      // Test with customers table (should have updated_at trigger)
      const testCustomerData = {
        email: `trigger-test-${Date.now()}@oceansoulsparkles.com.au`,
        full_name: 'Trigger Test Customer',
        phone: '+61 400 000 000',
      };

      // Create test customer
      const { data: customer, error: createError } = await supabase
        .from('customers')
        .insert(testCustomerData)
        .select()
        .single();

      if (createError || !customer) {
        results.push({
          test: 'Trigger Functionality',
          functionName: 'update_updated_at_column',
          status: 'warning',
          message: 'Cannot test triggers - unable to create test record',
          duration: Date.now() - startTime,
          error: createError?.message,
        });
        return results;
      }

      const originalUpdatedAt = customer.updated_at;

      // Wait a moment to ensure timestamp difference
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Update the customer to trigger the updated_at update
      const { data: updatedCustomer, error: updateError } = await supabase
        .from('customers')
        .update({ phone: '+61 400 111 222' })
        .eq('id', customer.id)
        .select()
        .single();

      // Cleanup test customer
      await supabase.from('customers').delete().eq('id', customer.id);

      if (updateError || !updatedCustomer) {
        results.push({
          test: 'Trigger Functionality',
          functionName: 'update_updated_at_column',
          status: 'warning',
          message: 'Trigger test inconclusive - update operation failed',
          duration: Date.now() - startTime,
          error: updateError?.message,
        });
      } else {
        const triggerWorking = new Date(updatedCustomer.updated_at) > new Date(originalUpdatedAt);
        
        results.push({
          test: 'Trigger Functionality',
          functionName: 'update_updated_at_column',
          status: triggerWorking ? 'pass' : 'fail',
          message: triggerWorking 
            ? 'updated_at trigger working correctly'
            : 'updated_at trigger not functioning',
          duration: Date.now() - startTime,
          details: {
            originalUpdatedAt,
            newUpdatedAt: updatedCustomer.updated_at,
            triggerWorking,
            testCustomerId: customer.id,
          },
        });
      }

    } catch (error) {
      results.push({
        test: 'Trigger Functionality',
        functionName: 'update_updated_at_column',
        status: 'fail',
        message: 'Failed to test trigger functionality',
        duration: Date.now() - startTime,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }

    return results;
  }

  /**
   * Validate invoice generation functions
   */
  async validateInvoiceFunctions(): Promise<SQLFunctionValidationResult[]> {
    console.log('🧾 Validating invoice generation functions...');
    const results: SQLFunctionValidationResult[] = [];

    const invoiceFunctions = [
      'generate_invoice_number',
      'calculate_invoice_total',
      'create_invoice_from_quote',
      'update_invoice_status',
    ];

    for (const functionName of invoiceFunctions) {
      const startTime = Date.now();
      
      try {
        // Test each invoice function
        let testResult;
        
        switch (functionName) {
          case 'generate_invoice_number':
            testResult = await supabase.rpc('generate_invoice_number', {});
            break;
          case 'calculate_invoice_total':
            testResult = await supabase.rpc('calculate_invoice_total', { invoice_id: 'test' });
            break;
          case 'create_invoice_from_quote':
            testResult = await supabase.rpc('create_invoice_from_quote', { quote_id: 'test' });
            break;
          case 'update_invoice_status':
            testResult = await supabase.rpc('update_invoice_status', { 
              invoice_id: 'test', 
              new_status: 'draft' 
            });
            break;
          default:
            testResult = { data: null, error: { message: 'Unknown function' } };
        }

        results.push({
          test: 'Invoice Function Validation',
          functionName,
          status: testResult.error ? 'warning' : 'pass',
          message: testResult.error 
            ? `Invoice function '${functionName}' not available or has issues`
            : `Invoice function '${functionName}' working correctly`,
          duration: Date.now() - startTime,
          details: {
            functionExists: !testResult.error,
            testResult: testResult.data,
            error: testResult.error?.message,
          },
          error: testResult.error?.message,
        });

      } catch (error) {
        results.push({
          test: 'Invoice Function Validation',
          functionName,
          status: 'warning',
          message: `Invoice function '${functionName}' validation failed`,
          duration: Date.now() - startTime,
          error: error instanceof Error ? error.message : 'Unknown error',
        });
      }
    }

    return results;
  }

  /**
   * Validate acknowledgment workflow functions
   */
  async validateAcknowledgmentFunctions(): Promise<SQLFunctionValidationResult[]> {
    console.log('✅ Validating acknowledgment workflow functions...');
    const results: SQLFunctionValidationResult[] = [];

    const acknowledgmentFunctions = [
      'create_booking_acknowledgment',
      'send_quote_acknowledgment',
      'update_acknowledgment_status',
      'get_pending_acknowledgments',
    ];

    for (const functionName of acknowledgmentFunctions) {
      const startTime = Date.now();
      
      try {
        // Test each acknowledgment function
        let testResult;
        
        switch (functionName) {
          case 'create_booking_acknowledgment':
            testResult = await supabase.rpc('create_booking_acknowledgment', { booking_id: 'test' });
            break;
          case 'send_quote_acknowledgment':
            testResult = await supabase.rpc('send_quote_acknowledgment', { quote_id: 'test' });
            break;
          case 'update_acknowledgment_status':
            testResult = await supabase.rpc('update_acknowledgment_status', { 
              acknowledgment_id: 'test', 
              status: 'sent' 
            });
            break;
          case 'get_pending_acknowledgments':
            testResult = await supabase.rpc('get_pending_acknowledgments', {});
            break;
          default:
            testResult = { data: null, error: { message: 'Unknown function' } };
        }

        results.push({
          test: 'Acknowledgment Function Validation',
          functionName,
          status: testResult.error ? 'warning' : 'pass',
          message: testResult.error 
            ? `Acknowledgment function '${functionName}' not available`
            : `Acknowledgment function '${functionName}' working correctly`,
          duration: Date.now() - startTime,
          details: {
            functionExists: !testResult.error,
            testResult: testResult.data,
            error: testResult.error?.message,
          },
          error: testResult.error?.message,
        });

      } catch (error) {
        results.push({
          test: 'Acknowledgment Function Validation',
          functionName,
          status: 'warning',
          message: `Acknowledgment function '${functionName}' validation failed`,
          duration: Date.now() - startTime,
          error: error instanceof Error ? error.message : 'Unknown error',
        });
      }
    }

    return results;
  }

  /**
   * Validate custom SQL procedures
   */
  async validateCustomProcedures(): Promise<SQLFunctionValidationResult[]> {
    console.log('🔧 Validating custom SQL procedures...');
    const results: SQLFunctionValidationResult[] = [];

    const customProcedures = [
      'cleanup_old_transactions',
      'generate_monthly_report',
      'backup_customer_data',
      'sync_with_admin_portal',
    ];

    for (const procedureName of customProcedures) {
      const startTime = Date.now();
      
      try {
        // Test each custom procedure
        const { data, error } = await supabase.rpc(procedureName, {});

        results.push({
          test: 'Custom Procedure Validation',
          functionName: procedureName,
          status: error ? 'warning' : 'pass',
          message: error 
            ? `Custom procedure '${procedureName}' not available`
            : `Custom procedure '${procedureName}' accessible`,
          duration: Date.now() - startTime,
          details: {
            procedureExists: !error,
            testResult: data,
            error: error?.message,
          },
          error: error?.message,
        });

      } catch (error) {
        results.push({
          test: 'Custom Procedure Validation',
          functionName: procedureName,
          status: 'warning',
          message: `Custom procedure '${procedureName}' validation failed`,
          duration: Date.now() - startTime,
          error: error instanceof Error ? error.message : 'Unknown error',
        });
      }
    }

    return results;
  }

  /**
   * Run comprehensive SQL functions validation
   */
  async runComprehensiveSQLValidation(): Promise<SQLFunctionValidationResult[]> {
    console.log('🧪 Running comprehensive SQL functions validation...');

    const [
      coreFunctions,
      triggers,
      invoiceFunctions,
      acknowledgmentFunctions,
      customProcedures,
    ] = await Promise.all([
      this.validateCoreFunctions(),
      this.validateTriggers(),
      this.validateInvoiceFunctions(),
      this.validateAcknowledgmentFunctions(),
      this.validateCustomProcedures(),
    ]);

    const allResults = [
      ...coreFunctions,
      ...triggers,
      ...invoiceFunctions,
      ...acknowledgmentFunctions,
      ...customProcedures,
    ];

    const passedTests = allResults.filter(r => r.status === 'pass').length;
    const totalTests = allResults.length;

    console.log(`✅ SQL functions validation completed: ${passedTests}/${totalTests} tests passed`);

    return allResults;
  }

  /**
   * Get SQL functions summary
   */
  getSQLFunctionsSummary(results: SQLFunctionValidationResult[]): {
    totalFunctions: number;
    workingFunctions: number;
    missingFunctions: number;
    criticalMissing: number;
    functionsByType: Record<string, number>;
  } {
    const criticalFunctions = ['update_updated_at_column'];
    
    const functionsByType = results.reduce((acc, result) => {
      const type = result.test.replace(' Validation', '').replace(' Functionality', '');
      acc[type] = (acc[type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return {
      totalFunctions: results.length,
      workingFunctions: results.filter(r => r.status === 'pass').length,
      missingFunctions: results.filter(r => r.status === 'fail' || r.status === 'warning').length,
      criticalMissing: results.filter(r => 
        (r.status === 'fail' || r.status === 'warning') && 
        criticalFunctions.includes(r.functionName)
      ).length,
      functionsByType,
    };
  }
}

// Export singleton instance
export const sqlFunctionsValidator = SQLFunctionsValidator.getInstance();
