/**
 * Ocean Soul Sparkles Mobile App - Real Bookings Screen
 * Displays actual booking/appointment data from the database
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  RefreshControl,
  FlatList,
  ScrollView,
  Modal
} from 'react-native';
import { Booking } from '@/types/database';
import { bookingService } from '@/services/database/bookingService';
import { websiteBookingService } from '@/services/booking/websiteBookingService';
import { bookingWorkflowService } from '@/services/workflow/bookingWorkflowService';
import { testBookingSync, testAdminDashboardSync } from '@/utils/testBookingSync';
import EnhancedBookingReviewScreen from './EnhancedBookingReviewScreen';
// import BookingDetailScreen from './BookingDetailScreen';

const BookingsScreenReal: React.FC = () => {
  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0]);
  const [currentMonth, setCurrentMonth] = useState(new Date());
  const [bookings, setBookings] = useState<Booking[]>([]);
  const [monthlyBookingCounts, setMonthlyBookingCounts] = useState<Record<string, number>>({});
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showCalendar, setShowCalendar] = useState(false);
  const [viewMode, setViewMode] = useState<'day' | 'week' | 'month'>('day');
  const [selectedBookingId, setSelectedBookingId] = useState<string | null>(null);
  const [showBookingDetail, setShowBookingDetail] = useState(false);

  console.log('🔄 BookingsScreenReal loaded - REAL DATA VERSION with Enhanced Calendar');

  useEffect(() => {
    loadBookings();
    loadMonthlyBookingCounts();
  }, [selectedDate, currentMonth]);

  // Date navigation helper functions
  const navigateDate = (direction: 'prev' | 'next') => {
    const currentDate = new Date(selectedDate);
    const newDate = new Date(currentDate);

    if (viewMode === 'day') {
      newDate.setDate(currentDate.getDate() + (direction === 'next' ? 1 : -1));
    } else if (viewMode === 'week') {
      newDate.setDate(currentDate.getDate() + (direction === 'next' ? 7 : -7));
    } else if (viewMode === 'month') {
      newDate.setMonth(currentDate.getMonth() + (direction === 'next' ? 1 : -1));
    }

    setSelectedDate(newDate.toISOString().split('T')[0]);
    setCurrentMonth(newDate);
  };

  const goToToday = () => {
    const today = new Date().toISOString().split('T')[0];
    setSelectedDate(today);
    setCurrentMonth(new Date());
  };

  const navigateMonth = (direction: 'prev' | 'next') => {
    const newMonth = new Date(currentMonth);
    newMonth.setMonth(currentMonth.getMonth() + (direction === 'next' ? 1 : -1));
    setCurrentMonth(newMonth);
  };

  const loadBookings = async (isRefresh = false) => {
    try {
      if (isRefresh) {
        setRefreshing(true);
      } else {
        setLoading(true);
      }
      setError(null);

      console.log('📅 Loading bookings for date:', selectedDate);

      let dateFrom = selectedDate;
      let dateTo = selectedDate;

      // Adjust date range based on view mode
      if (viewMode === 'week') {
        const startOfWeek = new Date(selectedDate);
        const dayOfWeek = startOfWeek.getDay();
        startOfWeek.setDate(startOfWeek.getDate() - dayOfWeek);
        const endOfWeek = new Date(startOfWeek);
        endOfWeek.setDate(startOfWeek.getDate() + 6);

        dateFrom = startOfWeek.toISOString().split('T')[0];
        dateTo = endOfWeek.toISOString().split('T')[0];
      } else if (viewMode === 'month') {
        const startOfMonth = new Date(currentMonth.getFullYear(), currentMonth.getMonth(), 1);
        const endOfMonth = new Date(currentMonth.getFullYear(), currentMonth.getMonth() + 1, 0);

        dateFrom = startOfMonth.toISOString().split('T')[0];
        dateTo = endOfMonth.toISOString().split('T')[0];
      }

      const result = await bookingService.getBookings({
        filters: {
          date_from: dateFrom,
          date_to: dateTo,
        },
        order_by: 'start_time',
        order_direction: 'asc',
      });

      if (result.error) {
        throw new Error(result.error.message);
      }

      setBookings(result.data || []);
      console.log(`✅ Loaded ${result.data?.length || 0} bookings for ${viewMode} view`);

    } catch (err) {
      console.error('❌ Failed to load bookings:', err);
      setError(err instanceof Error ? err.message : 'Failed to load bookings');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const loadMonthlyBookingCounts = async () => {
    try {
      // Load booking counts for the entire month to show indicators
      const startOfMonth = new Date(currentMonth.getFullYear(), currentMonth.getMonth(), 1);
      const endOfMonth = new Date(currentMonth.getFullYear(), currentMonth.getMonth() + 1, 0);

      const result = await bookingService.getBookings({
        filters: {
          date_from: startOfMonth.toISOString().split('T')[0],
          date_to: endOfMonth.toISOString().split('T')[0],
        },
        order_by: 'booking_date',
        order_direction: 'asc',
      });

      if (result.data) {
        const counts: Record<string, number> = {};
        result.data.forEach(booking => {
          const date = booking.booking_date;
          counts[date] = (counts[date] || 0) + 1;
        });
        setMonthlyBookingCounts(counts);
        console.log(`📊 Loaded booking counts for ${Object.keys(counts).length} days`);
      }
    } catch (err) {
      console.error('❌ Failed to load monthly booking counts:', err);
    }
  };

  const handleDebugBookings = async () => {
    console.log('🔧 Running booking database debug...');
    try {
      const syncResult = await testBookingSync();
      const adminSyncResult = await testAdminDashboardSync();

      console.log('Sync test result:', syncResult);
      console.log('Admin sync result:', adminSyncResult);

      if (syncResult.success && adminSyncResult.success) {
        Alert.alert(
          'Debug Complete ✅',
          `Found ${syncResult.totalBookings} bookings. ${adminSyncResult.matches ? 'Synced with admin dashboard!' : `Expected 4, found ${adminSyncResult.totalBookings}`}. Check console for details.`
        );
      } else {
        const error = syncResult.error || adminSyncResult.error || 'Unknown error';
        Alert.alert(
          'Debug Complete ⚠️',
          `Issue found: ${error}. Check console for details.`
        );
      }
    } catch (error) {
      console.error('Debug failed:', error);
      Alert.alert('Debug Failed', 'Check console for errors');
    }
  };

  const formatTime = (timeString: string) => {
    try {
      const date = new Date(timeString);
      return date.toLocaleTimeString('en-US', {
        hour: 'numeric',
        minute: '2-digit',
        hour12: true,
      });
    } catch {
      return timeString;
    }
  };

  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString('en-US', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric',
      });
    } catch {
      return dateString;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'confirmed': return '#10b981';
      case 'pending': return '#f59e0b';
      case 'cancelled': return '#ef4444';
      case 'completed': return '#6366f1';
      default: return '#6b7280';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status.toLowerCase()) {
      case 'confirmed': return '✅';
      case 'pending': return '⏳';
      case 'cancelled': return '❌';
      case 'completed': return '🎉';
      default: return '📅';
    }
  };

  const createTestBooking = async () => {
    try {
      console.log('🧪 Creating test booking...');

      // Create a test booking with minimal required data
      const testBookingData = {
        customer_id: 'test-customer-id', // This will likely fail, but we can test the validation
        service_id: 'test-service-id',
        booking_date: new Date().toISOString().split('T')[0], // Today's date
        start_time: '10:00',
        status: 'pending' as const,
        notes: 'Test booking created from mobile app for validation testing',
      };

      const result = await bookingService.createBooking(testBookingData);

      if (result.error) {
        // Expected to fail with invalid IDs, but we can test the error handling
        console.log('✅ Test booking validation working - error caught:', result.error.message);
        Alert.alert(
          'Test Validation Passed ✅',
          `Booking creation validation is working correctly:\n\n${result.error.message}\n\nThis confirms the booking service is properly validating input data.`,
          [{ text: 'OK' }]
        );
      } else {
        // Unexpected success - this would mean the test IDs somehow worked
        console.log('✅ Test booking created successfully:', result.data);
        Alert.alert(
          'Test Booking Created ✅',
          'Test booking was created successfully! The booking creation workflow is working.',
          [
            {
              text: 'OK',
              onPress: () => loadBookings(), // Refresh the booking list
            },
          ]
        );
      }
    } catch (error) {
      console.error('❌ Test booking creation failed:', error);
      Alert.alert(
        'Test Results 📋',
        `Booking service test completed:\n\nError: ${error}\n\nThis helps validate error handling in the booking creation workflow.`,
        [{ text: 'OK' }]
      );
    }
  };

  // Calendar rendering functions
  const renderCalendarHeader = () => (
    <View style={styles.calendarHeader}>
      <TouchableOpacity onPress={() => navigateMonth('prev')} style={styles.navButton}>
        <Text style={styles.navButtonText}>‹</Text>
      </TouchableOpacity>

      <Text style={styles.monthYearText}>
        {currentMonth.toLocaleDateString('en-US', { month: 'long', year: 'numeric' })}
      </Text>

      <TouchableOpacity onPress={() => navigateMonth('next')} style={styles.navButton}>
        <Text style={styles.navButtonText}>›</Text>
      </TouchableOpacity>
    </View>
  );

  const renderCalendarGrid = () => {
    const startOfMonth = new Date(currentMonth.getFullYear(), currentMonth.getMonth(), 1);
    const endOfMonth = new Date(currentMonth.getFullYear(), currentMonth.getMonth() + 1, 0);
    const startOfCalendar = new Date(startOfMonth);
    startOfCalendar.setDate(startOfCalendar.getDate() - startOfMonth.getDay());

    const days = [];
    const currentDate = new Date(startOfCalendar);

    // Add day headers
    const dayHeaders = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];

    for (let week = 0; week < 6; week++) {
      for (let day = 0; day < 7; day++) {
        const dateString = currentDate.toISOString().split('T')[0];
        const isCurrentMonth = currentDate.getMonth() === currentMonth.getMonth();
        const isSelected = dateString === selectedDate;
        const isToday = dateString === new Date().toISOString().split('T')[0];
        const bookingCount = monthlyBookingCounts[dateString] || 0;

        days.push({
          date: new Date(currentDate),
          dateString,
          isCurrentMonth,
          isSelected,
          isToday,
          bookingCount,
        });

        currentDate.setDate(currentDate.getDate() + 1);
      }
    }

    return (
      <View style={styles.calendarGrid}>
        <View style={styles.dayHeaderRow}>
          {dayHeaders.map(header => (
            <Text key={header} style={styles.dayHeader}>{header}</Text>
          ))}
        </View>

        {Array.from({ length: 6 }, (_, weekIndex) => (
          <View key={weekIndex} style={styles.calendarWeek}>
            {days.slice(weekIndex * 7, (weekIndex + 1) * 7).map((dayInfo) => (
              <TouchableOpacity
                key={dayInfo.dateString}
                style={[
                  styles.calendarDay,
                  !dayInfo.isCurrentMonth && styles.calendarDayInactive,
                  dayInfo.isSelected && styles.calendarDaySelected,
                  dayInfo.isToday && styles.calendarDayToday,
                ]}
                onPress={() => {
                  setSelectedDate(dayInfo.dateString);
                  setShowCalendar(false);
                }}
              >
                <Text style={[
                  styles.calendarDayText,
                  !dayInfo.isCurrentMonth && styles.calendarDayTextInactive,
                  dayInfo.isSelected && styles.calendarDayTextSelected,
                  dayInfo.isToday && styles.calendarDayTextToday,
                ]}>
                  {dayInfo.date.getDate()}
                </Text>
                {dayInfo.bookingCount > 0 && (
                  <View style={styles.bookingIndicator}>
                    <Text style={styles.bookingIndicatorText}>{dayInfo.bookingCount}</Text>
                  </View>
                )}
              </TouchableOpacity>
            ))}
          </View>
        ))}
      </View>
    );
  };

  const handleBookingPress = (booking: Booking) => {
    setSelectedBookingId(booking.id);
    setShowBookingDetail(true);
  };

  const handleBookingDetailClose = () => {
    setShowBookingDetail(false);
    setSelectedBookingId(null);
  };

  const handleBookingUpdated = () => {
    loadBookings(); // Reload bookings to reflect changes
  };

  const renderBooking = ({ item }: { item: Booking }) => (
    <TouchableOpacity
      style={styles.bookingCard}
      activeOpacity={0.7}
      onPress={() => handleBookingPress(item)}
    >
      <View style={styles.bookingHeader}>
        <View style={styles.timeContainer}>
          <Text style={styles.timeText}>{formatTime(item.start_time)}</Text>
          {item.end_time && (
            <Text style={styles.durationText}>
              - {formatTime(item.end_time)}
            </Text>
          )}
        </View>

        <View style={[styles.statusBadge, { backgroundColor: getStatusColor(item.status) }]}>
          <Text style={styles.statusText}>
            {getStatusIcon(item.status)} {item.status.toUpperCase()}
          </Text>
        </View>
      </View>

      <View style={styles.bookingContent}>
        <Text style={styles.customerName}>
          {item.customer?.full_name || 'Unknown Customer'}
        </Text>
        <Text style={styles.serviceName}>
          {item.service?.name || 'Unknown Service'}
        </Text>
        {item.staff && (
          <Text style={styles.staffName}>
            👤 {item.staff.first_name} {item.staff.last_name}
          </Text>
        )}
        {item.total_amount && (
          <Text style={styles.amount}>
            💰 ${item.total_amount.toFixed(2)}
          </Text>
        )}
      </View>

      {/* Visual indicator that card is clickable */}
      <View style={styles.clickIndicator}>
        <Text style={styles.clickIndicatorText}>
          {item.status === 'pending' ? '💰 Tap to create quote' : '✏️ Tap to edit'}
        </Text>
      </View>
    </TouchableOpacity>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyContainer}>
      <Text style={styles.emptyIcon}>📅</Text>
      <Text style={styles.emptyText}>No bookings found</Text>
      <Text style={styles.emptySubtext}>
        {error
          ? 'There was an error loading bookings'
          : `No appointments scheduled for ${formatDate(selectedDate)}`
        }
      </Text>
      {error && (
        <TouchableOpacity onPress={() => loadBookings()} style={styles.retryButton}>
          <Text style={styles.retryButtonText}>🔄 Retry</Text>
        </TouchableOpacity>
      )}
    </View>
  );

  const renderDateNavigation = () => (
    <View style={styles.dateNavContainer}>
      {/* View Mode Selector */}
      <View style={styles.viewModeContainer}>
        {(['day', 'week', 'month'] as const).map(mode => (
          <TouchableOpacity
            key={mode}
            style={[
              styles.viewModeButton,
              viewMode === mode && styles.viewModeButtonActive
            ]}
            onPress={() => setViewMode(mode)}
          >
            <Text style={[
              styles.viewModeText,
              viewMode === mode && styles.viewModeTextActive
            ]}>
              {mode.charAt(0).toUpperCase() + mode.slice(1)}
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      {/* Date Navigation */}
      <View style={styles.dateNavRow}>
        <TouchableOpacity onPress={() => navigateDate('prev')} style={styles.navButton}>
          <Text style={styles.navButtonText}>‹</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.dateDisplayContainer}
          onPress={() => setShowCalendar(true)}
        >
          <Text style={styles.dateDisplayText}>
            {viewMode === 'day' && formatDate(selectedDate)}
            {viewMode === 'week' && `Week of ${formatDate(selectedDate)}`}
            {viewMode === 'month' && currentMonth.toLocaleDateString('en-US', { month: 'long', year: 'numeric' })}
          </Text>
          <Text style={styles.calendarIcon}>📅</Text>
        </TouchableOpacity>

        <TouchableOpacity onPress={() => navigateDate('next')} style={styles.navButton}>
          <Text style={styles.navButtonText}>›</Text>
        </TouchableOpacity>
      </View>

      {/* Quick Actions */}
      <View style={styles.quickActionsRow}>
        <TouchableOpacity onPress={goToToday} style={styles.todayButton}>
          <Text style={styles.todayButtonText}>Today</Text>
        </TouchableOpacity>

        <View style={styles.bookingCountBadge}>
          <Text style={styles.bookingCountText}>
            {bookings.length} {viewMode === 'day' ? 'appointments' : 'total bookings'}
          </Text>
        </View>
      </View>
    </View>
  );

  const renderStatsHeader = () => (
    <View style={styles.statsContainer}>
      <View style={styles.statItem}>
        <Text style={styles.statNumber}>{bookings.length}</Text>
        <Text style={styles.statLabel}>
          {viewMode === 'day' ? 'Appointments' :
           viewMode === 'week' ? 'This Week' : 'This Month'}
        </Text>
      </View>
      <View style={styles.statItem}>
        <Text style={styles.statNumber}>
          {bookings.filter(b => b.status === 'confirmed').length}
        </Text>
        <Text style={styles.statLabel}>Confirmed</Text>
      </View>
      <View style={styles.statItem}>
        <Text style={styles.statNumber}>
          {bookings.filter(b => b.status === 'pending').length}
        </Text>
        <Text style={styles.statLabel}>Pending</Text>
      </View>
    </View>
  );

  if (loading && bookings.length === 0) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#FF9A8B" />
          <Text style={styles.loadingText}>Loading real bookings...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.headerContent}>
          <View>
            <Text style={styles.headerTitle}>📅 Bookings & Appointments</Text>
            <Text style={styles.headerSubtitle}>Enhanced Calendar View</Text>
          </View>

          <TouchableOpacity
            style={styles.newBookingButton}
            onPress={() => {
              Alert.alert(
                'Test New Booking',
                'Create a test booking to validate the booking creation workflow?',
                [
                  { text: 'Cancel', style: 'cancel' },
                  { text: 'Create Test Booking', onPress: createTestBooking },
                ]
              );
            }}
          >
            <Text style={styles.newBookingButtonText}>+ New</Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Date Navigation */}
      {renderDateNavigation()}

      {/* Stats */}
      {!error && renderStatsHeader()}

      {/* Debug Controls */}
      <View style={styles.debugContainer}>
        <TouchableOpacity
          style={styles.debugButton}
          onPress={handleDebugBookings}
        >
          <Text style={styles.debugButtonText}>🔧 Debug Database</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.debugButton, { backgroundColor: '#10b981' }]}
          onPress={async () => {
            const result = await testAdminDashboardSync();
            if (result.success) {
              Alert.alert(
                result.matches ? 'Sync Perfect ✅' : 'Sync Check ℹ️',
                result.message || `Found ${result.totalBookings} bookings`
              );
              if (result.matches) {
                loadBookings(); // Reload to show the synced data
              }
            } else {
              Alert.alert('Sync Failed ❌', result.error || 'Unknown error');
            }
          }}
        >
          <Text style={styles.debugButtonText}>🔗 Test Sync</Text>
        </TouchableOpacity>
      </View>

      {/* Content */}
      {error ? (
        <View style={styles.errorContainer}>
          <Text style={styles.errorIcon}>⚠️</Text>
          <Text style={styles.errorTitle}>Failed to Load Bookings</Text>
          <Text style={styles.errorText}>{error}</Text>
          <TouchableOpacity onPress={() => loadBookings()} style={styles.retryButton}>
            <Text style={styles.retryButtonText}>🔄 Retry</Text>
          </TouchableOpacity>
        </View>
      ) : (
        <FlatList
          data={bookings}
          renderItem={renderBooking}
          keyExtractor={(item) => item.id}
          contentContainerStyle={styles.listContainer}
          showsVerticalScrollIndicator={false}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={() => loadBookings(true)}
              colors={['#FF9A8B']}
            />
          }
          ListEmptyComponent={!loading ? renderEmptyState : null}
        />
      )}

      {/* Calendar Modal */}
      <Modal
        visible={showCalendar}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowCalendar(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.calendarModal}>
            <View style={styles.calendarModalHeader}>
              <Text style={styles.calendarModalTitle}>Select Date</Text>
              <TouchableOpacity
                onPress={() => setShowCalendar(false)}
                style={styles.closeButton}
              >
                <Text style={styles.closeButtonText}>✕</Text>
              </TouchableOpacity>
            </View>

            {renderCalendarHeader()}
            <ScrollView style={styles.calendarScrollView}>
              {renderCalendarGrid()}
            </ScrollView>

            <View style={styles.calendarModalFooter}>
              <TouchableOpacity
                onPress={goToToday}
                style={styles.todayModalButton}
              >
                <Text style={styles.todayModalButtonText}>Go to Today</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>

      {/* Booking Detail Modal - Temporarily disabled */}
      {showBookingDetail && selectedBookingId && (
        <Modal
          visible={showBookingDetail}
          animationType="slide"
          presentationStyle="pageSheet"
          onRequestClose={handleBookingDetailClose}
        >
          <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: '#fff' }}>
            <Text style={{ fontSize: 18, marginBottom: 20 }}>Booking Detail Screen</Text>
            <Text style={{ fontSize: 14, marginBottom: 20 }}>Booking ID: {selectedBookingId}</Text>
            <TouchableOpacity
              onPress={handleBookingDetailClose}
              style={{ backgroundColor: '#FF9A8B', padding: 12, borderRadius: 8 }}
            >
              <Text style={{ color: '#fff', fontWeight: 'bold' }}>Close</Text>
            </TouchableOpacity>
          </View>
        </Modal>
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    backgroundColor: '#FF9A8B',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomLeftRadius: 20,
    borderBottomRightRadius: 20,
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#fff',
  },
  headerSubtitle: {
    fontSize: 16,
    color: '#fff',
    marginTop: 4,
    opacity: 0.9,
  },
  newBookingButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.3)',
  },
  newBookingButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#666',
  },
  statsContainer: {
    flexDirection: 'row',
    backgroundColor: '#fff',
    marginHorizontal: 16,
    marginTop: 16,
    borderRadius: 12,
    padding: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  statItem: {
    flex: 1,
    alignItems: 'center',
  },
  statNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#FF9A8B',
  },
  statLabel: {
    fontSize: 12,
    color: '#666',
    marginTop: 4,
  },
  debugContainer: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    paddingVertical: 12,
    gap: 12,
  },
  debugButton: {
    flex: 1,
    backgroundColor: '#007AFF',
    borderRadius: 8,
    paddingVertical: 8,
    paddingHorizontal: 12,
  },
  debugButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  listContainer: {
    paddingHorizontal: 16,
    paddingBottom: 20,
  },
  bookingCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  bookingHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  timeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  timeText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  durationText: {
    fontSize: 14,
    color: '#666',
    marginLeft: 4,
  },
  statusBadge: {
    borderRadius: 16,
    paddingHorizontal: 12,
    paddingVertical: 4,
  },
  statusText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
  },
  bookingContent: {
    gap: 4,
  },
  customerName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  serviceName: {
    fontSize: 14,
    color: '#666',
  },
  staffName: {
    fontSize: 12,
    color: '#888',
  },
  amount: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#10b981',
  },
  clickIndicator: {
    marginTop: 12,
    paddingTop: 8,
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
  },
  clickIndicatorText: {
    fontSize: 12,
    color: '#007AFF',
    fontWeight: '600',
    textAlign: 'center',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  emptyIcon: {
    fontSize: 64,
    marginBottom: 16,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  emptySubtext: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    lineHeight: 20,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  errorIcon: {
    fontSize: 64,
    marginBottom: 16,
  },
  errorTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#ef4444',
    marginBottom: 8,
  },
  errorText: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: 20,
  },
  retryButton: {
    backgroundColor: '#FF9A8B',
    borderRadius: 8,
    paddingHorizontal: 20,
    paddingVertical: 10,
  },
  retryButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: 'bold',
  },
  // Date Navigation Styles
  dateNavContainer: {
    backgroundColor: '#fff',
    marginHorizontal: 16,
    marginTop: 12,
    borderRadius: 12,
    padding: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  viewModeContainer: {
    flexDirection: 'row',
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
    padding: 4,
    marginBottom: 12,
  },
  viewModeButton: {
    flex: 1,
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 6,
    alignItems: 'center',
  },
  viewModeButtonActive: {
    backgroundColor: '#FF9A8B',
  },
  viewModeText: {
    fontSize: 14,
    color: '#666',
    fontWeight: '500',
  },
  viewModeTextActive: {
    color: '#fff',
    fontWeight: 'bold',
  },
  dateNavRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  navButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#f5f5f5',
    alignItems: 'center',
    justifyContent: 'center',
  },
  navButtonText: {
    fontSize: 24,
    color: '#333',
    fontWeight: 'bold',
  },
  dateDisplayContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#f8f9fa',
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 16,
    marginHorizontal: 12,
  },
  dateDisplayText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    textAlign: 'center',
  },
  calendarIcon: {
    fontSize: 16,
    marginLeft: 8,
  },
  quickActionsRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  todayButton: {
    backgroundColor: '#007AFF',
    borderRadius: 8,
    paddingVertical: 8,
    paddingHorizontal: 16,
  },
  todayButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: 'bold',
  },
  bookingCountBadge: {
    backgroundColor: '#e3f2fd',
    borderRadius: 16,
    paddingVertical: 6,
    paddingHorizontal: 12,
  },
  bookingCountText: {
    fontSize: 12,
    color: '#1976d2',
    fontWeight: '600',
  },
  // Calendar Modal Styles
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  calendarModal: {
    backgroundColor: '#fff',
    borderRadius: 16,
    margin: 20,
    maxHeight: '80%',
    width: '90%',
  },
  calendarModalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#e5e5e5',
  },
  calendarModalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  closeButton: {
    width: 30,
    height: 30,
    borderRadius: 15,
    backgroundColor: '#f5f5f5',
    alignItems: 'center',
    justifyContent: 'center',
  },
  closeButtonText: {
    fontSize: 16,
    color: '#666',
  },
  calendarScrollView: {
    maxHeight: 400,
  },
  calendarHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  monthYearText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  calendarGrid: {
    paddingHorizontal: 20,
  },
  dayHeaderRow: {
    flexDirection: 'row',
    marginBottom: 8,
  },
  dayHeader: {
    flex: 1,
    textAlign: 'center',
    fontSize: 12,
    fontWeight: 'bold',
    color: '#666',
    paddingVertical: 8,
  },
  calendarWeek: {
    flexDirection: 'row',
  },
  calendarDay: {
    flex: 1,
    aspectRatio: 1,
    alignItems: 'center',
    justifyContent: 'center',
    margin: 1,
    borderRadius: 8,
    position: 'relative',
  },
  calendarDayInactive: {
    opacity: 0.3,
  },
  calendarDaySelected: {
    backgroundColor: '#FF9A8B',
  },
  calendarDayToday: {
    borderWidth: 2,
    borderColor: '#007AFF',
  },
  calendarDayText: {
    fontSize: 16,
    color: '#333',
    fontWeight: '500',
  },
  calendarDayTextInactive: {
    color: '#999',
  },
  calendarDayTextSelected: {
    color: '#fff',
    fontWeight: 'bold',
  },
  calendarDayTextToday: {
    color: '#007AFF',
    fontWeight: 'bold',
  },
  bookingIndicator: {
    position: 'absolute',
    top: 2,
    right: 2,
    backgroundColor: '#10b981',
    borderRadius: 8,
    minWidth: 16,
    height: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  bookingIndicatorText: {
    fontSize: 10,
    color: '#fff',
    fontWeight: 'bold',
  },
  calendarModalFooter: {
    padding: 20,
    borderTopWidth: 1,
    borderTopColor: '#e5e5e5',
  },
  todayModalButton: {
    backgroundColor: '#007AFF',
    borderRadius: 8,
    paddingVertical: 12,
    alignItems: 'center',
  },
  todayModalButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
});

export default BookingsScreenReal;
