/**
 * Ocean Soul Sparkles Mobile App - Staff Communication Service
 * Handles in-app chat and staff coordination for bookings
 */

import { supabase } from '@/services/database/supabase';
import { pushNotificationService } from '@/services/notifications/pushNotificationService';
import { RealtimeChannel } from '@supabase/supabase-js';
import { supabaseConnectionPool } from '@/utils/supabaseConnectionPool';

export interface ChatMessage {
  id: string;
  thread_id: string;
  sender_id: string;
  sender_name: string;
  message: string;
  message_type: 'text' | 'availability_response' | 'system';
  created_at: string;
  read_by: string[];
}

export interface ChatThread {
  id: string;
  booking_id: string;
  title: string;
  created_by: string;
  created_at: string;
  last_message_at: string;
  participants: string[];
  is_active: boolean;
}

export interface AvailabilityResponse {
  id: string;
  thread_id: string;
  staff_id: string;
  staff_name: string;
  status: 'available' | 'unavailable' | 'maybe';
  notes?: string;
  created_at: string;
}

class StaffCommunicationService {
  private channels: Map<string, RealtimeChannel> = new Map();
  private isInitialized = false;
  private maxChannels = 10; // Limit concurrent channels to prevent connection issues
  private channelQueue: string[] = []; // Queue for managing channel lifecycle
  private channelTimestamps: Map<string, number> = new Map(); // Track last activity
  private idleCleanupInterval: NodeJS.Timeout | null = null;
  private readonly IDLE_TIMEOUT = 10 * 60 * 1000; // 10 minutes

  /**
   * Initialize staff communication service
   */
  async initialize(): Promise<void> {
    try {
      console.log('💬 Initializing staff communication service...');

      // Initialize the connection pool
      supabaseConnectionPool.initialize();

      // Set up real-time subscriptions for chat messages
      await this.setupChatSubscriptions();

      // Start idle cleanup interval
      this.startIdleCleanup();

      this.isInitialized = true;
      console.log('✅ Staff communication service initialized');

    } catch (error) {
      console.error('❌ Failed to initialize staff communication:', error);
      throw error;
    }
  }

  /**
   * Create chat thread for booking
   */
  async createBookingChatThread(bookingId: string, bookingTitle: string): Promise<ChatThread | null> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      // Get all active staff for participants
      const { data: activeStaff } = await supabase
        .from('admin_users')
        .select('id')
        .eq('is_active', true);

      const participants = activeStaff?.map(staff => staff.id) || [];

      const threadData = {
        booking_id: bookingId,
        title: `Booking: ${bookingTitle}`,
        created_by: user.id,
        participants,
        is_active: true,
      };

      const { data: thread, error } = await supabase
        .from('chat_threads')
        .insert(threadData)
        .select()
        .single();

      if (error) throw error;

      // Send initial system message
      await this.sendSystemMessage(
        thread.id,
        `Chat thread created for booking: ${bookingTitle}. Please confirm your availability.`
      );

      return thread;

    } catch (error) {
      console.error('❌ Failed to create chat thread:', error);
      return null;
    }
  }

  /**
   * Send chat message
   */
  async sendMessage(threadId: string, message: string): Promise<ChatMessage | null> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      // Get user profile for sender name
      const { data: profile } = await supabase
        .from('admin_users')
        .select('first_name, last_name')
        .eq('id', user.id)
        .single();

      const senderName = profile 
        ? `${profile.first_name} ${profile.last_name}`.trim()
        : 'Unknown User';

      const messageData = {
        thread_id: threadId,
        sender_id: user.id,
        sender_name: senderName,
        message,
        message_type: 'text' as const,
        read_by: [user.id],
      };

      const { data: chatMessage, error } = await supabase
        .from('chat_messages')
        .insert(messageData)
        .select()
        .single();

      if (error) throw error;

      // Update thread last message time
      await supabase
        .from('chat_threads')
        .update({ last_message_at: new Date().toISOString() })
        .eq('id', threadId);

      // Send push notification to other participants
      await this.notifyThreadParticipants(threadId, user.id, message);

      return chatMessage;

    } catch (error) {
      console.error('❌ Failed to send message:', error);
      return null;
    }
  }

  /**
   * Send availability response
   */
  async sendAvailabilityResponse(
    threadId: string,
    status: 'available' | 'unavailable' | 'maybe',
    notes?: string
  ): Promise<AvailabilityResponse | null> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      // Get user profile
      const { data: profile } = await supabase
        .from('admin_users')
        .select('first_name, last_name')
        .eq('id', user.id)
        .single();

      const staffName = profile 
        ? `${profile.first_name} ${profile.last_name}`.trim()
        : 'Unknown Staff';

      const responseData = {
        thread_id: threadId,
        staff_id: user.id,
        staff_name: staffName,
        status,
        notes,
      };

      const { data: response, error } = await supabase
        .from('availability_responses')
        .upsert(responseData, { onConflict: 'thread_id,staff_id' })
        .select()
        .single();

      if (error) throw error;

      // Send system message about availability response
      const statusEmoji = status === 'available' ? '✅' : status === 'unavailable' ? '❌' : '❓';
      const systemMessage = `${statusEmoji} ${staffName} is ${status}${notes ? `: ${notes}` : ''}`;
      
      await this.sendSystemMessage(threadId, systemMessage);

      return response;

    } catch (error) {
      console.error('❌ Failed to send availability response:', error);
      return null;
    }
  }

  /**
   * Get chat thread messages
   */
  async getThreadMessages(threadId: string): Promise<ChatMessage[]> {
    try {
      const { data: messages, error } = await supabase
        .from('chat_messages')
        .select('*')
        .eq('thread_id', threadId)
        .order('created_at', { ascending: true });

      if (error) throw error;

      return messages || [];

    } catch (error) {
      console.error('❌ Failed to get thread messages:', error);
      return [];
    }
  }

  /**
   * Get availability responses for thread
   */
  async getAvailabilityResponses(threadId: string): Promise<AvailabilityResponse[]> {
    try {
      const { data: responses, error } = await supabase
        .from('availability_responses')
        .select('*')
        .eq('thread_id', threadId)
        .order('created_at', { ascending: false });

      if (error) throw error;

      return responses || [];

    } catch (error) {
      console.error('❌ Failed to get availability responses:', error);
      return [];
    }
  }

  /**
   * Start idle cleanup interval to remove unused subscriptions
   */
  private startIdleCleanup(): void {
    // Clean up idle channels every 5 minutes
    this.idleCleanupInterval = setInterval(() => {
      this.cleanupIdleChannels();
    }, 5 * 60 * 1000); // 5 minutes

    console.log('⏰ Idle cleanup interval started');
  }

  /**
   * Clean up channels that have been idle for too long
   */
  private cleanupIdleChannels(): void {
    const now = Date.now();
    const idleChannels: string[] = [];

    for (const [threadId, timestamp] of this.channelTimestamps) {
      if (now - timestamp > this.IDLE_TIMEOUT) {
        idleChannels.push(threadId);
      }
    }

    if (idleChannels.length > 0) {
      console.log(`🧹 Cleaning up ${idleChannels.length} idle channels`);
      for (const threadId of idleChannels) {
        this.unsubscribeFromThread(threadId);
      }
    }
  }

  /**
   * Update channel activity timestamp
   */
  private updateChannelActivity(threadId: string): void {
    this.channelTimestamps.set(threadId, Date.now());
  }

  /**
   * Subscribe to chat thread updates using connection pool
   */
  subscribeToThread(threadId: string, onMessage: (message: ChatMessage) => void): () => void {
    try {
      console.log(`📡 Subscribing to thread ${threadId} using connection pool`);

      // Use the connection pool for efficient subscription management
      const unsubscribe = supabaseConnectionPool.subscribe(
        `chat_thread_${threadId}`,
        'chat_messages',
        (payload) => {
          try {
            if (payload.new) {
              onMessage(payload.new as ChatMessage);
            }
          } catch (error) {
            console.error('❌ Error processing chat message:', error);
          }
        },
        {
          event: 'INSERT',
          filter: `thread_id=eq.${threadId}`,
        }
      );

      // Track the subscription for legacy compatibility
      this.updateChannelActivity(threadId);

      return unsubscribe;
    } catch (error) {
      console.error('❌ Error setting up chat subscription:', error);
      return () => {}; // Return empty cleanup function
    }
  }

  /**
   * Manage channel limits to prevent connection issues
   */
  private manageChannelLimits(newThreadId: string): void {
    if (this.channels.size >= this.maxChannels) {
      // Remove the oldest channel to make room
      const oldestThreadId = this.channelQueue.shift();
      if (oldestThreadId && this.channels.has(oldestThreadId)) {
        console.log(`📡 Removing oldest channel ${oldestThreadId} to make room for ${newThreadId}`);
        this.unsubscribeFromThread(oldestThreadId);
      }
    }
  }

  /**
   * Unsubscribe from a specific thread
   */
  private unsubscribeFromThread(threadId: string): void {
    try {
      const channel = this.channels.get(threadId);
      if (channel) {
        channel.unsubscribe();
        this.channels.delete(threadId);

        // Remove from queue
        const queueIndex = this.channelQueue.indexOf(threadId);
        if (queueIndex > -1) {
          this.channelQueue.splice(queueIndex, 1);
        }

        // Remove activity timestamp
        this.channelTimestamps.delete(threadId);

        console.log(`📡 Unsubscribed from chat thread ${threadId}`);
      }
    } catch (error) {
      console.error('❌ Error unsubscribing from chat thread:', error);
    }
  }

  /**
   * Send system message
   */
  private async sendSystemMessage(threadId: string, message: string): Promise<void> {
    try {
      await supabase
        .from('chat_messages')
        .insert({
          thread_id: threadId,
          sender_id: 'system',
          sender_name: 'System',
          message,
          message_type: 'system',
          read_by: [],
        });

    } catch (error) {
      console.error('❌ Failed to send system message:', error);
    }
  }

  /**
   * Notify thread participants of new message
   */
  private async notifyThreadParticipants(
    threadId: string,
    senderId: string,
    message: string
  ): Promise<void> {
    try {
      // Get thread participants (excluding sender)
      const { data: thread } = await supabase
        .from('chat_threads')
        .select('participants, title')
        .eq('id', threadId)
        .single();

      if (!thread) return;

      const otherParticipants = thread.participants.filter((id: string) => id !== senderId);

      if (otherParticipants.length === 0) return;

      // Send push notification
      await pushNotificationService.sendNotificationToStaff(otherParticipants, {
        type: 'staff_message',
        chatThreadId: threadId,
        title: `💬 ${thread.title}`,
        body: message.length > 50 ? `${message.substring(0, 50)}...` : message,
        data: {
          threadId,
          action: 'open_chat',
          screen: 'ChatThread',
        },
      });

    } catch (error) {
      console.error('❌ Failed to notify thread participants:', error);
    }
  }

  /**
   * Set up real-time chat subscriptions
   */
  private async setupChatSubscriptions(): Promise<void> {
    // This will be handled per-thread basis when users subscribe
    console.log('📡 Chat subscriptions ready for per-thread setup');
  }

  /**
   * Get connection pool health status
   */
  getConnectionPoolHealth(): any {
    return supabaseConnectionPool.getHealthStatus();
  }

  /**
   * Get connection pool statistics
   */
  getConnectionPoolStats(): any {
    return supabaseConnectionPool.getStats();
  }

  /**
   * Cleanup all active subscriptions
   */
  async cleanup(): Promise<void> {
    try {
      console.log('🧹 Cleaning up staff communication service...');

      // Stop idle cleanup interval
      if (this.idleCleanupInterval) {
        clearInterval(this.idleCleanupInterval);
        this.idleCleanupInterval = null;
        console.log('⏰ Idle cleanup interval stopped');
      }

      // Cleanup connection pool
      supabaseConnectionPool.cleanup();

      // Clear all data structures
      this.channels.clear();
      this.channelQueue.length = 0;
      this.channelTimestamps.clear();
      this.isInitialized = false;

      console.log('✅ Staff communication service cleanup completed');
    } catch (error) {
      console.error('❌ Staff communication service cleanup failed:', error);
    }
  }
}

export const staffCommunicationService = new StaffCommunicationService();