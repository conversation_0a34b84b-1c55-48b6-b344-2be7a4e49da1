/**
 * Ocean Soul Sparkles Mobile App - Square Payment Service
 * Mobile payment processing with Square SDK
 */

import { 
  SqIP<PERSON><PERSON>, 
  SqIPCardEntry, 
  SqIPApplePay,
  SqIPGooglePay 
} from 'react-native-square-in-app-payments';
import Constants from 'expo-constants';
import { Platform } from 'react-native';

// Environment variables
const SQUARE_APPLICATION_ID = Constants.expoConfig?.extra?.EXPO_PUBLIC_SQUARE_APPLICATION_ID || process.env.EXPO_PUBLIC_SQUARE_APPLICATION_ID;
const SQUARE_LOCATION_ID = Constants.expoConfig?.extra?.EXPO_PUBLIC_SQUARE_LOCATION_ID || process.env.EXPO_PUBLIC_SQUARE_LOCATION_ID;

if (!SQUARE_APPLICATION_ID || !SQUARE_LOCATION_ID) {
  throw new Error('Missing Square configuration. Please check your environment variables.');
}

export interface PaymentRequest {
  amount: number; // Amount in cents
  currency: string;
  description?: string;
  customerId?: string;
  orderId?: string;
}

export interface PaymentResult {
  success: boolean;
  nonce?: string;
  cardDetails?: any;
  error?: string;
}

export interface DigitalWalletResult {
  success: boolean;
  nonce?: string;
  error?: string;
}

export class SquarePaymentService {
  private isInitialized = false;

  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      await SqIPCore.setSquareApplicationId(SQUARE_APPLICATION_ID);
      this.isInitialized = true;
      console.log('Square SDK initialized successfully');
    } catch (error) {
      console.error('Failed to initialize Square SDK:', error);
      throw new Error('Square SDK initialization failed');
    }
  }

  // Card entry payment
  async processCardPayment(request: PaymentRequest): Promise<PaymentResult> {
    try {
      await this.initialize();

      const cardEntryConfig = {
        collectPostalCode: true,
        squareLocationId: SQUARE_LOCATION_ID,
      };

      const result = await SqIPCardEntry.startCardEntryFlow(
        cardEntryConfig,
        this.onCardNonceRequestSuccess.bind(this),
        this.onCardEntryCancel.bind(this)
      );

      return {
        success: true,
        nonce: result.nonce,
        cardDetails: result.cardDetails,
      };
    } catch (error) {
      console.error('Card payment failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Card payment failed',
      };
    }
  }

  // Apple Pay payment
  async processApplePayPayment(request: PaymentRequest): Promise<DigitalWalletResult> {
    if (Platform.OS !== 'ios') {
      return {
        success: false,
        error: 'Apple Pay is only available on iOS',
      };
    }

    try {
      await this.initialize();

      const isApplePayEnabled = await SqIPApplePay.canUseApplePay();
      if (!isApplePayEnabled) {
        return {
          success: false,
          error: 'Apple Pay is not available on this device',
        };
      }

      const applePayConfig = {
        price: (request.amount / 100).toFixed(2),
        summaryLabel: request.description || 'Ocean Soul Sparkles',
        countryCode: 'AU',
        currencyCode: request.currency,
        paymentType: SqIPApplePay.PaymentTypeFinal,
        squareLocationId: SQUARE_LOCATION_ID,
      };

      const result = await SqIPApplePay.requestApplePayNonce(
        applePayConfig,
        this.onApplePayNonceRequestSuccess.bind(this),
        this.onApplePayNonceRequestFailure.bind(this),
        this.onApplePayComplete.bind(this)
      );

      return {
        success: true,
        nonce: result.nonce,
      };
    } catch (error) {
      console.error('Apple Pay failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Apple Pay failed',
      };
    }
  }

  // Google Pay payment
  async processGooglePayPayment(request: PaymentRequest): Promise<DigitalWalletResult> {
    if (Platform.OS !== 'android') {
      return {
        success: false,
        error: 'Google Pay is only available on Android',
      };
    }

    try {
      await this.initialize();

      const isGooglePayEnabled = await SqIPGooglePay.canUseGooglePay();
      if (!isGooglePayEnabled) {
        return {
          success: false,
          error: 'Google Pay is not available on this device',
        };
      }

      const googlePayConfig = {
        price: (request.amount / 100).toFixed(2),
        currencyCode: request.currency,
        priceStatus: SqIPGooglePay.TotalPriceStatusFinal,
        squareLocationId: SQUARE_LOCATION_ID,
      };

      const result = await SqIPGooglePay.requestGooglePayNonce(
        googlePayConfig,
        this.onGooglePayNonceRequestSuccess.bind(this),
        this.onGooglePayNonceRequestFailure.bind(this),
        this.onGooglePayCanceled.bind(this)
      );

      return {
        success: true,
        nonce: result.nonce,
      };
    } catch (error) {
      console.error('Google Pay failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Google Pay failed',
      };
    }
  }

  // Check available payment methods
  async getAvailablePaymentMethods(): Promise<{
    cardEntry: boolean;
    applePay: boolean;
    googlePay: boolean;
  }> {
    try {
      await this.initialize();

      const [applePayAvailable, googlePayAvailable] = await Promise.all([
        Platform.OS === 'ios' ? SqIPApplePay.canUseApplePay() : Promise.resolve(false),
        Platform.OS === 'android' ? SqIPGooglePay.canUseGooglePay() : Promise.resolve(false),
      ]);

      return {
        cardEntry: true, // Always available
        applePay: applePayAvailable,
        googlePay: googlePayAvailable,
      };
    } catch (error) {
      console.error('Failed to check payment methods:', error);
      return {
        cardEntry: true,
        applePay: false,
        googlePay: false,
      };
    }
  }

  // Callback handlers
  private onCardNonceRequestSuccess(cardDetails: any) {
    console.log('Card nonce received:', cardDetails);
    return cardDetails;
  }

  private onCardEntryCancel() {
    console.log('Card entry cancelled');
    throw new Error('Payment cancelled by user');
  }

  private onApplePayNonceRequestSuccess(nonce: string) {
    console.log('Apple Pay nonce received:', nonce);
    return { nonce };
  }

  private onApplePayNonceRequestFailure(error: any) {
    console.error('Apple Pay failed:', error);
    throw new Error(error.message || 'Apple Pay failed');
  }

  private onApplePayComplete() {
    console.log('Apple Pay completed');
  }

  private onGooglePayNonceRequestSuccess(nonce: string) {
    console.log('Google Pay nonce received:', nonce);
    return { nonce };
  }

  private onGooglePayNonceRequestFailure(error: any) {
    console.error('Google Pay failed:', error);
    throw new Error(error.message || 'Google Pay failed');
  }

  private onGooglePayCanceled() {
    console.log('Google Pay cancelled');
    throw new Error('Payment cancelled by user');
  }

  // Process payment on server
  async processPaymentOnServer(nonce: string, request: PaymentRequest): Promise<{
    success: boolean;
    paymentId?: string;
    receiptUrl?: string;
    error?: string;
  }> {
    try {
      // This would call your admin portal API to process the payment
      const response = await fetch(`${process.env.EXPO_PUBLIC_API_BASE_URL}/payments/process`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          // Add authentication headers here
        },
        body: JSON.stringify({
          nonce,
          amount: request.amount,
          currency: request.currency,
          description: request.description,
          customerId: request.customerId,
          orderId: request.orderId,
          locationId: SQUARE_LOCATION_ID,
        }),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Payment processing failed');
      }

      return {
        success: true,
        paymentId: result.paymentId,
        receiptUrl: result.receiptUrl,
      };
    } catch (error) {
      console.error('Server payment processing failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Payment processing failed',
      };
    }
  }
}

// Export singleton instance
export const squarePayments = new SquarePaymentService();
