/**
 * Ocean Soul Sparkles Mobile App - Supabase Schema Checker
 * Connects to Supabase and checks actual table schemas
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

const SUPABASE_URL = process.env.EXPO_PUBLIC_SUPABASE_URL;
const SUPABASE_ANON_KEY = process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY;

console.log('🔍 Checking Supabase Database Schema...');
console.log('Supabase URL:', SUPABASE_URL);

if (!SUPABASE_URL || !SUPABASE_ANON_KEY) {
  console.error('❌ Missing Supabase configuration');
  process.exit(1);
}

const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

async function checkTableSchema(tableName) {
  console.log(`\n📋 Checking ${tableName} table schema...`);
  
  try {
    // Method 1: Try to get a sample record to see the structure
    const { data, error } = await supabase
      .from(tableName)
      .select('*')
      .limit(1);

    if (error) {
      console.error(`❌ Error accessing ${tableName}:`, error.message);
      return null;
    }

    if (data && data.length > 0) {
      console.log(`✅ ${tableName} table accessible with data`);
      console.log(`📊 ${tableName} columns:`, Object.keys(data[0]));
      
      // Show detailed structure
      const record = data[0];
      console.log(`\n📝 ${tableName} structure:`);
      Object.entries(record).forEach(([key, value]) => {
        console.log(`  ${key}: ${typeof value} = ${value}`);
      });
      
      return Object.keys(record);
    } else {
      console.log(`✅ ${tableName} table accessible but empty`);
      
      // Try to get schema from information_schema if available
      try {
        const { data: schemaData, error: schemaError } = await supabase
          .rpc('get_table_columns', { table_name: tableName });
        
        if (!schemaError && schemaData) {
          console.log(`📊 ${tableName} schema from information_schema:`, schemaData);
          return schemaData;
        }
      } catch (err) {
        console.log(`ℹ️ Could not get schema info for ${tableName}`);
      }
      
      return [];
    }
  } catch (error) {
    console.error(`❌ Error checking ${tableName}:`, error.message);
    return null;
  }
}

async function testQuoteOperations() {
  console.log('\n💰 Testing quote operations...');
  
  // Test different property names for quotes
  const testProperties = [
    { name: 'total_amount', value: 100.00 },
    { name: 'amount', value: 100.00 },
    { name: 'quote_amount', value: 100.00 },
    { name: 'price', value: 100.00 },
    { name: 'total', value: 100.00 }
  ];
  
  for (const prop of testProperties) {
    try {
      console.log(`\n🧪 Testing ${prop.name} property...`);
      
      const testQuote = {
        customer_id: '00000000-0000-0000-0000-000000000000', // Dummy UUID
        service_name: `Test Quote - ${prop.name}`,
        service_description: `Testing ${prop.name} property`,
        [prop.name]: prop.value,
        status: 'draft'
      };
      
      const { data, error } = await supabase
        .from('quotes')
        .insert([testQuote])
        .select('*');
      
      if (error) {
        console.log(`❌ ${prop.name} failed:`, error.message);
      } else {
        console.log(`✅ ${prop.name} works!`);
        console.log(`📊 Created quote structure:`, Object.keys(data[0]));
        
        // Clean up test data
        await supabase.from('quotes').delete().eq('id', data[0].id);
        
        // This property works, so we found the correct one
        return prop.name;
      }
    } catch (err) {
      console.log(`❌ ${prop.name} test failed:`, err.message);
    }
  }
  
  return null;
}

async function checkAllTables() {
  const tables = ['quotes', 'customers', 'bookings', 'services', 'admin_users'];
  const schemas = {};
  
  for (const table of tables) {
    schemas[table] = await checkTableSchema(table);
  }
  
  return schemas;
}

async function main() {
  console.log('🚨 Ocean Soul Sparkles - Supabase Schema Check');
  console.log('='.repeat(60));
  
  try {
    // Check all table schemas
    const schemas = await checkAllTables();
    
    // Test quote operations specifically
    const correctQuoteProperty = await testQuoteOperations();
    
    console.log('\n📊 SCHEMA SUMMARY:');
    console.log('='.repeat(40));
    
    Object.entries(schemas).forEach(([table, columns]) => {
      if (columns && columns.length > 0) {
        console.log(`\n${table.toUpperCase()}:`);
        columns.forEach(col => console.log(`  - ${col}`));
      } else {
        console.log(`\n${table.toUpperCase()}: Not accessible or empty`);
      }
    });
    
    if (correctQuoteProperty) {
      console.log(`\n💰 CORRECT QUOTE AMOUNT PROPERTY: ${correctQuoteProperty}`);
    } else {
      console.log('\n❌ Could not determine correct quote amount property');
    }
    
    // Check if quotes table has the properties our code expects
    if (schemas.quotes) {
      const quoteColumns = schemas.quotes;
      const expectedProps = ['total_amount', 'amount', 'quote_amount', 'price'];
      const foundProps = expectedProps.filter(prop => quoteColumns.includes(prop));
      
      console.log('\n🔍 QUOTE AMOUNT PROPERTIES ANALYSIS:');
      console.log('Expected properties:', expectedProps);
      console.log('Found properties:', foundProps);
      
      if (foundProps.length === 0) {
        console.log('❌ CRITICAL: No amount properties found in quotes table!');
        console.log('Available columns:', quoteColumns);
      } else {
        console.log(`✅ Found ${foundProps.length} amount property(ies):`, foundProps);
      }
    }
    
  } catch (error) {
    console.error('❌ Schema check failed:', error);
  }
}

main().catch(console.error);
