/**
 * Ocean Soul Sparkles Mobile App - Invoice Generation Workflow Service
 * Orchestrates complete invoice generation workflows using SQL functions
 */

import { sqlFunctionsService, InvoiceGenerationResult } from '@/services/database/sqlFunctionsService';
import { supabase } from '@/services/database/supabase';
import { emailService } from '@/services/email/emailService';
import { Quote, Invoice, Customer, DatabaseResponse } from '@/types/database';

export interface InvoiceWorkflowResult {
  success: boolean;
  invoice?: Invoice;
  invoice_number?: string;
  total_amount?: number;
  email_sent?: boolean;
  steps: InvoiceWorkflowStep[];
  error?: string;
}

export interface InvoiceWorkflowStep {
  step: string;
  status: 'pending' | 'in_progress' | 'completed' | 'failed';
  message: string;
  duration?: number;
  error?: string;
}

export interface InvoiceGenerationConfig {
  auto_send_email: boolean;
  email_template: string;
  due_days: number;
  include_payment_link: boolean;
  send_copy_to_staff: boolean;
}

export class InvoiceWorkflowService {
  private static instance: InvoiceWorkflowService;

  private readonly DEFAULT_CONFIG: InvoiceGenerationConfig = {
    auto_send_email: true,
    email_template: 'invoice',
    due_days: 30,
    include_payment_link: false,
    send_copy_to_staff: true,
  };

  private constructor() {}

  public static getInstance(): InvoiceWorkflowService {
    if (!InvoiceWorkflowService.instance) {
      InvoiceWorkflowService.instance = new InvoiceWorkflowService();
    }
    return InvoiceWorkflowService.instance;
  }

  /**
   * Generate invoice from quote with complete workflow
   */
  async generateInvoiceFromQuote(
    quoteId: string,
    config: Partial<InvoiceGenerationConfig> = {}
  ): Promise<InvoiceWorkflowResult> {
    const finalConfig = { ...this.DEFAULT_CONFIG, ...config };
    const steps: InvoiceWorkflowStep[] = [];
    
    try {
      console.log('🧾 Starting invoice generation workflow for quote:', quoteId);

      // Step 1: Validate quote
      steps.push(this.createStep('validate_quote', 'in_progress', 'Validating quote...'));
      
      const quote = await this.validateQuote(quoteId);
      if (!quote) {
        steps[steps.length - 1] = this.updateStep(steps[steps.length - 1], 'failed', 'Quote not found or invalid');
        return { success: false, steps, error: 'Quote not found or invalid' };
      }

      steps[steps.length - 1] = this.updateStep(steps[steps.length - 1], 'completed', `Quote validated: ${quote.service_name}`);

      // Step 2: Generate invoice using SQL function
      steps.push(this.createStep('generate_invoice', 'in_progress', 'Generating invoice...'));
      
      const invoiceResult = await sqlFunctionsService.createInvoiceFromQuote(quoteId);
      if (!invoiceResult.success) {
        steps[steps.length - 1] = this.updateStep(steps[steps.length - 1], 'failed', invoiceResult.error || 'Invoice generation failed');
        return { success: false, steps, error: invoiceResult.error };
      }

      steps[steps.length - 1] = this.updateStep(steps[steps.length - 1], 'completed', `Invoice generated: ${invoiceResult.invoice_number}`);

      // Step 3: Retrieve generated invoice
      steps.push(this.createStep('retrieve_invoice', 'in_progress', 'Retrieving invoice details...'));
      
      const invoice = await this.getInvoiceById(invoiceResult.invoice_id!);
      if (!invoice) {
        steps[steps.length - 1] = this.updateStep(steps[steps.length - 1], 'failed', 'Failed to retrieve generated invoice');
        return { success: false, steps, error: 'Failed to retrieve generated invoice' };
      }

      steps[steps.length - 1] = this.updateStep(steps[steps.length - 1], 'completed', 'Invoice details retrieved');

      // Step 4: Update quote status
      steps.push(this.createStep('update_quote', 'in_progress', 'Updating quote status...'));
      
      const quoteUpdateResult = await this.updateQuoteStatus(quoteId, 'accepted');
      if (!quoteUpdateResult.success) {
        console.warn('Quote status update failed, but continuing with invoice workflow');
        steps[steps.length - 1] = this.updateStep(steps[steps.length - 1], 'completed', 'Quote status update skipped');
      } else {
        steps[steps.length - 1] = this.updateStep(steps[steps.length - 1], 'completed', 'Quote status updated to accepted');
      }

      // Step 5: Send email notification (if configured)
      let emailSent = false;
      if (finalConfig.auto_send_email) {
        steps.push(this.createStep('send_email', 'in_progress', 'Sending invoice email...'));
        
        const emailResult = await this.sendInvoiceEmail(invoice, quote.customer_id);
        emailSent = emailResult.success;
        
        if (emailResult.success) {
          steps[steps.length - 1] = this.updateStep(steps[steps.length - 1], 'completed', 'Invoice email sent successfully');
        } else {
          steps[steps.length - 1] = this.updateStep(steps[steps.length - 1], 'failed', emailResult.error || 'Email sending failed');
        }
      }

      console.log(`✅ Invoice generation workflow completed: ${invoiceResult.invoice_number}`);

      return {
        success: true,
        invoice,
        invoice_number: invoiceResult.invoice_number,
        total_amount: invoiceResult.total_amount,
        email_sent: emailSent,
        steps,
      };

    } catch (error) {
      console.error('❌ Invoice generation workflow failed:', error);
      
      // Update current step as failed
      if (steps.length > 0) {
        steps[steps.length - 1] = this.updateStep(
          steps[steps.length - 1], 
          'failed', 
          error instanceof Error ? error.message : 'Unknown error'
        );
      }

      return {
        success: false,
        steps,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Update invoice status with workflow
   */
  async updateInvoiceStatus(
    invoiceId: string,
    newStatus: 'draft' | 'sent' | 'paid' | 'overdue' | 'cancelled',
    sendNotification: boolean = true
  ): Promise<InvoiceWorkflowResult> {
    const steps: InvoiceWorkflowStep[] = [];

    try {
      console.log(`📝 Updating invoice ${invoiceId} status to:`, newStatus);

      // Step 1: Update status using SQL function
      steps.push(this.createStep('update_status', 'in_progress', `Updating status to ${newStatus}...`));
      
      const updateResult = await sqlFunctionsService.updateInvoiceStatus(invoiceId, newStatus);
      if (!updateResult.success) {
        steps[steps.length - 1] = this.updateStep(steps[steps.length - 1], 'failed', updateResult.error || 'Status update failed');
        return { success: false, steps, error: updateResult.error };
      }

      steps[steps.length - 1] = this.updateStep(steps[steps.length - 1], 'completed', `Status updated to ${newStatus}`);

      // Step 2: Retrieve updated invoice
      steps.push(this.createStep('retrieve_invoice', 'in_progress', 'Retrieving updated invoice...'));
      
      const invoice = await this.getInvoiceById(invoiceId);
      if (!invoice) {
        steps[steps.length - 1] = this.updateStep(steps[steps.length - 1], 'failed', 'Failed to retrieve updated invoice');
        return { success: false, steps, error: 'Failed to retrieve updated invoice' };
      }

      steps[steps.length - 1] = this.updateStep(steps[steps.length - 1], 'completed', 'Updated invoice retrieved');

      // Step 3: Send notification (if configured and status requires it)
      let emailSent = false;
      if (sendNotification && (newStatus === 'sent' || newStatus === 'overdue')) {
        steps.push(this.createStep('send_notification', 'in_progress', 'Sending status notification...'));
        
        const emailResult = await this.sendInvoiceStatusEmail(invoice, newStatus);
        emailSent = emailResult.success;
        
        if (emailResult.success) {
          steps[steps.length - 1] = this.updateStep(steps[steps.length - 1], 'completed', 'Status notification sent');
        } else {
          steps[steps.length - 1] = this.updateStep(steps[steps.length - 1], 'failed', emailResult.error || 'Notification failed');
        }
      }

      return {
        success: true,
        invoice,
        invoice_number: invoice.invoice_number,
        total_amount: invoice.total_amount,
        email_sent: emailSent,
        steps,
      };

    } catch (error) {
      console.error('❌ Invoice status update workflow failed:', error);
      
      if (steps.length > 0) {
        steps[steps.length - 1] = this.updateStep(
          steps[steps.length - 1], 
          'failed', 
          error instanceof Error ? error.message : 'Unknown error'
        );
      }

      return {
        success: false,
        steps,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Calculate invoice total using SQL function
   */
  async calculateInvoiceTotal(invoiceId: string): Promise<{ success: boolean; total?: number; error?: string }> {
    try {
      const result = await sqlFunctionsService.calculateInvoiceTotal(invoiceId);
      
      if (!result.success) {
        return { success: false, error: result.error };
      }

      return { success: true, total: result.data };

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Validate quote before invoice generation
   */
  private async validateQuote(quoteId: string): Promise<Quote | null> {
    try {
      const { data, error } = await supabase
        .from('quotes')
        .select('*')
        .eq('id', quoteId)
        .single();

      if (error || !data) {
        console.error('Quote validation failed:', error);
        return null;
      }

      // Check if quote is in valid state for invoice generation
      if (data.status === 'cancelled' || data.status === 'expired') {
        console.error('Quote is not in valid state for invoice generation:', data.status);
        return null;
      }

      return data;

    } catch (error) {
      console.error('Quote validation error:', error);
      return null;
    }
  }

  /**
   * Get invoice by ID
   */
  private async getInvoiceById(invoiceId: string): Promise<Invoice | null> {
    try {
      const { data, error } = await supabase
        .from('invoices')
        .select('*')
        .eq('id', invoiceId)
        .single();

      if (error || !data) {
        console.error('Invoice retrieval failed:', error);
        return null;
      }

      return data;

    } catch (error) {
      console.error('Invoice retrieval error:', error);
      return null;
    }
  }

  /**
   * Update quote status
   */
  private async updateQuoteStatus(quoteId: string, status: string): Promise<{ success: boolean; error?: string }> {
    try {
      const { error } = await supabase
        .from('quotes')
        .update({ 
          status,
          updated_at: new Date().toISOString(),
        })
        .eq('id', quoteId);

      if (error) {
        return { success: false, error: error.message };
      }

      return { success: true };

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Send invoice email
   */
  private async sendInvoiceEmail(invoice: Invoice, customerId: string): Promise<{ success: boolean; error?: string }> {
    try {
      // Get customer details
      const { data: customer, error: customerError } = await supabase
        .from('customers')
        .select('*')
        .eq('id', customerId)
        .single();

      if (customerError || !customer) {
        return { success: false, error: 'Customer not found' };
      }

      // Send email using email service
      const emailResult = await emailService.sendEmail({
        to: customer.email,
        subject: `Invoice ${invoice.invoice_number} - Ocean Soul Sparkles`,
        template: 'invoice',
        variables: {
          customer_name: customer.full_name,
          invoice_number: invoice.invoice_number,
          total_amount: invoice.total_amount.toFixed(2),
          due_date: invoice.due_date || 'Upon receipt',
        },
      });

      return { success: emailResult.success, error: emailResult.error };

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Send invoice status notification email
   */
  private async sendInvoiceStatusEmail(
    invoice: Invoice, 
    status: string
  ): Promise<{ success: boolean; error?: string }> {
    try {
      // Get customer details
      const { data: customer, error: customerError } = await supabase
        .from('customers')
        .select('*')
        .eq('id', invoice.customer_id)
        .single();

      if (customerError || !customer) {
        return { success: false, error: 'Customer not found' };
      }

      const subject = status === 'overdue' 
        ? `Overdue Invoice ${invoice.invoice_number} - Ocean Soul Sparkles`
        : `Invoice ${invoice.invoice_number} Status Update - Ocean Soul Sparkles`;

      // Send status notification email
      const emailResult = await emailService.sendEmail({
        to: customer.email,
        subject,
        template: 'invoice_status',
        variables: {
          customer_name: customer.full_name,
          invoice_number: invoice.invoice_number,
          status: status.toUpperCase(),
          total_amount: invoice.total_amount.toFixed(2),
        },
      });

      return { success: emailResult.success, error: emailResult.error };

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Create workflow step
   */
  private createStep(step: string, status: InvoiceWorkflowStep['status'], message: string): InvoiceWorkflowStep {
    return { step, status, message };
  }

  /**
   * Update workflow step
   */
  private updateStep(
    step: InvoiceWorkflowStep, 
    status: InvoiceWorkflowStep['status'], 
    message: string,
    error?: string
  ): InvoiceWorkflowStep {
    return { ...step, status, message, error };
  }
}

// Export singleton instance
export const invoiceWorkflowService = InvoiceWorkflowService.getInstance();
