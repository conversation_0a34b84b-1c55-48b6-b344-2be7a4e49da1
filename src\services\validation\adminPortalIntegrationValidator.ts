/**
 * Ocean Soul Sparkles Mobile App - Admin Portal Integration Validation System
 * Comprehensive validation of admin portal integration, API connectivity, and system compatibility
 */

import { adminPortalAPIService, AdminPortalHealthCheck } from '@/services/adminPortal/adminPortalAPIService';
import { realTimeDataSyncService } from '@/services/adminPortal/realTimeDataSyncService';
import { crudCompatibilityValidator, EntityCompatibilityReport } from './crudCompatibilityValidator';
import { crossPlatformDataIntegrityValidator, DataIntegrityReport } from './crossPlatformDataIntegrityValidator';

export interface AdminPortalValidationResult {
  test: string;
  status: 'pass' | 'fail' | 'warning';
  message: string;
  duration: number;
  details?: any;
  error?: string;
}

export interface AdminPortalIntegrationStatus {
  apiConnectivity: boolean;
  authentication: boolean;
  crudCompatibility: boolean;
  dataIntegrity: boolean;
  realTimeSync: boolean;
  emailIntegration: boolean;
  overall: boolean;
}

export interface AdminPortalValidationSuite {
  connectivityTests: AdminPortalValidationResult[];
  authenticationTests: AdminPortalValidationResult[];
  crudTests: AdminPortalValidationResult[];
  dataIntegrityTests: AdminPortalValidationResult[];
  syncTests: AdminPortalValidationResult[];
  emailTests: AdminPortalValidationResult[];
  summary: {
    totalTests: number;
    passedTests: number;
    failedTests: number;
    warningTests: number;
    successRate: number;
    totalDuration: number;
  };
}

export class AdminPortalIntegrationValidator {
  private static instance: AdminPortalIntegrationValidator;

  // Performance thresholds
  private readonly PERFORMANCE_THRESHOLDS = {
    apiResponseTime: 3000,    // 3 seconds
    healthCheckTime: 2000,    // 2 seconds
    syncLatency: 5000,        // 5 seconds
    emailDeliveryTime: 10000, // 10 seconds
  };

  private constructor() {}

  public static getInstance(): AdminPortalIntegrationValidator {
    if (!AdminPortalIntegrationValidator.instance) {
      AdminPortalIntegrationValidator.instance = new AdminPortalIntegrationValidator();
    }
    return AdminPortalIntegrationValidator.instance;
  }

  /**
   * Test admin portal API connectivity
   */
  async testAPIConnectivity(): Promise<AdminPortalValidationResult[]> {
    const results: AdminPortalValidationResult[] = [];

    // Test health endpoint
    const healthStartTime = Date.now();
    try {
      console.log('🔗 Testing admin portal health...');

      const healthResult = await adminPortalAPIService.testHealth();
      const duration = Date.now() - healthStartTime;

      if (healthResult.success && healthResult.data) {
        const health = healthResult.data;
        const status = health.status === 'healthy' ? 'pass' : 
                      health.status === 'degraded' ? 'warning' : 'fail';

        results.push({
          test: 'Admin Portal Health Check',
          status,
          message: `Portal status: ${health.status} (${duration}ms)`,
          duration,
          details: {
            status: health.status,
            version: health.version,
            services: health.services,
            responseTime: health.responseTime,
          },
        });
      } else {
        results.push({
          test: 'Admin Portal Health Check',
          status: 'fail',
          message: 'Health check failed',
          duration,
          error: healthResult.error,
        });
      }

    } catch (error) {
      results.push({
        test: 'Admin Portal Health Check',
        status: 'fail',
        message: 'Health check error',
        duration: Date.now() - healthStartTime,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }

    // Test API endpoints
    const endpoints = adminPortalAPIService.getAvailableEndpoints();
    const criticalEndpoints = endpoints.filter(ep => ep.critical);

    for (const endpoint of criticalEndpoints.slice(0, 5)) { // Test first 5 critical endpoints
      const endpointStartTime = Date.now();
      try {
        const testResult = await adminPortalAPIService.testEndpoint(endpoint);
        const status = testResult.success ? 'pass' : 'fail';

        results.push({
          test: `API Endpoint: ${endpoint.method} ${endpoint.path}`,
          status,
          message: testResult.success ? 
            `Endpoint accessible (${testResult.responseTime}ms)` : 
            testResult.error || 'Endpoint failed',
          duration: testResult.responseTime,
          details: {
            method: endpoint.method,
            path: endpoint.path,
            authenticated: endpoint.authenticated,
            status: testResult.status,
          },
          error: testResult.success ? undefined : testResult.error,
        });

      } catch (error) {
        results.push({
          test: `API Endpoint: ${endpoint.method} ${endpoint.path}`,
          status: 'fail',
          message: 'Endpoint test failed',
          duration: Date.now() - endpointStartTime,
          error: error instanceof Error ? error.message : 'Unknown error',
        });
      }
    }

    return results;
  }

  /**
   * Test authentication integration
   */
  async testAuthentication(): Promise<AdminPortalValidationResult[]> {
    const results: AdminPortalValidationResult[] = [];

    // Test token validation
    const tokenStartTime = Date.now();
    try {
      console.log('🔐 Testing authentication integration...');

      const tokenResult = await adminPortalAPIService.validateToken();
      const duration = Date.now() - tokenStartTime;

      results.push({
        test: 'Token Validation',
        status: tokenResult.success ? 'pass' : 'fail',
        message: tokenResult.success ? 
          'Token validation successful' : 
          tokenResult.error || 'Token validation failed',
        duration,
        details: tokenResult.success ? tokenResult.data : undefined,
        error: tokenResult.success ? undefined : tokenResult.error,
      });

    } catch (error) {
      results.push({
        test: 'Token Validation',
        status: 'fail',
        message: 'Token validation error',
        duration: Date.now() - tokenStartTime,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }

    // Test token refresh
    const refreshStartTime = Date.now();
    try {
      const refreshResult = await adminPortalAPIService.refreshToken();
      const duration = Date.now() - refreshStartTime;

      results.push({
        test: 'Token Refresh',
        status: refreshResult.success ? 'pass' : 'warning',
        message: refreshResult.success ? 
          'Token refresh successful' : 
          'Token refresh not available',
        duration,
        details: refreshResult.success ? { hasNewToken: !!refreshResult.data?.token } : undefined,
        error: refreshResult.success ? undefined : refreshResult.error,
      });

    } catch (error) {
      results.push({
        test: 'Token Refresh',
        status: 'warning',
        message: 'Token refresh test failed',
        duration: Date.now() - refreshStartTime,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }

    return results;
  }

  /**
   * Test CRUD compatibility
   */
  async testCRUDCompatibility(): Promise<AdminPortalValidationResult[]> {
    const results: AdminPortalValidationResult[] = [];

    try {
      console.log('🔄 Testing CRUD compatibility...');

      const startTime = Date.now();
      const crudReports = await crudCompatibilityValidator.runComprehensiveCRUDValidation();
      const duration = Date.now() - startTime;

      for (const report of crudReports) {
        const status = report.overallCompatibility >= 90 ? 'pass' : 
                      report.overallCompatibility >= 75 ? 'warning' : 'fail';

        results.push({
          test: `CRUD Compatibility: ${report.entity}`,
          status,
          message: `${report.overallCompatibility.toFixed(1)}% compatibility`,
          duration: duration / crudReports.length,
          details: {
            compatibility: report.overallCompatibility,
            operations: report.operations.length,
            issues: report.issues.length,
            recommendations: report.recommendations,
          },
        });
      }

    } catch (error) {
      results.push({
        test: 'CRUD Compatibility',
        status: 'fail',
        message: 'CRUD compatibility test failed',
        duration: 0,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }

    return results;
  }

  /**
   * Test data integrity
   */
  async testDataIntegrity(): Promise<AdminPortalValidationResult[]> {
    const results: AdminPortalValidationResult[] = [];

    try {
      console.log('🔍 Testing data integrity...');

      const startTime = Date.now();
      const integrityReport = await crossPlatformDataIntegrityValidator.generateDataIntegrityReport();
      const duration = Date.now() - startTime;

      const status = integrityReport.overallConsistency >= 95 ? 'pass' : 
                    integrityReport.overallConsistency >= 80 ? 'warning' : 'fail';

      results.push({
        test: 'Cross-Platform Data Integrity',
        status,
        message: `${integrityReport.overallConsistency.toFixed(1)}% data consistency`,
        duration,
        details: {
          consistency: integrityReport.overallConsistency,
          tables: integrityReport.tables.length,
          criticalIssues: integrityReport.criticalIssues.length,
          summary: integrityReport.summary,
        },
      });

      // Individual table integrity tests
      for (const table of integrityReport.tables) {
        const tableStatus = table.consistent ? 'pass' : 'warning';
        
        results.push({
          test: `Data Integrity: ${table.table}`,
          status: tableStatus,
          message: table.consistent ? 
            `${table.table} data consistent` : 
            `${table.issues.length} issues found`,
          duration: 0,
          details: {
            mobileCount: table.mobileCount,
            adminCount: table.adminCount,
            discrepancy: table.discrepancy,
            issues: table.issues,
          },
        });
      }

    } catch (error) {
      results.push({
        test: 'Data Integrity',
        status: 'fail',
        message: 'Data integrity test failed',
        duration: 0,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }

    return results;
  }

  /**
   * Test real-time synchronization
   */
  async testRealTimeSync(): Promise<AdminPortalValidationResult[]> {
    const results: AdminPortalValidationResult[] = [];

    // Test real-time connectivity
    const connectivityStartTime = Date.now();
    try {
      console.log('📡 Testing real-time synchronization...');

      const connectivityResult = await realTimeDataSyncService.testRealTimeConnectivity();
      const duration = Date.now() - connectivityStartTime;

      const status = connectivityResult.connected ? 'pass' : 'fail';

      results.push({
        test: 'Real-Time Connectivity',
        status,
        message: connectivityResult.connected ? 
          `Connected with ${connectivityResult.latency}ms latency` : 
          'Real-time connection failed',
        duration,
        details: {
          connected: connectivityResult.connected,
          latency: connectivityResult.latency,
          channels: connectivityResult.channels,
        },
      });

    } catch (error) {
      results.push({
        test: 'Real-Time Connectivity',
        status: 'fail',
        message: 'Real-time connectivity test failed',
        duration: Date.now() - connectivityStartTime,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }

    // Test sync status
    const syncStartTime = Date.now();
    try {
      const syncStatus = realTimeDataSyncService.getSyncStatus();
      const duration = Date.now() - syncStartTime;

      const syncedTables = syncStatus.filter(s => s.status === 'synced').length;
      const status = syncedTables === syncStatus.length ? 'pass' : 
                    syncedTables >= syncStatus.length * 0.8 ? 'warning' : 'fail';

      results.push({
        test: 'Data Synchronization Status',
        status,
        message: `${syncedTables}/${syncStatus.length} tables synchronized`,
        duration,
        details: {
          totalTables: syncStatus.length,
          syncedTables,
          syncStatus: syncStatus.map(s => ({ table: s.table, status: s.status })),
        },
      });

    } catch (error) {
      results.push({
        test: 'Data Synchronization Status',
        status: 'fail',
        message: 'Sync status test failed',
        duration: Date.now() - syncStartTime,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }

    return results;
  }

  /**
   * Test email integration
   */
  async testEmailIntegration(): Promise<AdminPortalValidationResult[]> {
    const results: AdminPortalValidationResult[] = [];

    // Test email sending capability
    const emailStartTime = Date.now();
    try {
      console.log('📧 Testing email integration...');

      // Test email sending (dry run)
      const emailResult = await adminPortalAPIService.sendEmail({
        to: '<EMAIL>',
        subject: 'Integration Test',
        template: 'test',
        variables: { test: true },
      });

      const duration = Date.now() - emailStartTime;
      const status = emailResult.success ? 'pass' : 'fail';

      results.push({
        test: 'Email Sending Integration',
        status,
        message: emailResult.success ? 
          'Email integration functional' : 
          emailResult.error || 'Email sending failed',
        duration,
        details: emailResult.success ? emailResult.data : undefined,
        error: emailResult.success ? undefined : emailResult.error,
      });

    } catch (error) {
      results.push({
        test: 'Email Sending Integration',
        status: 'fail',
        message: 'Email integration test failed',
        duration: Date.now() - emailStartTime,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }

    return results;
  }

  /**
   * Get admin portal integration status
   */
  async getAdminPortalIntegrationStatus(): Promise<AdminPortalIntegrationStatus> {
    try {
      const [
        connectivityResults,
        authResults,
        crudResults,
        integrityResults,
        syncResults,
        emailResults,
      ] = await Promise.all([
        this.testAPIConnectivity(),
        this.testAuthentication(),
        this.testCRUDCompatibility(),
        this.testDataIntegrity(),
        this.testRealTimeSync(),
        this.testEmailIntegration(),
      ]);

      const status = {
        apiConnectivity: connectivityResults.every(r => r.status === 'pass'),
        authentication: authResults.filter(r => r.status === 'pass').length >= authResults.length * 0.5,
        crudCompatibility: crudResults.every(r => r.status !== 'fail'),
        dataIntegrity: integrityResults.every(r => r.status !== 'fail'),
        realTimeSync: syncResults.every(r => r.status !== 'fail'),
        emailIntegration: emailResults.every(r => r.status !== 'fail'),
        overall: false,
      };

      // Overall status is true if critical components are working
      status.overall = status.apiConnectivity && 
                      status.authentication && 
                      status.crudCompatibility && 
                      status.dataIntegrity;

      return status;

    } catch (error) {
      console.error('❌ Failed to get admin portal integration status:', error);
      return {
        apiConnectivity: false,
        authentication: false,
        crudCompatibility: false,
        dataIntegrity: false,
        realTimeSync: false,
        emailIntegration: false,
        overall: false,
      };
    }
  }

  /**
   * Run comprehensive admin portal integration validation
   */
  async runCompleteAdminPortalValidation(): Promise<AdminPortalValidationSuite> {
    console.log('🧪 Running comprehensive admin portal integration validation...');

    const startTime = Date.now();

    const connectivityTests = await this.testAPIConnectivity();
    const authenticationTests = await this.testAuthentication();
    const crudTests = await this.testCRUDCompatibility();
    const dataIntegrityTests = await this.testDataIntegrity();
    const syncTests = await this.testRealTimeSync();
    const emailTests = await this.testEmailIntegration();

    const allTests = [
      ...connectivityTests,
      ...authenticationTests,
      ...crudTests,
      ...dataIntegrityTests,
      ...syncTests,
      ...emailTests,
    ];

    const passedTests = allTests.filter(t => t.status === 'pass').length;
    const failedTests = allTests.filter(t => t.status === 'fail').length;
    const warningTests = allTests.filter(t => t.status === 'warning').length;
    const successRate = allTests.length > 0 ? (passedTests / allTests.length) * 100 : 0;
    const totalDuration = Date.now() - startTime;

    const summary = {
      totalTests: allTests.length,
      passedTests,
      failedTests,
      warningTests,
      successRate: Math.round(successRate * 100) / 100,
      totalDuration,
    };

    console.log(`✅ Admin portal validation completed: ${passedTests}/${allTests.length} tests passed (${summary.successRate}%)`);

    return {
      connectivityTests,
      authenticationTests,
      crudTests,
      dataIntegrityTests,
      syncTests,
      emailTests,
      summary,
    };
  }
}

// Export singleton instance
export const adminPortalIntegrationValidator = AdminPortalIntegrationValidator.getInstance();
