import { ConfigContext, ExpoConfig } from 'expo/config';

export default ({ config }: ConfigContext): ExpoConfig => ({
  ...config,
  name: 'Ocean Soul Sparkles',
  slug: 'oceansoulapp',
  owner: 'connectplusapps',
  version: '1.0.0',
  orientation: 'portrait',
  userInterfaceStyle: 'automatic',
  splash: {
    resizeMode: 'contain',
    backgroundColor: '#FF9A8B'
  },
  assetBundlePatterns: [
    '**/*'
  ],
  ios: {
    supportsTablet: true,
    bundleIdentifier: 'com.oceansoulsparkles.app',
    buildNumber: '1',
    infoPlist: {
      NSCameraUsageDescription: 'This app uses the camera to scan QR codes and take photos for products.',
      NSMicrophoneUsageDescription: 'This app uses the microphone for voice notes and customer communication.',
      NSLocationWhenInUseUsageDescription: 'This app uses location to provide location-based services and delivery tracking.',
      ITSAppUsesNonExemptEncryption: false
    }
  },
  android: {
    adaptiveIcon: {
      backgroundColor: '#FF9A8B'
    },
    package: 'com.oceansoulsparkles.app',
    permissions: [
      'CAMERA',
      'RECORD_AUDIO',
      'ACCESS_FINE_LOCATION',
      'ACCESS_COARSE_LOCATION',
      'INTERNET',
      'ACCESS_NETWORK_STATE',
      'WRITE_EXTERNAL_STORAGE',
      'READ_EXTERNAL_STORAGE',
      'android.permission.POST_NOTIFICATIONS'
    ]
  },
  web: {
    bundler: 'metro'
  },
  plugins: [
    [
      'expo-splash-screen',
      {
        backgroundColor: '#FF9A8B'
      }
    ]
  ],
  extra: {
    eas: {
      projectId: 'ba00e89d-a467-4a76-8272-a8ae6c364fd8'
    }
  }
});











