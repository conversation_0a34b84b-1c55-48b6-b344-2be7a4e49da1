/**
 * Ocean Soul Sparkles Mobile App - Production Readiness Validator
 * Master orchestrator for comprehensive production deployment validation
 */

import { integrationValidator } from '@/utils/integrationValidation';
import { adminPortalIntegrationSuite, AdminPortalIntegrationReport } from './adminPortalIntegrationSuite';
import { databaseIntegrationSuite, DatabaseIntegrationReport } from './databaseIntegrationSuite';
import { emailTesting, EmailSystemStatus } from '@/utils/emailTesting';
import { workflowTesting, WorkflowTestSuite } from '@/utils/workflowTesting';
import { finalIntegrationTester, FinalIntegrationResult } from '@/utils/finalIntegrationTest';

export interface ProductionReadinessReport {
  timestamp: string;
  overall: 'ready' | 'not_ready' | 'warning';
  readinessScore: number; // 0-100
  summary: {
    totalSystems: number;
    readySystems: number;
    criticalIssues: number;
    warnings: number;
    duration: number;
  };
  systemReports: {
    enhancedWorkflow: any;
    emailNotifications: EmailSystemStatus;
    adminPortalIntegration: AdminPortalIntegrationReport;
    databaseIntegration: DatabaseIntegrationReport;
    finalIntegration: FinalIntegrationResult;
  };
  deploymentChecklist: DeploymentChecklistItem[];
  criticalBlockers: string[];
  recommendations: string[];
  nextSteps: string[];
}

export interface DeploymentChecklistItem {
  category: 'security' | 'performance' | 'functionality' | 'integration' | 'monitoring';
  item: string;
  status: 'pass' | 'fail' | 'warning' | 'not_tested';
  priority: 'critical' | 'high' | 'medium' | 'low';
  description: string;
  details?: any;
}

export interface ProductionReadinessStatus {
  enhancedWorkflow: boolean;
  emailSystem: boolean;
  adminPortal: boolean;
  database: boolean;
  overall: boolean;
  readinessPercentage: number;
}

export class ProductionReadinessValidator {
  private static instance: ProductionReadinessValidator;

  // Production readiness criteria weights
  private readonly SYSTEM_WEIGHTS = {
    enhancedWorkflow: 25,    // 25% - Core booking functionality
    emailSystem: 20,         // 20% - Communication system
    adminPortal: 25,         // 25% - Admin integration
    database: 30,            // 30% - Data foundation
  };

  // Critical deployment checklist
  private readonly DEPLOYMENT_CHECKLIST: Omit<DeploymentChecklistItem, 'status' | 'details'>[] = [
    {
      category: 'security',
      item: 'Authentication System',
      priority: 'critical',
      description: 'User authentication and authorization working correctly',
    },
    {
      category: 'security',
      item: 'Data Encryption',
      priority: 'critical',
      description: 'All sensitive data encrypted in transit and at rest',
    },
    {
      category: 'security',
      item: 'API Security',
      priority: 'critical',
      description: 'Admin portal API endpoints secured and authenticated',
    },
    {
      category: 'functionality',
      item: 'Booking Workflow',
      priority: 'critical',
      description: 'Complete booking-to-quote workflow functional',
    },
    {
      category: 'functionality',
      item: 'Email Notifications',
      priority: 'critical',
      description: 'Email system sending notifications reliably',
    },
    {
      category: 'functionality',
      item: 'Customer Management',
      priority: 'high',
      description: 'Customer CRUD operations working correctly',
    },
    {
      category: 'integration',
      item: 'Admin Portal Sync',
      priority: 'critical',
      description: 'Mobile app synchronized with admin dashboard',
    },
    {
      category: 'integration',
      item: 'Database Connectivity',
      priority: 'critical',
      description: 'Supabase database accessible and performant',
    },
    {
      category: 'performance',
      item: 'Query Performance',
      priority: 'high',
      description: 'Database queries executing within acceptable time limits',
    },
    {
      category: 'performance',
      item: 'API Response Times',
      priority: 'high',
      description: 'Admin portal API responding within performance thresholds',
    },
    {
      category: 'monitoring',
      item: 'Error Handling',
      priority: 'high',
      description: 'Comprehensive error handling and user feedback',
    },
    {
      category: 'monitoring',
      item: 'Logging System',
      priority: 'medium',
      description: 'Proper logging for debugging and monitoring',
    },
  ];

  private constructor() {}

  public static getInstance(): ProductionReadinessValidator {
    if (!ProductionReadinessValidator.instance) {
      ProductionReadinessValidator.instance = new ProductionReadinessValidator();
    }
    return ProductionReadinessValidator.instance;
  }

  /**
   * Run comprehensive production readiness validation
   */
  async validateProductionReadiness(): Promise<ProductionReadinessReport> {
    const startTime = Date.now();
    console.log('🚀 Starting comprehensive production readiness validation...');

    try {
      // Run all validation systems in parallel
      const [
        enhancedWorkflowResult,
        emailSystemResult,
        adminPortalResult,
        databaseResult,
        finalIntegrationResult,
      ] = await Promise.allSettled([
        integrationValidator.validateWorkflowIntegration(),
        emailTesting.getEmailSystemStatus(),
        adminPortalIntegrationSuite.runCompleteIntegrationValidation(),
        databaseIntegrationSuite.runCompleteDatabaseValidation(),
        finalIntegrationTester.testCompleteSystemIntegration(),
      ]);

      // Extract results from settled promises
      const enhancedWorkflow = this.extractResult(enhancedWorkflowResult, null);
      const emailNotifications = this.extractResult(emailSystemResult, {
        emailService: false,
        authentication: false,
        templates: false,
        adminPortalConnection: false,
        overall: false,
      });
      const adminPortalIntegration = this.extractResult(adminPortalResult, null);
      const databaseIntegration = this.extractResult(databaseResult, null);
      const finalIntegration = this.extractResult(finalIntegrationResult, {
        overall: 'fail',
        systems: {
          enhancedWorkflow: false,
          emailNotifications: false,
          adminPortalIntegration: false,
          databaseValidation: false,
        },
        summary: { totalSystems: 4, workingSystems: 0, successRate: 0 },
        recommendations: ['System validation failed'],
      });

      // Calculate readiness score
      const readinessScore = this.calculateReadinessScore({
        enhancedWorkflow: enhancedWorkflow?.overall === 'pass',
        emailSystem: emailNotifications.overall,
        adminPortal: adminPortalIntegration?.overall === 'pass',
        database: databaseIntegration?.overall === 'pass',
      });

      // Generate deployment checklist
      const deploymentChecklist = await this.generateDeploymentChecklist({
        enhancedWorkflow,
        emailNotifications,
        adminPortalIntegration,
        databaseIntegration,
        finalIntegration,
      });

      // Identify critical blockers
      const criticalBlockers = this.identifyCriticalBlockers(deploymentChecklist);

      // Calculate summary metrics
      const readySystems = [
        enhancedWorkflow?.overall === 'pass',
        emailNotifications.overall,
        adminPortalIntegration?.overall === 'pass',
        databaseIntegration?.overall === 'pass',
      ].filter(Boolean).length;

      const criticalIssues = criticalBlockers.length;
      const warnings = deploymentChecklist.filter(item => item.status === 'warning').length;

      // Determine overall readiness
      const overall: 'ready' | 'not_ready' | 'warning' = 
        criticalIssues > 0 ? 'not_ready' :
        readinessScore >= 90 ? 'ready' :
        readinessScore >= 75 ? 'warning' : 'not_ready';

      // Generate recommendations and next steps
      const { recommendations, nextSteps } = this.generateRecommendations({
        overall,
        readinessScore,
        criticalBlockers,
        deploymentChecklist,
        systemReports: {
          enhancedWorkflow,
          emailNotifications,
          adminPortalIntegration,
          databaseIntegration,
          finalIntegration,
        },
      });

      const report: ProductionReadinessReport = {
        timestamp: new Date().toISOString(),
        overall,
        readinessScore,
        summary: {
          totalSystems: 4,
          readySystems,
          criticalIssues,
          warnings,
          duration: Date.now() - startTime,
        },
        systemReports: {
          enhancedWorkflow,
          emailNotifications,
          adminPortalIntegration,
          databaseIntegration,
          finalIntegration,
        },
        deploymentChecklist,
        criticalBlockers,
        recommendations,
        nextSteps,
      };

      console.log(`✅ Production readiness validation completed: ${readinessScore}% ready (${overall})`);

      return report;

    } catch (error) {
      console.error('❌ Production readiness validation failed:', error);
      
      return {
        timestamp: new Date().toISOString(),
        overall: 'not_ready',
        readinessScore: 0,
        summary: {
          totalSystems: 4,
          readySystems: 0,
          criticalIssues: 1,
          warnings: 0,
          duration: Date.now() - startTime,
        },
        systemReports: {
          enhancedWorkflow: null,
          emailNotifications: {
            emailService: false,
            authentication: false,
            templates: false,
            adminPortalConnection: false,
            overall: false,
          },
          adminPortalIntegration: null,
          databaseIntegration: null,
          finalIntegration: {
            overall: 'fail',
            systems: {
              enhancedWorkflow: false,
              emailNotifications: false,
              adminPortalIntegration: false,
              databaseValidation: false,
            },
            summary: { totalSystems: 4, workingSystems: 0, successRate: 0 },
            recommendations: ['Critical validation system error'],
          },
        },
        deploymentChecklist: [],
        criticalBlockers: [error instanceof Error ? error.message : 'Unknown validation error'],
        recommendations: ['Fix critical validation system error before proceeding'],
        nextSteps: ['Debug and resolve validation system issues'],
      };
    }
  }

  /**
   * Extract result from settled promise
   */
  private extractResult<T>(settledResult: PromiseSettledResult<T>, fallback: T): T {
    if (settledResult.status === 'fulfilled') {
      return settledResult.value;
    } else {
      console.warn('Promise rejected:', settledResult.reason);
      return fallback;
    }
  }

  /**
   * Calculate overall readiness score
   */
  private calculateReadinessScore(systemStatus: {
    enhancedWorkflow: boolean;
    emailSystem: boolean;
    adminPortal: boolean;
    database: boolean;
  }): number {
    let score = 0;
    
    if (systemStatus.enhancedWorkflow) score += this.SYSTEM_WEIGHTS.enhancedWorkflow;
    if (systemStatus.emailSystem) score += this.SYSTEM_WEIGHTS.emailSystem;
    if (systemStatus.adminPortal) score += this.SYSTEM_WEIGHTS.adminPortal;
    if (systemStatus.database) score += this.SYSTEM_WEIGHTS.database;

    return Math.round(score);
  }

  /**
   * Generate deployment checklist with current status
   */
  private async generateDeploymentChecklist(systemReports: any): Promise<DeploymentChecklistItem[]> {
    return this.DEPLOYMENT_CHECKLIST.map(item => {
      let status: 'pass' | 'fail' | 'warning' | 'not_tested' = 'not_tested';
      let details: any = {};

      // Determine status based on system reports
      switch (item.item) {
        case 'Authentication System':
          status = systemReports.adminPortalIntegration?.status.adminPortal.authentication ? 'pass' : 'fail';
          break;
        case 'Booking Workflow':
          status = systemReports.enhancedWorkflow?.overall === 'pass' ? 'pass' : 'fail';
          break;
        case 'Email Notifications':
          status = systemReports.emailNotifications.overall ? 'pass' : 'fail';
          break;
        case 'Admin Portal Sync':
          status = systemReports.adminPortalIntegration?.overall === 'pass' ? 'pass' : 'fail';
          break;
        case 'Database Connectivity':
          status = systemReports.databaseIntegration?.status.schema.overall ? 'pass' : 'fail';
          break;
        case 'Query Performance':
          status = systemReports.databaseIntegration?.status.performance.queryPerformance ? 'pass' : 'warning';
          break;
        case 'API Response Times':
          status = systemReports.adminPortalIntegration?.status.adminPortal.connectivity ? 'pass' : 'warning';
          break;
        default:
          status = 'not_tested';
      }

      return {
        ...item,
        status,
        details,
      };
    });
  }

  /**
   * Identify critical blockers for deployment
   */
  private identifyCriticalBlockers(checklist: DeploymentChecklistItem[]): string[] {
    return checklist
      .filter(item => item.priority === 'critical' && item.status === 'fail')
      .map(item => `${item.item}: ${item.description}`);
  }

  /**
   * Generate recommendations and next steps
   */
  private generateRecommendations(data: {
    overall: 'ready' | 'not_ready' | 'warning';
    readinessScore: number;
    criticalBlockers: string[];
    deploymentChecklist: DeploymentChecklistItem[];
    systemReports: any;
  }): { recommendations: string[]; nextSteps: string[] } {
    
    const recommendations: string[] = [];
    const nextSteps: string[] = [];

    if (data.overall === 'ready') {
      recommendations.push('🎉 System is production-ready for deployment');
      recommendations.push('All critical systems are operational and validated');
      nextSteps.push('1. Perform final user acceptance testing');
      nextSteps.push('2. Schedule production deployment');
      nextSteps.push('3. Implement production monitoring');
    } else if (data.overall === 'warning') {
      recommendations.push('⚠️ System is mostly ready but has minor issues');
      recommendations.push('Address warnings before production deployment');
      nextSteps.push('1. Fix identified warning issues');
      nextSteps.push('2. Re-run production readiness validation');
      nextSteps.push('3. Plan deployment after fixes');
    } else {
      recommendations.push('❌ System is not ready for production deployment');
      recommendations.push('Critical issues must be resolved before deployment');
      nextSteps.push('1. Address all critical blockers immediately');
      nextSteps.push('2. Fix failed deployment checklist items');
      nextSteps.push('3. Re-run validation after fixes');
    }

    // Add specific recommendations based on failed systems
    if (data.systemReports.enhancedWorkflow?.overall !== 'pass') {
      recommendations.push('Fix Enhanced Booking-to-Quote Workflow issues');
    }
    if (!data.systemReports.emailNotifications.overall) {
      recommendations.push('Resolve email notification system problems');
    }
    if (data.systemReports.adminPortalIntegration?.overall !== 'pass') {
      recommendations.push('Address admin portal integration failures');
    }
    if (data.systemReports.databaseIntegration?.overall !== 'pass') {
      recommendations.push('Fix database integration and performance issues');
    }

    return { recommendations, nextSteps };
  }

  /**
   * Get current production readiness status
   */
  async getProductionReadinessStatus(): Promise<ProductionReadinessStatus> {
    try {
      const report = await this.validateProductionReadiness();
      
      return {
        enhancedWorkflow: report.systemReports.enhancedWorkflow?.overall === 'pass',
        emailSystem: report.systemReports.emailNotifications.overall,
        adminPortal: report.systemReports.adminPortalIntegration?.overall === 'pass',
        database: report.systemReports.databaseIntegration?.overall === 'pass',
        overall: report.overall === 'ready',
        readinessPercentage: report.readinessScore,
      };
    } catch (error) {
      console.error('❌ Failed to get production readiness status:', error);
      return {
        enhancedWorkflow: false,
        emailSystem: false,
        adminPortal: false,
        database: false,
        overall: false,
        readinessPercentage: 0,
      };
    }
  }
}

// Export singleton instance
export const productionReadinessValidator = ProductionReadinessValidator.getInstance();
