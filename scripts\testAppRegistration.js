/**
 * Ocean Soul Sparkles Mobile App - App Registration Test
 * Tests if the app can be imported and registered without errors
 */

console.log('🧪 Testing App Registration...');

try {
  // Test 1: Check if index.js exists and is valid
  console.log('\n📱 Test 1: Checking index.js...');
  const fs = require('fs');
  const path = require('path');
  
  const indexPath = path.join(__dirname, '..', 'index.js');
  if (fs.existsSync(indexPath)) {
    console.log('✅ index.js exists');
    
    const indexContent = fs.readFileSync(indexPath, 'utf8');
    console.log('📄 index.js content:');
    console.log(indexContent);
    
    if (indexContent.includes('registerRootComponent')) {
      console.log('✅ index.js contains registerRootComponent');
    } else {
      console.log('❌ index.js missing registerRootComponent');
    }
  } else {
    console.log('❌ index.js does not exist');
  }
  
  // Test 2: Check app.json configuration
  console.log('\n📱 Test 2: Checking app.json...');
  const appJsonPath = path.join(__dirname, '..', 'app.json');
  if (fs.existsSync(appJsonPath)) {
    console.log('✅ app.json exists');
    
    const appConfig = JSON.parse(fs.readFileSync(appJsonPath, 'utf8'));
    console.log('📄 app.json expo config:');
    console.log(JSON.stringify(appConfig.expo, null, 2));
    
    if (appConfig.expo && appConfig.expo.main) {
      console.log(`✅ app.json specifies main entry point: ${appConfig.expo.main}`);
    } else {
      console.log('❌ app.json missing main entry point');
    }
  } else {
    console.log('❌ app.json does not exist');
  }
  
  // Test 3: Check if App.tsx can be imported (this might reveal import errors)
  console.log('\n📱 Test 3: Testing App.tsx import...');
  try {
    // Note: This won't work in Node.js environment, but we can check if the file exists
    const appTsxPath = path.join(__dirname, '..', 'App.tsx');
    if (fs.existsSync(appTsxPath)) {
      console.log('✅ App.tsx exists');
      
      // Check for potential problematic imports
      const appContent = fs.readFileSync(appTsxPath, 'utf8');
      
      // Look for imports that might cause issues
      const imports = appContent.match(/import.*from.*['"].*['"];?/g) || [];
      console.log('📦 App.tsx imports:');
      imports.forEach(imp => console.log(`  ${imp}`));
      
      // Check for specific problematic patterns
      if (appContent.includes('@/services/email')) {
        console.log('⚠️ App.tsx imports email services - this could cause property access errors during initialization');
      }
      
      if (appContent.includes('@/services/database')) {
        console.log('⚠️ App.tsx imports database services - this could cause property access errors during initialization');
      }
      
    } else {
      console.log('❌ App.tsx does not exist');
    }
  } catch (error) {
    console.log('❌ Error checking App.tsx:', error.message);
  }
  
  // Test 4: Check for potential circular dependencies in email services
  console.log('\n📱 Test 4: Checking email service dependencies...');
  try {
    const emailServicePath = path.join(__dirname, '..', 'src', 'services', 'email', 'emailService.ts');
    if (fs.existsSync(emailServicePath)) {
      console.log('✅ emailService.ts exists');
      
      const emailServiceContent = fs.readFileSync(emailServicePath, 'utf8');
      
      // Check for imports that might cause circular dependencies
      const imports = emailServiceContent.match(/import.*from.*['"].*['"];?/g) || [];
      console.log('📦 emailService.ts imports:');
      imports.forEach(imp => console.log(`  ${imp}`));
      
      // Look for potential issues
      if (emailServiceContent.includes('estimated_total')) {
        console.log('✅ emailService.ts references estimated_total');
      }
      
      if (emailServiceContent.includes('total_amount')) {
        console.log('⚠️ emailService.ts still references total_amount - this could cause property access errors');
      }
      
    } else {
      console.log('❌ emailService.ts does not exist');
    }
  } catch (error) {
    console.log('❌ Error checking emailService.ts:', error.message);
  }
  
  // Test 5: Check email templates
  console.log('\n📱 Test 5: Checking email templates...');
  try {
    const emailTemplatesPath = path.join(__dirname, '..', 'src', 'services', 'email', 'emailTemplates.ts');
    if (fs.existsSync(emailTemplatesPath)) {
      console.log('✅ emailTemplates.ts exists');
      
      const templatesContent = fs.readFileSync(emailTemplatesPath, 'utf8');
      
      // Check for property references
      if (templatesContent.includes('{{estimated_total}}')) {
        console.log('✅ emailTemplates.ts uses {{estimated_total}}');
      }
      
      if (templatesContent.includes('{{total_amount}}')) {
        console.log('⚠️ emailTemplates.ts still uses {{total_amount}} - this could cause confusion');
      }
      
      if (templatesContent.includes('{{quote_amount}}')) {
        console.log('❌ emailTemplates.ts still uses {{quote_amount}} - this will cause property access errors');
      }
      
      // Check variables arrays
      const variableMatches = templatesContent.match(/variables:\s*\[(.*?)\]/gs) || [];
      console.log('📋 Template variables arrays:');
      variableMatches.forEach((match, index) => {
        console.log(`  Template ${index + 1}: ${match}`);
      });
      
    } else {
      console.log('❌ emailTemplates.ts does not exist');
    }
  } catch (error) {
    console.log('❌ Error checking emailTemplates.ts:', error.message);
  }
  
  console.log('\n🎯 App Registration Test Summary:');
  console.log('='.repeat(40));
  console.log('✅ If all tests pass, the app registration should work');
  console.log('⚠️ If there are warnings, they might cause runtime errors');
  console.log('❌ If there are errors, they will prevent app registration');
  
} catch (error) {
  console.error('❌ App registration test failed:', error.message);
}
