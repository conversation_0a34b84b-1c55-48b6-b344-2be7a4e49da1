/**
 * Ocean Soul Sparkles Mobile App - Database Performance Optimizer
 * Monitors and optimizes database performance, SQL functions, and query efficiency
 */

import { supabase } from './supabase';
import { sqlFunctionsService } from './sqlFunctionsService';

export interface PerformanceMetrics {
  queryTime: number;
  connectionTime: number;
  functionExecutionTime: number;
  cacheHitRate: number;
  activeConnections: number;
  slowQueries: SlowQuery[];
  indexEfficiency: number;
  overallScore: number;
}

export interface SlowQuery {
  query: string;
  duration: number;
  timestamp: string;
  table: string;
  type: 'SELECT' | 'INSERT' | 'UPDATE' | 'DELETE' | 'FUNCTION';
}

export interface OptimizationRecommendation {
  type: 'index' | 'query' | 'function' | 'connection' | 'cache';
  priority: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  impact: string;
  implementation: string;
  estimatedImprovement: string;
}

export interface DatabaseHealthReport {
  timestamp: string;
  metrics: PerformanceMetrics;
  recommendations: OptimizationRecommendation[];
  status: 'excellent' | 'good' | 'fair' | 'poor' | 'critical';
  summary: string;
}

export class DatabasePerformanceOptimizer {
  private static instance: DatabasePerformanceOptimizer;
  private performanceHistory: PerformanceMetrics[] = [];
  private readonly MAX_HISTORY_SIZE = 100;
  private readonly SLOW_QUERY_THRESHOLD = 1000; // 1 second

  private constructor() {}

  public static getInstance(): DatabasePerformanceOptimizer {
    if (!DatabasePerformanceOptimizer.instance) {
      DatabasePerformanceOptimizer.instance = new DatabasePerformanceOptimizer();
    }
    return DatabasePerformanceOptimizer.instance;
  }

  /**
   * Measure database performance metrics
   */
  async measurePerformanceMetrics(): Promise<PerformanceMetrics> {
    try {
      console.log('📊 Measuring database performance metrics...');

      const metrics: Partial<PerformanceMetrics> = {};

      // Measure query time
      const queryStartTime = Date.now();
      await supabase.from('customers').select('count').limit(1);
      metrics.queryTime = Date.now() - queryStartTime;

      // Measure connection time
      const connectionStartTime = Date.now();
      await supabase.from('pg_stat_activity').select('count').limit(1);
      metrics.connectionTime = Date.now() - connectionStartTime;

      // Measure function execution time
      const functionStartTime = Date.now();
      await sqlFunctionsService.generateTransactionNumber();
      metrics.functionExecutionTime = Date.now() - functionStartTime;

      // Get database statistics
      const dbStats = await this.getDatabaseStatistics();
      metrics.cacheHitRate = dbStats.cacheHitRate;
      metrics.activeConnections = dbStats.activeConnections;
      metrics.indexEfficiency = dbStats.indexEfficiency;

      // Get slow queries
      metrics.slowQueries = await this.getSlowQueries();

      // Calculate overall score
      metrics.overallScore = this.calculateOverallScore(metrics as PerformanceMetrics);

      const finalMetrics = metrics as PerformanceMetrics;

      // Store in history
      this.addToHistory(finalMetrics);

      console.log(`✅ Performance metrics measured - Overall Score: ${finalMetrics.overallScore}%`);
      return finalMetrics;

    } catch (error) {
      console.error('❌ Performance measurement failed:', error);
      
      // Return default metrics on error
      return {
        queryTime: 9999,
        connectionTime: 9999,
        functionExecutionTime: 9999,
        cacheHitRate: 0,
        activeConnections: 0,
        slowQueries: [],
        indexEfficiency: 0,
        overallScore: 0,
      };
    }
  }

  /**
   * Get database statistics
   */
  private async getDatabaseStatistics(): Promise<{
    cacheHitRate: number;
    activeConnections: number;
    indexEfficiency: number;
  }> {
    try {
      // Note: These queries would work on a full PostgreSQL instance
      // Supabase may have limited access to some system tables
      
      // Cache hit rate (simplified calculation)
      const cacheHitRate = 95; // Default good value for Supabase

      // Active connections (estimated)
      const activeConnections = 5; // Default estimate

      // Index efficiency (estimated based on query performance)
      const indexEfficiency = this.estimateIndexEfficiency();

      return {
        cacheHitRate,
        activeConnections,
        indexEfficiency,
      };

    } catch (error) {
      console.warn('Database statistics query failed, using defaults:', error);
      return {
        cacheHitRate: 90,
        activeConnections: 3,
        indexEfficiency: 80,
      };
    }
  }

  /**
   * Estimate index efficiency based on query performance
   */
  private estimateIndexEfficiency(): number {
    if (this.performanceHistory.length === 0) return 85;

    const recentMetrics = this.performanceHistory.slice(-10);
    const avgQueryTime = recentMetrics.reduce((sum, m) => sum + m.queryTime, 0) / recentMetrics.length;

    // Good query time (< 100ms) indicates good index efficiency
    if (avgQueryTime < 100) return 95;
    if (avgQueryTime < 300) return 85;
    if (avgQueryTime < 500) return 75;
    if (avgQueryTime < 1000) return 65;
    return 50;
  }

  /**
   * Get slow queries (simulated for now)
   */
  private async getSlowQueries(): Promise<SlowQuery[]> {
    try {
      // In a real implementation, this would query pg_stat_statements
      // For now, we'll simulate based on recent performance
      
      const slowQueries: SlowQuery[] = [];
      
      if (this.performanceHistory.length > 0) {
        const recent = this.performanceHistory[this.performanceHistory.length - 1];
        
        if (recent.queryTime > this.SLOW_QUERY_THRESHOLD) {
          slowQueries.push({
            query: 'SELECT * FROM customers WHERE ...',
            duration: recent.queryTime,
            timestamp: new Date().toISOString(),
            table: 'customers',
            type: 'SELECT',
          });
        }

        if (recent.functionExecutionTime > this.SLOW_QUERY_THRESHOLD) {
          slowQueries.push({
            query: 'FUNCTION generate_transaction_number()',
            duration: recent.functionExecutionTime,
            timestamp: new Date().toISOString(),
            table: 'functions',
            type: 'FUNCTION',
          });
        }
      }

      return slowQueries;

    } catch (error) {
      console.warn('Slow query detection failed:', error);
      return [];
    }
  }

  /**
   * Calculate overall performance score
   */
  private calculateOverallScore(metrics: PerformanceMetrics): number {
    let score = 100;

    // Query time impact (30% weight)
    if (metrics.queryTime > 1000) score -= 30;
    else if (metrics.queryTime > 500) score -= 20;
    else if (metrics.queryTime > 200) score -= 10;
    else if (metrics.queryTime > 100) score -= 5;

    // Function execution time impact (25% weight)
    if (metrics.functionExecutionTime > 2000) score -= 25;
    else if (metrics.functionExecutionTime > 1000) score -= 15;
    else if (metrics.functionExecutionTime > 500) score -= 10;
    else if (metrics.functionExecutionTime > 200) score -= 5;

    // Cache hit rate impact (20% weight)
    if (metrics.cacheHitRate < 70) score -= 20;
    else if (metrics.cacheHitRate < 80) score -= 15;
    else if (metrics.cacheHitRate < 90) score -= 10;
    else if (metrics.cacheHitRate < 95) score -= 5;

    // Index efficiency impact (15% weight)
    if (metrics.indexEfficiency < 60) score -= 15;
    else if (metrics.indexEfficiency < 70) score -= 10;
    else if (metrics.indexEfficiency < 80) score -= 8;
    else if (metrics.indexEfficiency < 90) score -= 5;

    // Slow queries impact (10% weight)
    score -= Math.min(metrics.slowQueries.length * 2, 10);

    return Math.max(0, Math.round(score));
  }

  /**
   * Generate optimization recommendations
   */
  generateOptimizationRecommendations(metrics: PerformanceMetrics): OptimizationRecommendation[] {
    const recommendations: OptimizationRecommendation[] = [];

    // Query performance recommendations
    if (metrics.queryTime > 500) {
      recommendations.push({
        type: 'query',
        priority: metrics.queryTime > 1000 ? 'critical' : 'high',
        description: 'Query execution time is above optimal threshold',
        impact: 'Slow queries affect user experience and app responsiveness',
        implementation: 'Review and optimize slow queries, add appropriate indexes',
        estimatedImprovement: '30-50% faster query execution',
      });
    }

    // Function performance recommendations
    if (metrics.functionExecutionTime > 1000) {
      recommendations.push({
        type: 'function',
        priority: metrics.functionExecutionTime > 2000 ? 'critical' : 'high',
        description: 'SQL function execution time is high',
        impact: 'Slow functions delay business operations',
        implementation: 'Optimize SQL functions, review logic complexity',
        estimatedImprovement: '40-60% faster function execution',
      });
    }

    // Cache recommendations
    if (metrics.cacheHitRate < 90) {
      recommendations.push({
        type: 'cache',
        priority: metrics.cacheHitRate < 80 ? 'high' : 'medium',
        description: 'Database cache hit rate is below optimal',
        impact: 'Low cache hit rate increases query response time',
        implementation: 'Implement query result caching, optimize cache configuration',
        estimatedImprovement: '20-30% faster data access',
      });
    }

    // Index recommendations
    if (metrics.indexEfficiency < 80) {
      recommendations.push({
        type: 'index',
        priority: metrics.indexEfficiency < 60 ? 'critical' : 'high',
        description: 'Database index efficiency is suboptimal',
        impact: 'Poor indexing leads to slow query execution',
        implementation: 'Add missing indexes, remove unused indexes, optimize existing ones',
        estimatedImprovement: '50-70% faster query execution',
      });
    }

    // Connection recommendations
    if (metrics.activeConnections > 20) {
      recommendations.push({
        type: 'connection',
        priority: 'medium',
        description: 'High number of active database connections',
        impact: 'Too many connections can impact database performance',
        implementation: 'Implement connection pooling, optimize connection usage',
        estimatedImprovement: '15-25% better resource utilization',
      });
    }

    // Slow query recommendations
    if (metrics.slowQueries.length > 0) {
      recommendations.push({
        type: 'query',
        priority: 'high',
        description: `${metrics.slowQueries.length} slow queries detected`,
        impact: 'Slow queries significantly impact application performance',
        implementation: 'Analyze and optimize identified slow queries',
        estimatedImprovement: '40-60% improvement in affected operations',
      });
    }

    return recommendations;
  }

  /**
   * Generate comprehensive database health report
   */
  async generateHealthReport(): Promise<DatabaseHealthReport> {
    try {
      console.log('📋 Generating database health report...');

      const metrics = await this.measurePerformanceMetrics();
      const recommendations = this.generateOptimizationRecommendations(metrics);

      // Determine overall status
      let status: DatabaseHealthReport['status'];
      if (metrics.overallScore >= 90) status = 'excellent';
      else if (metrics.overallScore >= 80) status = 'good';
      else if (metrics.overallScore >= 70) status = 'fair';
      else if (metrics.overallScore >= 50) status = 'poor';
      else status = 'critical';

      // Generate summary
      const summary = this.generateHealthSummary(metrics, status, recommendations);

      const report: DatabaseHealthReport = {
        timestamp: new Date().toISOString(),
        metrics,
        recommendations,
        status,
        summary,
      };

      console.log(`✅ Database health report generated - Status: ${status} (${metrics.overallScore}%)`);
      return report;

    } catch (error) {
      console.error('❌ Health report generation failed:', error);
      
      return {
        timestamp: new Date().toISOString(),
        metrics: await this.measurePerformanceMetrics(),
        recommendations: [],
        status: 'critical',
        summary: 'Failed to generate health report due to system error',
      };
    }
  }

  /**
   * Generate health summary
   */
  private generateHealthSummary(
    metrics: PerformanceMetrics,
    status: DatabaseHealthReport['status'],
    recommendations: OptimizationRecommendation[]
  ): string {
    const lines = [];

    lines.push(`Database performance is ${status.toUpperCase()} (${metrics.overallScore}% score).`);
    
    if (metrics.queryTime < 200) {
      lines.push('Query performance is excellent.');
    } else if (metrics.queryTime < 500) {
      lines.push('Query performance is good but could be optimized.');
    } else {
      lines.push('Query performance needs immediate attention.');
    }

    if (metrics.functionExecutionTime < 500) {
      lines.push('SQL functions are performing well.');
    } else {
      lines.push('SQL function performance should be optimized.');
    }

    if (recommendations.length === 0) {
      lines.push('No immediate optimizations required.');
    } else {
      const criticalCount = recommendations.filter(r => r.priority === 'critical').length;
      const highCount = recommendations.filter(r => r.priority === 'high').length;
      
      if (criticalCount > 0) {
        lines.push(`${criticalCount} critical optimization(s) required.`);
      }
      if (highCount > 0) {
        lines.push(`${highCount} high-priority optimization(s) recommended.`);
      }
    }

    return lines.join(' ');
  }

  /**
   * Add metrics to performance history
   */
  private addToHistory(metrics: PerformanceMetrics): void {
    this.performanceHistory.push(metrics);
    
    // Keep only recent history
    if (this.performanceHistory.length > this.MAX_HISTORY_SIZE) {
      this.performanceHistory = this.performanceHistory.slice(-this.MAX_HISTORY_SIZE);
    }
  }

  /**
   * Get performance trends
   */
  getPerformanceTrends(): {
    queryTimetrend: 'improving' | 'stable' | 'degrading';
    functionTimetrend: 'improving' | 'stable' | 'degrading';
    overallTrend: 'improving' | 'stable' | 'degrading';
  } {
    if (this.performanceHistory.length < 5) {
      return {
        queryTimetrend: 'stable',
        functionTimetrend: 'stable',
        overallTrend: 'stable',
      };
    }

    const recent = this.performanceHistory.slice(-5);
    const older = this.performanceHistory.slice(-10, -5);

    const recentAvgQuery = recent.reduce((sum, m) => sum + m.queryTime, 0) / recent.length;
    const olderAvgQuery = older.reduce((sum, m) => sum + m.queryTime, 0) / older.length;

    const recentAvgFunction = recent.reduce((sum, m) => sum + m.functionExecutionTime, 0) / recent.length;
    const olderAvgFunction = older.reduce((sum, m) => sum + m.functionExecutionTime, 0) / older.length;

    const recentAvgScore = recent.reduce((sum, m) => sum + m.overallScore, 0) / recent.length;
    const olderAvgScore = older.reduce((sum, m) => sum + m.overallScore, 0) / older.length;

    return {
      queryTimetrend: recentAvgQuery < olderAvgQuery * 0.9 ? 'improving' : 
                     recentAvgQuery > olderAvgQuery * 1.1 ? 'degrading' : 'stable',
      functionTimetrend: recentAvgFunction < olderAvgFunction * 0.9 ? 'improving' : 
                        recentAvgFunction > olderAvgFunction * 1.1 ? 'degrading' : 'stable',
      overallTrend: recentAvgScore > olderAvgScore * 1.05 ? 'improving' : 
                   recentAvgScore < olderAvgScore * 0.95 ? 'degrading' : 'stable',
    };
  }

  /**
   * Get current performance history
   */
  getPerformanceHistory(): PerformanceMetrics[] {
    return [...this.performanceHistory];
  }
}

// Export singleton instance
export const databasePerformanceOptimizer = DatabasePerformanceOptimizer.getInstance();
