#!/usr/bin/env node

/**
 * Ocean Soul Sparkles Mobile App - Android Studio Setup Script
 * Configures the development environment for Metro + Android Studio integration
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🚀 Setting up Android Studio + Metro development environment...\n');

// Check if Android SDK is available
function checkAndroidSDK() {
  try {
    const androidHome = process.env.ANDROID_HOME || process.env.ANDROID_SDK_ROOT;
    if (!androidHome) {
      console.log('⚠️  ANDROID_HOME not set. Please set up Android SDK.');
      console.log('   Download from: https://developer.android.com/studio');
      return false;
    }
    
    console.log('✅ Android SDK found at:', androidHome);
    return true;
  } catch (error) {
    console.log('❌ Android SDK check failed:', error.message);
    return false;
  }
}

// Check if Java is available
function checkJava() {
  try {
    const javaVersion = execSync('java -version', { encoding: 'utf8', stdio: 'pipe' });
    console.log('✅ Java is available');
    return true;
  } catch (error) {
    console.log('⚠️  Java not found. Please install Java 11 or higher.');
    return false;
  }
}

// Verify Android project structure
function verifyAndroidProject() {
  const androidDir = path.join(__dirname, '..', 'android');
  const buildGradle = path.join(androidDir, 'build.gradle');
  const appBuildGradle = path.join(androidDir, 'app', 'build.gradle');
  
  if (!fs.existsSync(buildGradle) || !fs.existsSync(appBuildGradle)) {
    console.log('❌ Android project structure incomplete');
    return false;
  }
  
  console.log('✅ Android project structure verified');
  return true;
}

// Create Metro configuration for Android Studio
function setupMetroConfig() {
  const metroConfig = path.join(__dirname, '..', 'metro.config.js');
  if (fs.existsSync(metroConfig)) {
    console.log('✅ Metro configuration already exists');
    return true;
  }
  
  console.log('❌ Metro configuration missing');
  return false;
}

// Check React Native CLI
function checkReactNativeCLI() {
  try {
    execSync('npx react-native --version', { encoding: 'utf8', stdio: 'pipe' });
    console.log('✅ React Native CLI is available');
    return true;
  } catch (error) {
    console.log('⚠️  React Native CLI not found. Installing...');
    try {
      execSync('npm install -g @react-native-community/cli', { stdio: 'inherit' });
      console.log('✅ React Native CLI installed');
      return true;
    } catch (installError) {
      console.log('❌ Failed to install React Native CLI');
      return false;
    }
  }
}

// Main setup function
function main() {
  console.log('🔍 Checking development environment...\n');
  
  const checks = [
    { name: 'Android SDK', check: checkAndroidSDK },
    { name: 'Java', check: checkJava },
    { name: 'Android Project', check: verifyAndroidProject },
    { name: 'Metro Config', check: setupMetroConfig },
    { name: 'React Native CLI', check: checkReactNativeCLI },
  ];
  
  let allPassed = true;
  
  checks.forEach(({ name, check }) => {
    const passed = check();
    if (!passed) {
      allPassed = false;
    }
  });
  
  console.log('\n' + '='.repeat(60));
  
  if (allPassed) {
    console.log('🎉 Setup complete! You can now use Android Studio with Metro.');
    console.log('\n📋 Next steps:');
    console.log('1. Open Android Studio');
    console.log('2. Open the android/ folder as a project');
    console.log('3. Run: npm run metro');
    console.log('4. Build and run the app in Android Studio');
    console.log('\n🛠️  Available commands:');
    console.log('   npm run metro          - Start Metro bundler');
    console.log('   npm run metro:reset     - Start Metro with cache reset');
    console.log('   npm run android:metro   - Run Android app with Metro');
    console.log('   npm run dev:android     - Start Metro and Android together');
  } else {
    console.log('❌ Setup incomplete. Please resolve the issues above.');
    console.log('\n📖 Helpful resources:');
    console.log('   Android Studio: https://developer.android.com/studio');
    console.log('   React Native Setup: https://reactnative.dev/docs/environment-setup');
  }
  
  console.log('='.repeat(60));
}

// Run the setup
main();
