/**
 * Ocean Soul Sparkles Mobile App - Database Performance & Indexing Validator
 * Validates database performance, query optimization, and indexing strategies
 */

import { supabase } from '@/services/database/supabase';
import { bookingService } from '@/services/database/bookingService';
import { customerService } from '@/services/database/customerService';
import { quoteService } from '@/services/database/quoteService';

export interface PerformanceValidationResult {
  test: string;
  operation: string;
  status: 'pass' | 'fail' | 'warning';
  message: string;
  duration: number;
  details?: any;
  error?: string;
}

export interface QueryPerformanceMetrics {
  operation: string;
  table: string;
  executionTime: number;
  recordCount: number;
  performanceRating: 'excellent' | 'good' | 'acceptable' | 'poor';
}

export interface DatabasePerformanceStatus {
  queryPerformance: boolean;
  indexOptimization: boolean;
  connectionStability: boolean;
  memoryUsage: boolean;
  overall: boolean;
}

export class DatabasePerformanceValidator {
  private static instance: DatabasePerformanceValidator;

  // Performance thresholds (in milliseconds)
  private readonly PERFORMANCE_THRESHOLDS = {
    excellent: 100,
    good: 300,
    acceptable: 1000,
    poor: 3000,
  };

  // Expected indexes for optimal performance
  private readonly EXPECTED_INDEXES = [
    { table: 'bookings', column: 'customer_id', type: 'foreign_key' },
    { table: 'bookings', column: 'service_id', type: 'foreign_key' },
    { table: 'bookings', column: 'booking_date', type: 'date_index' },
    { table: 'bookings', column: 'status', type: 'status_index' },
    { table: 'customers', column: 'email', type: 'unique_index' },
    { table: 'quotes', column: 'customer_id', type: 'foreign_key' },
    { table: 'quotes', column: 'status', type: 'status_index' },
    { table: 'transactions', column: 'transaction_number', type: 'unique_index' },
    { table: 'transactions', column: 'customer_id', type: 'foreign_key' },
  ];

  private constructor() {}

  public static getInstance(): DatabasePerformanceValidator {
    if (!DatabasePerformanceValidator.instance) {
      DatabasePerformanceValidator.instance = new DatabasePerformanceValidator();
    }
    return DatabasePerformanceValidator.instance;
  }

  /**
   * Validate query performance across core operations
   */
  async validateQueryPerformance(): Promise<PerformanceValidationResult[]> {
    console.log('⚡ Validating database query performance...');
    const results: PerformanceValidationResult[] = [];

    // Test various query operations
    const queryTests = [
      {
        name: 'Booking List Query',
        operation: 'SELECT',
        test: async () => {
          const startTime = Date.now();
          const result = await bookingService.getBookings({ limit: 20 });
          const duration = Date.now() - startTime;
          return { result, duration, recordCount: result.data?.length || 0 };
        }
      },
      {
        name: 'Customer Search Query',
        operation: 'SEARCH',
        test: async () => {
          const startTime = Date.now();
          const result = await customerService.getCustomers({ 
            search: 'test',
            limit: 10 
          });
          const duration = Date.now() - startTime;
          return { result, duration, recordCount: result.data?.length || 0 };
        }
      },
      {
        name: 'Quote Filter Query',
        operation: 'FILTER',
        test: async () => {
          const startTime = Date.now();
          const result = await quoteService.getQuotes({ 
            filters: { status: 'draft' },
            limit: 15 
          });
          const duration = Date.now() - startTime;
          return { result, duration, recordCount: result.data?.length || 0 };
        }
      },
      {
        name: 'Booking Date Range Query',
        operation: 'DATE_RANGE',
        test: async () => {
          const startTime = Date.now();
          const today = new Date().toISOString().split('T')[0];
          const { data, error } = await supabase
            .from('bookings')
            .select('*')
            .gte('booking_date', today)
            .limit(10);
          const duration = Date.now() - startTime;
          return { result: { data, error }, duration, recordCount: data?.length || 0 };
        }
      },
      {
        name: 'Customer Booking Join Query',
        operation: 'JOIN',
        test: async () => {
          const startTime = Date.now();
          const { data, error } = await supabase
            .from('bookings')
            .select('id, booking_date, customers(full_name, email)')
            .limit(10);
          const duration = Date.now() - startTime;
          return { result: { data, error }, duration, recordCount: data?.length || 0 };
        }
      },
    ];

    for (const queryTest of queryTests) {
      try {
        const testResult = await queryTest.test();
        const performanceRating = this.getPerformanceRating(testResult.duration);
        
        results.push({
          test: 'Query Performance',
          operation: queryTest.name,
          status: testResult.result.error ? 'fail' : 
                  performanceRating === 'poor' ? 'warning' : 'pass',
          message: testResult.result.error 
            ? `Query failed: ${testResult.result.error.message}`
            : `Query completed in ${testResult.duration}ms (${performanceRating})`,
          duration: testResult.duration,
          details: {
            operation: queryTest.operation,
            executionTime: testResult.duration,
            recordCount: testResult.recordCount,
            performanceRating,
            error: testResult.result.error?.message,
          },
          error: testResult.result.error?.message,
        });

      } catch (error) {
        results.push({
          test: 'Query Performance',
          operation: queryTest.name,
          status: 'fail',
          message: `Query test failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
          duration: 0,
          error: error instanceof Error ? error.message : 'Unknown error',
        });
      }
    }

    return results;
  }

  /**
   * Validate database indexing strategy
   */
  async validateIndexOptimization(): Promise<PerformanceValidationResult[]> {
    console.log('📊 Validating database indexing optimization...');
    const results: PerformanceValidationResult[] = [];

    // Test index effectiveness by comparing query performance
    for (const expectedIndex of this.EXPECTED_INDEXES) {
      const startTime = Date.now();
      
      try {
        // Test query that should benefit from the index
        let testQuery;
        
        switch (expectedIndex.type) {
          case 'foreign_key':
            testQuery = this.testForeignKeyIndex(expectedIndex.table, expectedIndex.column);
            break;
          case 'date_index':
            testQuery = this.testDateIndex(expectedIndex.table, expectedIndex.column);
            break;
          case 'status_index':
            testQuery = this.testStatusIndex(expectedIndex.table, expectedIndex.column);
            break;
          case 'unique_index':
            testQuery = this.testUniqueIndex(expectedIndex.table, expectedIndex.column);
            break;
          default:
            testQuery = Promise.resolve({ data: null, error: null, duration: 0 });
        }

        const indexTestResult = await testQuery;
        const performanceRating = this.getPerformanceRating(indexTestResult.duration);

        results.push({
          test: 'Index Optimization',
          operation: `${expectedIndex.table}.${expectedIndex.column} (${expectedIndex.type})`,
          status: indexTestResult.error ? 'fail' : 
                  performanceRating === 'poor' ? 'warning' : 'pass',
          message: indexTestResult.error 
            ? `Index test failed: ${indexTestResult.error.message}`
            : `Index query completed in ${indexTestResult.duration}ms (${performanceRating})`,
          duration: Date.now() - startTime,
          details: {
            table: expectedIndex.table,
            column: expectedIndex.column,
            indexType: expectedIndex.type,
            executionTime: indexTestResult.duration,
            performanceRating,
            error: indexTestResult.error?.message,
          },
          error: indexTestResult.error?.message,
        });

      } catch (error) {
        results.push({
          test: 'Index Optimization',
          operation: `${expectedIndex.table}.${expectedIndex.column}`,
          status: 'fail',
          message: `Index validation failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
          duration: Date.now() - startTime,
          error: error instanceof Error ? error.message : 'Unknown error',
        });
      }
    }

    return results;
  }

  /**
   * Test foreign key index performance
   */
  private async testForeignKeyIndex(table: string, column: string): Promise<{ data: any; error: any; duration: number }> {
    const startTime = Date.now();
    
    try {
      // Get a sample foreign key value
      const { data: sampleData } = await supabase
        .from(table)
        .select(column)
        .not(column, 'is', null)
        .limit(1);

      if (!sampleData || sampleData.length === 0) {
        return { data: null, error: null, duration: Date.now() - startTime };
      }

      const sampleValue = sampleData[0][column];

      // Test query using the foreign key
      const { data, error } = await supabase
        .from(table)
        .select('*')
        .eq(column, sampleValue)
        .limit(10);

      return { data, error, duration: Date.now() - startTime };

    } catch (error) {
      return { data: null, error, duration: Date.now() - startTime };
    }
  }

  /**
   * Test date index performance
   */
  private async testDateIndex(table: string, column: string): Promise<{ data: any; error: any; duration: number }> {
    const startTime = Date.now();
    
    try {
      const today = new Date().toISOString().split('T')[0];
      const { data, error } = await supabase
        .from(table)
        .select('*')
        .gte(column, today)
        .limit(10);

      return { data, error, duration: Date.now() - startTime };

    } catch (error) {
      return { data: null, error, duration: Date.now() - startTime };
    }
  }

  /**
   * Test status index performance
   */
  private async testStatusIndex(table: string, column: string): Promise<{ data: any; error: any; duration: number }> {
    const startTime = Date.now();
    
    try {
      // Test common status values
      const statusValues = ['pending', 'confirmed', 'draft', 'sent'];
      const testStatus = statusValues[0]; // Use first status for test

      const { data, error } = await supabase
        .from(table)
        .select('*')
        .eq(column, testStatus)
        .limit(10);

      return { data, error, duration: Date.now() - startTime };

    } catch (error) {
      return { data: null, error, duration: Date.now() - startTime };
    }
  }

  /**
   * Test unique index performance
   */
  private async testUniqueIndex(table: string, column: string): Promise<{ data: any; error: any; duration: number }> {
    const startTime = Date.now();
    
    try {
      // Get a sample unique value
      const { data: sampleData } = await supabase
        .from(table)
        .select(column)
        .not(column, 'is', null)
        .limit(1);

      if (!sampleData || sampleData.length === 0) {
        return { data: null, error: null, duration: Date.now() - startTime };
      }

      const sampleValue = sampleData[0][column];

      // Test unique lookup
      const { data, error } = await supabase
        .from(table)
        .select('*')
        .eq(column, sampleValue)
        .single();

      return { data, error, duration: Date.now() - startTime };

    } catch (error) {
      return { data: null, error, duration: Date.now() - startTime };
    }
  }

  /**
   * Validate connection stability and reliability
   */
  async validateConnectionStability(): Promise<PerformanceValidationResult[]> {
    console.log('🔗 Validating database connection stability...');
    const results: PerformanceValidationResult[] = [];

    // Test multiple concurrent connections
    const connectionTests = Array.from({ length: 5 }, (_, index) => ({
      name: `Connection Test ${index + 1}`,
      test: async () => {
        const startTime = Date.now();
        const { data, error } = await supabase
          .from('admin_users')
          .select('count')
          .limit(1);
        return { data, error, duration: Date.now() - startTime };
      }
    }));

    try {
      const connectionResults = await Promise.allSettled(
        connectionTests.map(test => test.test())
      );

      const successfulConnections = connectionResults.filter(
        result => result.status === 'fulfilled' && !result.value.error
      ).length;

      const averageResponseTime = connectionResults
        .filter(result => result.status === 'fulfilled')
        .reduce((sum, result) => {
          const value = (result as PromiseFulfilledResult<any>).value;
          return sum + value.duration;
        }, 0) / connectionResults.length;

      results.push({
        test: 'Connection Stability',
        operation: 'Concurrent Connections',
        status: successfulConnections === connectionTests.length ? 'pass' : 'warning',
        message: `${successfulConnections}/${connectionTests.length} connections successful, avg response: ${Math.round(averageResponseTime)}ms`,
        duration: Math.round(averageResponseTime),
        details: {
          totalTests: connectionTests.length,
          successfulConnections,
          averageResponseTime: Math.round(averageResponseTime),
          connectionResults: connectionResults.map((result, index) => ({
            test: connectionTests[index].name,
            success: result.status === 'fulfilled' && !result.value.error,
            duration: result.status === 'fulfilled' ? result.value.duration : 0,
            error: result.status === 'rejected' ? result.reason : 
                   result.status === 'fulfilled' && result.value.error ? result.value.error.message : null,
          })),
        },
      });

    } catch (error) {
      results.push({
        test: 'Connection Stability',
        operation: 'Concurrent Connections',
        status: 'fail',
        message: 'Connection stability test failed',
        duration: 0,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }

    return results;
  }

  /**
   * Get performance rating based on execution time
   */
  private getPerformanceRating(duration: number): 'excellent' | 'good' | 'acceptable' | 'poor' {
    if (duration <= this.PERFORMANCE_THRESHOLDS.excellent) return 'excellent';
    if (duration <= this.PERFORMANCE_THRESHOLDS.good) return 'good';
    if (duration <= this.PERFORMANCE_THRESHOLDS.acceptable) return 'acceptable';
    return 'poor';
  }

  /**
   * Get database performance status
   */
  async getDatabasePerformanceStatus(): Promise<DatabasePerformanceStatus> {
    try {
      const [queryResults, indexResults, connectionResults] = await Promise.all([
        this.validateQueryPerformance(),
        this.validateIndexOptimization(),
        this.validateConnectionStability(),
      ]);

      const queryPerformance = queryResults.filter(r => r.status === 'pass').length >= 
                              queryResults.length * 0.8; // 80% pass rate

      const indexOptimization = indexResults.filter(r => r.status === 'pass').length >= 
                               indexResults.length * 0.7; // 70% pass rate

      const connectionStability = connectionResults.every(r => r.status === 'pass');

      const status = {
        queryPerformance,
        indexOptimization,
        connectionStability,
        memoryUsage: true, // Placeholder - would need specific monitoring
        overall: false,
      };

      status.overall = queryPerformance && connectionStability;

      return status;

    } catch (error) {
      console.error('❌ Failed to get database performance status:', error);
      return {
        queryPerformance: false,
        indexOptimization: false,
        connectionStability: false,
        memoryUsage: false,
        overall: false,
      };
    }
  }

  /**
   * Run comprehensive database performance validation
   */
  async runComprehensivePerformanceValidation(): Promise<PerformanceValidationResult[]> {
    console.log('🧪 Running comprehensive database performance validation...');

    const [queryResults, indexResults, connectionResults] = await Promise.all([
      this.validateQueryPerformance(),
      this.validateIndexOptimization(),
      this.validateConnectionStability(),
    ]);

    const allResults = [...queryResults, ...indexResults, ...connectionResults];
    const passedTests = allResults.filter(r => r.status === 'pass').length;
    const totalTests = allResults.length;

    console.log(`✅ Database performance validation completed: ${passedTests}/${totalTests} tests passed`);

    return allResults;
  }

  /**
   * Get performance metrics summary
   */
  getPerformanceMetrics(results: PerformanceValidationResult[]): QueryPerformanceMetrics[] {
    return results
      .filter(r => r.details?.executionTime !== undefined)
      .map(r => ({
        operation: r.operation,
        table: r.details.table || 'multiple',
        executionTime: r.details.executionTime,
        recordCount: r.details.recordCount || 0,
        performanceRating: r.details.performanceRating || 'poor',
      }));
  }
}

// Export singleton instance
export const databasePerformanceValidator = DatabasePerformanceValidator.getInstance();
