/**
 * Ocean Soul Sparkles Mobile App - Email Configuration Service
 * Manages email service configuration and authentication
 */

import { emailService } from './emailService';

export class EmailConfigService {
  private static instance: EmailConfigService;
  private isInitialized = false;

  private constructor() {}

  public static getInstance(): EmailConfigService {
    if (!EmailConfigService.instance) {
      EmailConfigService.instance = new EmailConfigService();
    }
    return EmailConfigService.instance;
  }

  /**
   * Initialize email service with authentication
   */
  async initialize(authToken?: string): Promise<void> {
    if (this.isInitialized) {
      return;
    }

    try {
      console.log('📧 Initializing email service...');

      if (authToken) {
        // Set authentication token for email service
        emailService.setAuthToken(authToken);
        console.log('✅ Email service authenticated with admin portal');
      } else {
        console.warn('⚠️ Email service initialized without authentication');
      }

      this.isInitialized = true;
      console.log('✅ Email service initialized successfully');

    } catch (error) {
      console.error('❌ Failed to initialize email service:', error);
      throw error;
    }
  }

  /**
   * Update email service authentication when user logs in
   */
  updateAuthentication(token: string): void {
    try {
      emailService.setAuthToken(token);
      console.log('✅ Email service authentication updated');
    } catch (error) {
      console.error('❌ Failed to update email service authentication:', error);
    }
  }

  /**
   * Clear email service authentication when user logs out
   */
  clearAuthentication(): void {
    try {
      emailService.setAuthToken('');
      this.isInitialized = false;
      console.log('✅ Email service authentication cleared');
    } catch (error) {
      console.error('❌ Failed to clear email service authentication:', error);
    }
  }

  /**
   * Test email service connectivity
   */
  async testEmailService(): Promise<boolean> {
    try {
      console.log('🧪 Testing email service connectivity...');

      // Try to load email templates as a connectivity test
      const templates = await emailService.getEmailTemplates();
      
      if (templates.length > 0) {
        console.log('✅ Email service connectivity test passed');
        return true;
      } else {
        console.warn('⚠️ Email service connected but no templates found');
        return false;
      }

    } catch (error) {
      console.error('❌ Email service connectivity test failed:', error);
      return false;
    }
  }

  /**
   * Get email service status
   */
  getStatus(hasAuthToken?: boolean): {
    initialized: boolean;
    authenticated: boolean;
    ready: boolean;
  } {
    const authenticated = hasAuthToken || false;

    return {
      initialized: this.isInitialized,
      authenticated,
      ready: this.isInitialized && authenticated,
    };
  }
}

// Export singleton instance
export const emailConfigService = EmailConfigService.getInstance();
