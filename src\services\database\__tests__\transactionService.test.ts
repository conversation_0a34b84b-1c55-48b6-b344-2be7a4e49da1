/**
 * Ocean Soul Sparkles Mobile App - Transaction Service Tests
 * Tests for transaction creation, validation, and database integration
 */

import { transactionService } from '../transactionService';
import { CartItem, Product, Service } from '@/types/database';

// Mock Supabase
const mockInsertData = jest.fn();
jest.mock('../supabase', () => ({
  supabase: {
    from: jest.fn(() => ({
      insert: jest.fn((data) => {
        mockInsertData(data);
        return {
          select: jest.fn(() => ({
            single: jest.fn(() => Promise.resolve({
              data: {
                id: 'test-transaction-id',
                transaction_number: 'OSS241226-001',
                ...data, // Return the data that was inserted
              },
              error: null
            }))
          }))
        };
      }),
      select: jest.fn(() => ({
        eq: jest.fn(() => ({
          single: jest.fn(() => Promise.resolve({
            data: null,
            error: null
          }))
        })),
        gte: jest.fn(() => ({
          lt: jest.fn(() => Promise.resolve({
            count: 0
          }))
        }))
      }))
    }))
  }
}));

describe('TransactionService', () => {
  const mockProduct: Product = {
    id: 'product-1',
    name: 'Test Product',
    description: 'A test product',
    price: 50.00,
    category: 'test',
    stock_quantity: 10,
    is_active: true,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  };

  const mockService: Service = {
    id: 'service-1',
    name: 'Test Service',
    description: 'A test service',
    base_price: 75.00,
    category: 'test',
    duration_minutes: 60,
    is_active: true,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  };

  const mockCartItems: CartItem[] = [
    {
      id: 'cart-item-1',
      type: 'product',
      item_id: mockProduct.id,
      item: mockProduct,
      name: mockProduct.name,
      quantity: 2,
      unit_price: mockProduct.price!,
      total_price: mockProduct.price! * 2,
    },
    {
      id: 'cart-item-2',
      type: 'service',
      item_id: mockService.id,
      item: mockService,
      name: mockService.name,
      quantity: 1,
      unit_price: mockService.base_price!,
      total_price: mockService.base_price!,
    }
  ];

  const mockStaffId = 'staff-123';

  describe('createTransaction', () => {
    it('should create a transaction with correct totals', async () => {
      const result = await transactionService.createTransaction(
        mockCartItems,
        mockStaffId,
        undefined,
        'cash'
      );

      expect(result.error).toBeNull();
      expect(result.data).toBeDefined();
      expect(result.data?.id).toBe('test-transaction-id');
      expect(result.data?.transaction_number).toMatch(/^OSS\d{6}-\d{3}$/);
    });

    it('should calculate tax correctly', async () => {
      const subtotal = mockCartItems.reduce((sum, item) => sum + item.total_price, 0);
      const expectedTax = subtotal * 0.10; // 10% GST
      const expectedTotal = subtotal + expectedTax;

      const result = await transactionService.createTransaction(
        mockCartItems,
        mockStaffId,
        undefined,
        'cash'
      );

      expect(result.error).toBeNull();
      expect(result.data?.subtotal).toBe(175.00); // 100 + 75
      expect(result.data?.tax_amount).toBe(17.50); // 10% of 175
      expect(result.data?.total_amount).toBe(192.50); // 175 + 17.50
    });

    it('should handle customer ID when provided', async () => {
      const customerId = 'customer-123';
      
      const result = await transactionService.createTransaction(
        mockCartItems,
        mockStaffId,
        customerId,
        'card'
      );

      expect(result.error).toBeNull();
      expect(result.data).toBeDefined();
    });

    it('should handle different payment methods', async () => {
      const paymentMethods: Array<'cash' | 'card' | 'digital_wallet'> = ['cash', 'card', 'digital_wallet'];

      for (const method of paymentMethods) {
        const result = await transactionService.createTransaction(
          mockCartItems,
          mockStaffId,
          undefined,
          method
        );

        expect(result.error).toBeNull();
        expect(result.data).toBeDefined();
      }
    });

    it('should handle empty cart', async () => {
      const result = await transactionService.createTransaction(
        [],
        mockStaffId,
        undefined,
        'cash'
      );

      expect(result.error).toBeNull();
      expect(result.data?.subtotal).toBe(0);
      expect(result.data?.tax_amount).toBe(0);
      expect(result.data?.total_amount).toBe(0);
    });
  });

  describe('generateReceipt', () => {
    it('should generate receipt with correct business info', () => {
      const mockTransaction = {
        id: 'test-transaction-id',
        transaction_number: 'OSS241226-001',
        subtotal: 100.00,
        tax_amount: 10.00,
        total_amount: 110.00,
        payment_method: 'cash' as const,
        payment_status: 'completed' as const,
        staff_id: mockStaffId,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };

      const receipt = transactionService.generateReceipt(mockTransaction);

      expect(receipt.transaction).toBe(mockTransaction);
      expect(receipt.business_info.name).toBe('Ocean Soul Sparkles');
      expect(receipt.business_info.email).toBe('<EMAIL>');
      expect(receipt.receipt_number).toBe(mockTransaction.transaction_number);
      expect(receipt.generated_at).toBeDefined();
    });
  });

  describe('validation', () => {
    it('should validate cart items have required fields', () => {
      const invalidCartItem = {
        id: 'invalid-item',
        type: 'product' as const,
        // Missing required fields
      };

      // This would be caught by TypeScript, but we can test runtime validation
      expect(() => {
        // @ts-ignore - Testing invalid input
        transactionService.createTransaction([invalidCartItem], mockStaffId);
      }).not.toThrow(); // Service should handle gracefully
    });

    it('should handle missing staff ID', async () => {
      const result = await transactionService.createTransaction(
        mockCartItems,
        '', // Empty staff ID
        undefined,
        'cash'
      );

      // Should still work but might have validation issues
      expect(result).toBeDefined();
    });
  });
});
