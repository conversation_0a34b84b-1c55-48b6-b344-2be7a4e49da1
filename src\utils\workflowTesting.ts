/**
 * Ocean Soul Sparkles Mobile App - Enhanced Booking Workflow Testing
 * Comprehensive testing utilities for the complete booking-to-quote workflow
 */

import { websiteBookingService, WebsiteBookingData } from '@/services/booking/websiteBookingService';
import { distancePricingService } from '@/services/pricing/distancePricingService';
import { bookingWorkflowService, AutoQuoteConfig } from '@/services/workflow/bookingWorkflowService';
import { emailTesting } from './emailTesting';

export interface WorkflowTestResult {
  test: string;
  success: boolean;
  message: string;
  duration: number;
  details?: any;
  error?: string;
}

export interface WorkflowTestSuite {
  websiteBooking: WorkflowTestResult[];
  distancePricing: WorkflowTestResult[];
  automatedWorkflow: WorkflowTestResult[];
  emailIntegration: WorkflowTestResult[];
  summary: {
    totalTests: number;
    passedTests: number;
    failedTests: number;
    successRate: number;
    totalDuration: number;
  };
}

export class WorkflowTestingService {
  /**
   * Test website booking processing
   */
  static async testWebsiteBookingProcessing(): Promise<WorkflowTestResult[]> {
    const results: WorkflowTestResult[] = [];

    // Test 1: Valid website booking
    const startTime1 = Date.now();
    try {
      const validBookingData: WebsiteBookingData = {
        customer_email: '<EMAIL>',
        customer_name: 'Test Customer',
        customer_phone: '+61 400 000 000',
        customer_address: '123 Test Street, Sydney, NSW 2000',
        service_name: 'Facial Treatment',
        service_category: 'Beauty',
        booking_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        start_time: '10:00',
        end_time: '11:00',
        notes: 'Test booking for workflow validation',
        source: 'website',
        website_booking_id: 'TEST-' + Date.now(),
        estimated_duration: 60,
        special_requests: 'Please test the automated workflow',
      };

      const result = await websiteBookingService.processWebsiteBooking(validBookingData);
      
      results.push({
        test: 'Valid Website Booking Processing',
        success: result.success,
        message: result.success 
          ? `Booking processed successfully${result.requiresReview ? ' (requires review)' : ''}`
          : result.error || 'Processing failed',
        duration: Date.now() - startTime1,
        details: {
          bookingId: result.booking?.id,
          customerId: result.customer?.id,
          serviceId: result.service?.id,
          requiresReview: result.requiresReview,
          reviewReasons: result.reviewReasons,
        },
      });

    } catch (error) {
      results.push({
        test: 'Valid Website Booking Processing',
        success: false,
        message: 'Test execution failed',
        duration: Date.now() - startTime1,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }

    // Test 2: Invalid booking data
    const startTime2 = Date.now();
    try {
      const invalidBookingData: WebsiteBookingData = {
        customer_email: '', // Invalid email
        customer_name: '',  // Invalid name
        service_name: '',   // Invalid service
        booking_date: '',   // Invalid date
        start_time: '',     // Invalid time
        source: 'website',
      };

      const result = await websiteBookingService.processWebsiteBooking(invalidBookingData);
      
      results.push({
        test: 'Invalid Website Booking Validation',
        success: !result.success, // Should fail validation
        message: !result.success 
          ? 'Validation correctly rejected invalid data'
          : 'Validation failed to catch invalid data',
        duration: Date.now() - startTime2,
        details: {
          error: result.error,
          requiresReview: result.requiresReview,
        },
      });

    } catch (error) {
      results.push({
        test: 'Invalid Website Booking Validation',
        success: true, // Exception is expected for invalid data
        message: 'Validation correctly threw exception for invalid data',
        duration: Date.now() - startTime2,
        details: { error: error instanceof Error ? error.message : 'Unknown error' },
      });
    }

    return results;
  }

  /**
   * Test distance-based pricing calculations
   */
  static async testDistancePricingCalculations(): Promise<WorkflowTestResult[]> {
    const results: WorkflowTestResult[] = [];

    // Test 1: Local distance pricing
    const startTime1 = Date.now();
    try {
      const localLocation = {
        address: '100 George Street, Sydney, NSW 2000',
        city: 'Sydney',
        state: 'NSW',
        postal_code: '2000',
      };

      const mockService = {
        id: 'test-service',
        name: 'Test Service',
        base_price: 100,
        duration_minutes: 60,
        category: 'Test',
        description: 'Test service',
        is_active: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };

      const pricingResult = await distancePricingService.calculatePricing(mockService, localLocation);
      
      results.push({
        test: 'Local Distance Pricing',
        success: pricingResult.success,
        message: pricingResult.success 
          ? `Pricing calculated: $${pricingResult.total_price.toFixed(2)} (${pricingResult.distance_km}km)`
          : pricingResult.error || 'Pricing calculation failed',
        duration: Date.now() - startTime1,
        details: {
          basePrice: pricingResult.base_price,
          distance: pricingResult.distance_km,
          tier: pricingResult.pricing_tier?.name,
          totalPrice: pricingResult.total_price,
          breakdown: pricingResult.breakdown,
        },
      });

    } catch (error) {
      results.push({
        test: 'Local Distance Pricing',
        success: false,
        message: 'Pricing calculation test failed',
        duration: Date.now() - startTime1,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }

    // Test 2: Regional distance pricing
    const startTime2 = Date.now();
    try {
      const regionalLocation = {
        address: 'Newcastle, NSW',
        city: 'Newcastle',
        state: 'NSW',
        postal_code: '2300',
      };

      const mockService = {
        id: 'test-service',
        name: 'Test Service',
        base_price: 150,
        duration_minutes: 90,
        category: 'Test',
        description: 'Test service',
        is_active: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };

      const pricingResult = await distancePricingService.calculatePricing(mockService, regionalLocation);
      
      results.push({
        test: 'Regional Distance Pricing',
        success: pricingResult.success,
        message: pricingResult.success 
          ? `Regional pricing: $${pricingResult.total_price.toFixed(2)} (${pricingResult.distance_km}km, ${pricingResult.pricing_tier.name})`
          : pricingResult.error || 'Regional pricing failed',
        duration: Date.now() - startTime2,
        details: {
          basePrice: pricingResult.base_price,
          distance: pricingResult.distance_km,
          tier: pricingResult.pricing_tier?.name,
          totalPrice: pricingResult.total_price,
          travelFee: pricingResult.travel_fee,
        },
      });

    } catch (error) {
      results.push({
        test: 'Regional Distance Pricing',
        success: false,
        message: 'Regional pricing test failed',
        duration: Date.now() - startTime2,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }

    return results;
  }

  /**
   * Test automated workflow processing
   */
  static async testAutomatedWorkflow(): Promise<WorkflowTestResult[]> {
    const results: WorkflowTestResult[] = [];

    // Test 1: Complete automated workflow (simulation)
    const startTime1 = Date.now();
    try {
      const testBookingData: WebsiteBookingData = {
        customer_email: '<EMAIL>',
        customer_name: 'Workflow Test Customer',
        customer_phone: '+61 400 111 222',
        customer_address: '456 Test Avenue, Sydney, NSW 2001',
        service_name: 'Automated Test Service',
        booking_date: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        start_time: '14:00',
        source: 'website',
        website_booking_id: 'AUTO-TEST-' + Date.now(),
      };

      const autoQuoteConfig: AutoQuoteConfig = {
        enabled: true,
        maxDistanceKm: 50,
        requiresApproval: false,
        autoSendEmail: false, // Disable email for testing
        staffId: 'test-staff-id',
      };

      // Note: This will likely fail due to missing database records, but tests the workflow logic
      const workflowResult = await bookingWorkflowService.processWebsiteBookingWorkflow(
        testBookingData,
        autoQuoteConfig
      );
      
      results.push({
        test: 'Automated Workflow Processing',
        success: workflowResult.success || workflowResult.requiresManualReview,
        message: workflowResult.success 
          ? 'Workflow completed successfully'
          : workflowResult.requiresManualReview 
            ? `Workflow requires manual review: ${workflowResult.reviewReasons.join(', ')}`
            : workflowResult.error || 'Workflow failed',
        duration: Date.now() - startTime1,
        details: {
          steps: workflowResult.steps,
          requiresReview: workflowResult.requiresManualReview,
          reviewReasons: workflowResult.reviewReasons,
          bookingId: workflowResult.booking?.id,
          quoteId: workflowResult.quote?.id,
        },
      });

    } catch (error) {
      results.push({
        test: 'Automated Workflow Processing',
        success: false,
        message: 'Workflow test execution failed',
        duration: Date.now() - startTime1,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }

    return results;
  }

  /**
   * Test email integration with workflow
   */
  static async testEmailIntegration(): Promise<WorkflowTestResult[]> {
    const results: WorkflowTestResult[] = [];

    // Test 1: Email system status
    const startTime1 = Date.now();
    try {
      const emailStatus = await emailTesting.getEmailSystemStatus();
      
      results.push({
        test: 'Email System Integration',
        success: emailStatus.overall,
        message: emailStatus.overall 
          ? 'Email system ready for workflow integration'
          : 'Email system has issues that may affect workflow',
        duration: Date.now() - startTime1,
        details: emailStatus,
      });

    } catch (error) {
      results.push({
        test: 'Email System Integration',
        success: false,
        message: 'Email system test failed',
        duration: Date.now() - startTime1,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }

    // Test 2: Email template availability
    const startTime2 = Date.now();
    try {
      const emailTests = await emailTesting.testQuoteEmailFunctionality();
      const templateTest = emailTests.find(t => t.test === 'Quote Template Processing');
      
      results.push({
        test: 'Email Template Integration',
        success: templateTest?.success || false,
        message: templateTest?.success 
          ? 'Email templates ready for automated workflow'
          : 'Email template issues may affect automated notifications',
        duration: Date.now() - startTime2,
        details: templateTest?.details,
      });

    } catch (error) {
      results.push({
        test: 'Email Template Integration',
        success: false,
        message: 'Email template test failed',
        duration: Date.now() - startTime2,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }

    return results;
  }

  /**
   * Run complete workflow test suite
   */
  static async runCompleteWorkflowTest(): Promise<WorkflowTestSuite> {
    console.log('🧪 Running complete enhanced booking workflow test suite...');

    const startTime = Date.now();

    const websiteBooking = await this.testWebsiteBookingProcessing();
    const distancePricing = await this.testDistancePricingCalculations();
    const automatedWorkflow = await this.testAutomatedWorkflow();
    const emailIntegration = await this.testEmailIntegration();

    const allTests = [...websiteBooking, ...distancePricing, ...automatedWorkflow, ...emailIntegration];
    const passedTests = allTests.filter(t => t.success).length;
    const failedTests = allTests.length - passedTests;
    const successRate = allTests.length > 0 ? (passedTests / allTests.length) * 100 : 0;
    const totalDuration = Date.now() - startTime;

    const summary = {
      totalTests: allTests.length,
      passedTests,
      failedTests,
      successRate: Math.round(successRate * 100) / 100,
      totalDuration,
    };

    console.log(`✅ Workflow test suite completed: ${passedTests}/${allTests.length} tests passed (${summary.successRate}%)`);

    return {
      websiteBooking,
      distancePricing,
      automatedWorkflow,
      emailIntegration,
      summary,
    };
  }

  /**
   * Generate workflow test report
   */
  static generateTestReport(testSuite: WorkflowTestSuite): string {
    const lines = ['# Enhanced Booking Workflow Test Report'];
    lines.push('');
    lines.push(`**Test Execution Date:** ${new Date().toISOString()}`);
    lines.push(`**Total Duration:** ${testSuite.summary.totalDuration}ms`);
    lines.push(`**Success Rate:** ${testSuite.summary.successRate}%`);
    lines.push('');

    // Summary
    lines.push('## Summary');
    lines.push(`- **Total Tests:** ${testSuite.summary.totalTests}`);
    lines.push(`- **Passed:** ${testSuite.summary.passedTests} ✅`);
    lines.push(`- **Failed:** ${testSuite.summary.failedTests} ❌`);
    lines.push('');

    // Website Booking Tests
    lines.push('## Website Booking Processing');
    testSuite.websiteBooking.forEach(test => {
      lines.push(`- **${test.test}:** ${test.success ? '✅' : '❌'} ${test.message} (${test.duration}ms)`);
    });
    lines.push('');

    // Distance Pricing Tests
    lines.push('## Distance-Based Pricing');
    testSuite.distancePricing.forEach(test => {
      lines.push(`- **${test.test}:** ${test.success ? '✅' : '❌'} ${test.message} (${test.duration}ms)`);
    });
    lines.push('');

    // Automated Workflow Tests
    lines.push('## Automated Workflow');
    testSuite.automatedWorkflow.forEach(test => {
      lines.push(`- **${test.test}:** ${test.success ? '✅' : '❌'} ${test.message} (${test.duration}ms)`);
    });
    lines.push('');

    // Email Integration Tests
    lines.push('## Email Integration');
    testSuite.emailIntegration.forEach(test => {
      lines.push(`- **${test.test}:** ${test.success ? '✅' : '❌'} ${test.message} (${test.duration}ms)`);
    });
    lines.push('');

    // Recommendations
    lines.push('## Recommendations');
    if (testSuite.summary.successRate >= 80) {
      lines.push('✅ **Workflow is ready for production use**');
    } else if (testSuite.summary.successRate >= 60) {
      lines.push('⚠️ **Workflow needs minor fixes before production**');
    } else {
      lines.push('❌ **Workflow requires significant fixes before production**');
    }

    const failedTests = [
      ...testSuite.websiteBooking,
      ...testSuite.distancePricing,
      ...testSuite.automatedWorkflow,
      ...testSuite.emailIntegration,
    ].filter(t => !t.success);

    if (failedTests.length > 0) {
      lines.push('');
      lines.push('### Failed Tests to Address:');
      failedTests.forEach(test => {
        lines.push(`- ${test.test}: ${test.error || test.message}`);
      });
    }

    return lines.join('\n');
  }
}

// Export for easy access
export const workflowTesting = WorkflowTestingService;
