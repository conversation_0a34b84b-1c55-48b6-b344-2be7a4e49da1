/**
 * Ocean Soul Sparkles Mobile App - Database Connection Test
 * Tests the actual database connection and schema
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

const SUPABASE_URL = process.env.EXPO_PUBLIC_SUPABASE_URL;
const SUPABASE_ANON_KEY = process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY;

console.log('🔍 Testing database connection...');
console.log('Supabase URL:', SUPABASE_URL);
console.log('Anon Key:', SUPABASE_ANON_KEY ? 'Present' : 'Missing');

if (!SUPABASE_URL || !SUPABASE_ANON_KEY) {
  console.error('❌ Missing Supabase configuration');
  process.exit(1);
}

const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

async function testDatabaseConnection() {
  try {
    console.log('\n📋 Testing quotes table...');
    
    // Test 1: Check if quotes table exists and get its structure
    const { data: quotesData, error: quotesError } = await supabase
      .from('quotes')
      .select('*')
      .limit(1);

    if (quotesError) {
      console.error('❌ Quotes table error:', quotesError.message);
      console.log('Error details:', quotesError);
    } else {
      console.log('✅ Quotes table accessible');
      if (quotesData && quotesData.length > 0) {
        console.log('\n📊 Quote object structure:');
        const quote = quotesData[0];
        Object.keys(quote).forEach(key => {
          console.log(`  ${key}: ${typeof quote[key]} = ${quote[key]}`);
        });
        
        // Check specifically for amount-related properties
        console.log('\n💰 Amount-related properties:');
        const amountProps = Object.keys(quote).filter(key => 
          key.includes('amount') || key.includes('price') || key.includes('total')
        );
        amountProps.forEach(prop => {
          console.log(`  ✓ ${prop}: ${quote[prop]}`);
        });
        
        if (amountProps.length === 0) {
          console.log('  ❌ No amount-related properties found!');
        }
      } else {
        console.log('📝 No quotes in database, testing with insert...');
        
        // Try to insert a test quote to see what properties are expected
        const testQuote = {
          customer_id: '00000000-0000-0000-0000-000000000000',
          title: 'Test Quote',
          description: 'Testing database schema',
          total_amount: 100.00,
          status: 'draft'
        };
        
        const { data: insertData, error: insertError } = await supabase
          .from('quotes')
          .insert([testQuote])
          .select('*');
        
        if (insertError) {
          console.log('❌ Insert with total_amount failed:', insertError.message);
          
          // Try with different property names
          const testQuote2 = {
            customer_id: '00000000-0000-0000-0000-000000000000',
            title: 'Test Quote 2',
            description: 'Testing database schema',
            amount: 100.00,
            status: 'draft'
          };
          
          const { data: insertData2, error: insertError2 } = await supabase
            .from('quotes')
            .insert([testQuote2])
            .select('*');
          
          if (insertError2) {
            console.log('❌ Insert with amount failed:', insertError2.message);
          } else {
            console.log('✅ Insert with amount succeeded');
            console.log('Quote structure:', insertData2[0]);
            // Clean up
            await supabase.from('quotes').delete().eq('id', insertData2[0].id);
          }
        } else {
          console.log('✅ Insert with total_amount succeeded');
          console.log('Quote structure:', insertData[0]);
          // Clean up
          await supabase.from('quotes').delete().eq('id', insertData[0].id);
        }
      }
    }

    // Test 2: Check customers table
    console.log('\n👥 Testing customers table...');
    const { data: customersData, error: customersError } = await supabase
      .from('customers')
      .select('*')
      .limit(1);

    if (customersError) {
      console.error('❌ Customers table error:', customersError.message);
    } else {
      console.log('✅ Customers table accessible');
      if (customersData && customersData.length > 0) {
        console.log('Customer structure:', Object.keys(customersData[0]));
      }
    }

    // Test 3: Check bookings table
    console.log('\n📅 Testing bookings table...');
    const { data: bookingsData, error: bookingsError } = await supabase
      .from('bookings')
      .select('*')
      .limit(1);

    if (bookingsError) {
      console.error('❌ Bookings table error:', bookingsError.message);
    } else {
      console.log('✅ Bookings table accessible');
      if (bookingsData && bookingsData.length > 0) {
        console.log('Booking structure:', Object.keys(bookingsData[0]));
      }
    }

    // Test 4: Check services table
    console.log('\n🛠️ Testing services table...');
    const { data: servicesData, error: servicesError } = await supabase
      .from('services')
      .select('*')
      .limit(1);

    if (servicesError) {
      console.error('❌ Services table error:', servicesError.message);
    } else {
      console.log('✅ Services table accessible');
      if (servicesData && servicesData.length > 0) {
        console.log('Service structure:', Object.keys(servicesData[0]));
      }
    }

  } catch (error) {
    console.error('❌ Database test failed:', error);
  }
}

async function testAppRegistration() {
  console.log('\n📱 Testing app registration components...');
  
  try {
    // Check if we can require the App component
    const App = require('../App');
    console.log('✅ App component loadable:', !!App);
    console.log('App export type:', typeof App.default);
    
    // Check if expo is available
    try {
      const expo = require('expo');
      console.log('✅ Expo module available');
      
      const { registerRootComponent } = require('expo');
      console.log('✅ registerRootComponent available:', typeof registerRootComponent);
    } catch (err) {
      console.log('❌ Expo modules not available:', err.message);
    }
    
  } catch (error) {
    console.error('❌ App registration test failed:', error.message);
  }
}

// Run tests
async function runAllTests() {
  console.log('🚨 Ocean Soul Sparkles - Database & App Registration Test');
  console.log('='.repeat(60));
  
  await testDatabaseConnection();
  await testAppRegistration();
  
  console.log('\n✅ All tests completed');
}

runAllTests().catch(console.error);
