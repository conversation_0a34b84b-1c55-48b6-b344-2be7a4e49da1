/**
 * Ocean Soul Sparkles Mobile App - Website Booking Reception Service
 * Handles automated booking reception from website and integration with mobile workflow
 */

import { supabase } from '@/services/database/supabase';
import { bookingService } from '@/services/database/bookingService';
import { customerService } from '@/services/database/customerService';
import { serviceService } from '@/services/database/serviceService';
import { Booking, Customer, Service, DatabaseResponse } from '@/types/database';
import Constants from 'expo-constants';

// Environment variables
const API_BASE_URL = Constants.expoConfig?.extra?.EXPO_PUBLIC_API_BASE_URL || process.env.EXPO_PUBLIC_API_BASE_URL;

export interface WebsiteBookingData {
  customer_email: string;
  customer_name: string;
  customer_phone?: string;
  customer_address?: string;
  service_name: string;
  service_category?: string;
  booking_date: string;
  start_time: string;
  end_time?: string;
  notes?: string;
  source: 'website' | 'phone' | 'walk_in' | 'referral';
  website_booking_id?: string;
  estimated_duration?: number;
  special_requests?: string;
}

export interface BookingReceptionResult {
  success: boolean;
  booking?: Booking;
  customer?: Customer;
  service?: Service;
  error?: string;
  requiresReview?: boolean;
  reviewReasons?: string[];
}

export class WebsiteBookingService {
  private static instance: WebsiteBookingService;

  private constructor() {}

  public static getInstance(): WebsiteBookingService {
    if (!WebsiteBookingService.instance) {
      WebsiteBookingService.instance = new WebsiteBookingService();
    }
    return WebsiteBookingService.instance;
  }

  /**
   * Process incoming booking from website
   */
  async processWebsiteBooking(websiteData: WebsiteBookingData): Promise<BookingReceptionResult> {
    try {
      console.log('🌐 Processing website booking...');

      // Step 1: Find or create customer
      const customerResult = await this.findOrCreateCustomer(websiteData);
      if (!customerResult.success || !customerResult.customer) {
        return {
          success: false,
          error: customerResult.error || 'Failed to process customer data',
        };
      }

      // Step 2: Find or create service
      const serviceResult = await this.findOrCreateService(websiteData);
      if (!serviceResult.success || !serviceResult.service) {
        return {
          success: false,
          error: serviceResult.error || 'Failed to process service data',
        };
      }

      // Step 3: Validate booking data
      const validationResult = this.validateBookingData(websiteData);
      if (!validationResult.isValid) {
        return {
          success: false,
          error: validationResult.error,
          requiresReview: true,
          reviewReasons: validationResult.issues,
        };
      }

      // Step 4: Create booking
      const bookingResult = await this.createBookingFromWebsite(
        websiteData,
        customerResult.customer,
        serviceResult.service
      );

      if (!bookingResult.success || !bookingResult.booking) {
        return {
          success: false,
          error: bookingResult.error || 'Failed to create booking',
        };
      }

      console.log('✅ Website booking processed successfully');
      return {
        success: true,
        booking: bookingResult.booking,
        customer: customerResult.customer,
        service: serviceResult.service,
        requiresReview: validationResult.requiresReview,
        reviewReasons: validationResult.issues,
      };

    } catch (error) {
      console.error('❌ Website booking processing error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error processing website booking',
      };
    }
  }

  /**
   * Find existing customer or create new one
   */
  private async findOrCreateCustomer(websiteData: WebsiteBookingData): Promise<{
    success: boolean;
    customer?: Customer;
    error?: string;
  }> {
    try {
      // First, try to find existing customer by email
      const existingCustomers = await customerService.getCustomers({
        search: websiteData.customer_email,
        limit: 1,
      });

      if (existingCustomers.data && existingCustomers.data.length > 0) {
        const existingCustomer = existingCustomers.data[0];
        console.log('✅ Found existing customer:', existingCustomer.email);
        
        // Update customer info if needed
        const updatedCustomer = await this.updateCustomerIfNeeded(existingCustomer, websiteData);
        return {
          success: true,
          customer: updatedCustomer || existingCustomer,
        };
      }

      // Create new customer
      console.log('📝 Creating new customer from website booking...');
      const newCustomerResult = await customerService.createCustomer({
        email: websiteData.customer_email,
        full_name: websiteData.customer_name,
        phone: websiteData.customer_phone,
        address: websiteData.customer_address,
        notes: `Customer created from website booking - ${websiteData.source}`,
      });

      if (newCustomerResult.error || !newCustomerResult.data) {
        return {
          success: false,
          error: newCustomerResult.error?.message || 'Failed to create customer',
        };
      }

      console.log('✅ New customer created:', newCustomerResult.data.email);
      return {
        success: true,
        customer: newCustomerResult.data,
      };

    } catch (error) {
      console.error('❌ Customer processing error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to process customer',
      };
    }
  }

  /**
   * Update existing customer with new information if needed
   */
  private async updateCustomerIfNeeded(
    existingCustomer: Customer,
    websiteData: WebsiteBookingData
  ): Promise<Customer | null> {
    try {
      const needsUpdate = 
        (websiteData.customer_phone && !existingCustomer.phone) ||
        (websiteData.customer_address && !existingCustomer.address) ||
        (existingCustomer.full_name !== websiteData.customer_name);

      if (!needsUpdate) {
        return null;
      }

      console.log('📝 Updating existing customer with new information...');
      const updateResult = await customerService.updateCustomer(existingCustomer.id, {
        full_name: websiteData.customer_name,
        phone: websiteData.customer_phone || existingCustomer.phone,
        address: websiteData.customer_address || existingCustomer.address,
        notes: existingCustomer.notes 
          ? `${existingCustomer.notes}\nUpdated from website booking - ${new Date().toISOString()}`
          : `Updated from website booking - ${new Date().toISOString()}`,
      });

      if (updateResult.error || !updateResult.data) {
        console.warn('⚠️ Failed to update customer:', updateResult.error);
        return null;
      }

      return updateResult.data;
    } catch (error) {
      console.warn('⚠️ Customer update error:', error);
      return null;
    }
  }

  /**
   * Find existing service or create placeholder
   */
  private async findOrCreateService(websiteData: WebsiteBookingData): Promise<{
    success: boolean;
    service?: Service;
    error?: string;
  }> {
    try {
      // Try to find existing service by name
      const existingServices = await serviceService.getServices({
        search: websiteData.service_name,
        limit: 5,
      });

      if (existingServices.data && existingServices.data.length > 0) {
        // Find exact match or closest match
        const exactMatch = existingServices.data.find(
          s => s.name.toLowerCase() === websiteData.service_name.toLowerCase()
        );
        
        if (exactMatch) {
          console.log('✅ Found exact service match:', exactMatch.name);
          return {
            success: true,
            service: exactMatch,
          };
        }

        // Use first result as closest match
        console.log('✅ Using closest service match:', existingServices.data[0].name);
        return {
          success: true,
          service: existingServices.data[0],
        };
      }

      // Create placeholder service for manual review
      console.log('📝 Creating placeholder service for manual review...');
      const newServiceResult = await serviceService.createService({
        name: websiteData.service_name,
        description: `Service created from website booking - requires manual review and pricing`,
        category: websiteData.service_category || 'Website Booking',
        base_price: 0, // Will be set during review
        duration_minutes: websiteData.estimated_duration || 60,
        is_active: false, // Inactive until reviewed
      });

      if (newServiceResult.error || !newServiceResult.data) {
        return {
          success: false,
          error: newServiceResult.error?.message || 'Failed to create service placeholder',
        };
      }

      console.log('✅ Placeholder service created:', newServiceResult.data.name);
      return {
        success: true,
        service: newServiceResult.data,
      };

    } catch (error) {
      console.error('❌ Service processing error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to process service',
      };
    }
  }

  /**
   * Validate booking data and determine if manual review is needed
   */
  private validateBookingData(websiteData: WebsiteBookingData): {
    isValid: boolean;
    requiresReview: boolean;
    issues: string[];
    error?: string;
  } {
    const issues: string[] = [];
    let requiresReview = false;

    // Check required fields
    if (!websiteData.customer_email || !websiteData.customer_name) {
      return {
        isValid: false,
        requiresReview: true,
        issues: ['Missing required customer information'],
        error: 'Customer email and name are required',
      };
    }

    if (!websiteData.service_name || !websiteData.booking_date || !websiteData.start_time) {
      return {
        isValid: false,
        requiresReview: true,
        issues: ['Missing required booking information'],
        error: 'Service name, booking date, and start time are required',
      };
    }

    // Check date validity
    const bookingDate = new Date(websiteData.booking_date);
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    if (bookingDate < today) {
      issues.push('Booking date is in the past');
      requiresReview = true;
    }

    // Check if booking is too far in the future (more than 6 months)
    const sixMonthsFromNow = new Date();
    sixMonthsFromNow.setMonth(sixMonthsFromNow.getMonth() + 6);

    if (bookingDate > sixMonthsFromNow) {
      issues.push('Booking date is more than 6 months in the future');
      requiresReview = true;
    }

    // Check for special requests that need attention
    if (websiteData.special_requests && websiteData.special_requests.length > 0) {
      issues.push('Customer has special requests');
      requiresReview = true;
    }

    // Check for missing location information (needed for distance pricing)
    if (!websiteData.customer_address) {
      issues.push('Missing customer address for distance-based pricing');
      requiresReview = true;
    }

    return {
      isValid: true,
      requiresReview,
      issues,
    };
  }

  /**
   * Create booking from website data
   */
  private async createBookingFromWebsite(
    websiteData: WebsiteBookingData,
    customer: Customer,
    service: Service
  ): Promise<{
    success: boolean;
    booking?: Booking;
    error?: string;
  }> {
    try {
      const bookingData: Partial<Booking> = {
        customer_id: customer.id,
        service_id: service.id,
        booking_date: websiteData.booking_date,
        start_time: websiteData.start_time,
        end_time: websiteData.end_time,
        status: 'pending', // All website bookings start as pending
        notes: this.buildBookingNotes(websiteData),
      };

      const result = await bookingService.createBooking(bookingData);

      if (result.error || !result.data) {
        return {
          success: false,
          error: result.error?.message || 'Failed to create booking',
        };
      }

      return {
        success: true,
        booking: result.data,
      };

    } catch (error) {
      console.error('❌ Booking creation error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to create booking',
      };
    }
  }

  /**
   * Build comprehensive booking notes from website data
   */
  private buildBookingNotes(websiteData: WebsiteBookingData): string {
    const notes: string[] = [];
    
    notes.push(`📱 Booking Source: ${websiteData.source}`);
    
    if (websiteData.website_booking_id) {
      notes.push(`🌐 Website Booking ID: ${websiteData.website_booking_id}`);
    }
    
    if (websiteData.special_requests) {
      notes.push(`⭐ Special Requests: ${websiteData.special_requests}`);
    }
    
    if (websiteData.notes) {
      notes.push(`📝 Customer Notes: ${websiteData.notes}`);
    }
    
    notes.push(`⏰ Created: ${new Date().toISOString()}`);
    notes.push(`🔄 Status: Requires quote generation and review`);
    
    return notes.join('\n');
  }

  /**
   * Get pending website bookings that need review
   */
  async getPendingWebsiteBookings(): Promise<DatabaseResponse<Booking[]>> {
    try {
      const result = await bookingService.getBookings({
        filters: {
          status: 'pending',
        },
        order_by: 'created_at',
        order_direction: 'desc',
      });

      if (result.error) {
        return { data: null, error: result.error };
      }

      // Filter for website bookings (those with source information in notes)
      const websiteBookings = (result.data || []).filter(booking => 
        booking.notes?.includes('📱 Booking Source:') || 
        booking.notes?.includes('🌐 Website Booking ID:')
      );

      return { data: websiteBookings, error: null };

    } catch (error) {
      console.error('❌ Failed to get pending website bookings:', error);
      return { 
        data: null, 
        error: error instanceof Error ? error : new Error('Failed to get pending bookings') 
      };
    }
  }
}

// Export singleton instance
export const websiteBookingService = WebsiteBookingService.getInstance();
