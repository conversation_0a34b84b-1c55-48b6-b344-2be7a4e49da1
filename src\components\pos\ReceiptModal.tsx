/**
 * Ocean Soul Sparkles Mobile App - Receipt Modal Component
 * Displays digital receipts with Ocean Soul Sparkles branding
 */

import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  TouchableOpacity,
  ScrollView,
  Share,
  Alert,
} from 'react-native';
import { Receipt } from '@/types/database';

interface ReceiptModalProps {
  visible: boolean;
  onClose: () => void;
  receipt: Receipt | null;
}

const ReceiptModal: React.FC<ReceiptModalProps> = ({
  visible,
  onClose,
  receipt,
}) => {
  if (!receipt) return null;

  const { transaction, business_info, generated_at, receipt_number } = receipt;

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('en-AU', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const formatPrice = (price: number) => {
    return `$${price.toFixed(2)}`;
  };

  // Helper function to safely call toUpperCase
  const safeToUpperCase = (value: any): string => {
    if (!value) return '';
    if (typeof value !== 'string') return String(value).toUpperCase();
    return value.toUpperCase();
  };

  const generateReceiptText = () => {
    const lines = [
      '🌊 OCEAN SOUL SPARKLES 🌊',
      business_info.address,
      `Phone: ${business_info.phone}`,
      `Email: ${business_info.email}`,
      business_info.website ? `Web: ${business_info.website}` : '',
      business_info.abn ? `ABN: ${business_info.abn}` : '',
      '',
      '═══════════════════════════════',
      '           RECEIPT',
      '═══════════════════════════════',
      '',
      `Receipt #: ${receipt_number}`,
      `Date: ${formatDate(transaction.created_at)}`,
      `Staff: ${transaction.staff?.first_name} ${transaction.staff?.last_name}`,
      transaction.customer ? `Customer: ${transaction.customer.full_name}` : '',
      '',
      '───────────────────────────────',
      'ITEMS',
      '───────────────────────────────',
    ];

    // Add transaction items
    transaction.transaction_items?.forEach(item => {
      lines.push(`${item.item_name}`);
      lines.push(`  ${item.quantity} × ${formatPrice(item.unit_price)} = ${formatPrice(item.total_price)}`);
      if (item.notes) {
        lines.push(`  Note: ${item.notes}`);
      }
      lines.push('');
    });

    lines.push('───────────────────────────────');
    if (transaction.total_amount) {
      lines.push(`Subtotal:        ${formatPrice(transaction.subtotal)}`);
      lines.push(`GST (10%):       ${formatPrice(transaction.tax_amount)}`);
      lines.push('═══════════════════════════════');
      lines.push(`TOTAL:           ${formatPrice(transaction.total_amount)}`);
    } else {
      lines.push(`Points Earned:   ${transaction.points || 0} pts`);
      lines.push('═══════════════════════════════');
      lines.push(`LOYALTY POINTS:  ${transaction.points || 0} pts`);
    }
    lines.push('═══════════════════════════════');
    lines.push('');
    lines.push(`Payment: ${safeToUpperCase(transaction.payment_method || 'LOYALTY TRANSACTION')}`);
    lines.push(`Status: ${safeToUpperCase(transaction.payment_status || transaction.transaction_type || 'COMPLETED')}`);
    lines.push('');
    lines.push('Thank you for choosing');
    lines.push('Ocean Soul Sparkles! 🌊✨');
    lines.push('');
    lines.push(`Generated: ${formatDate(generated_at)}`);

    return lines.filter(line => line !== null).join('\n');
  };

  const handleShare = async () => {
    try {
      const receiptText = generateReceiptText();
      await Share.share({
        message: receiptText,
        title: `Ocean Soul Sparkles Receipt - ${receipt_number}`,
      });
    } catch (error) {
      Alert.alert('Error', 'Failed to share receipt');
    }
  };

  const handleEmail = () => {
    // TODO: Implement email functionality in future update
    Alert.alert(
      'Email Receipt',
      'Email functionality will be available in a future update.',
      [{ text: 'OK' }]
    );
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <View style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <Text style={styles.closeButtonText}>Close</Text>
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Receipt</Text>
          <TouchableOpacity onPress={handleShare} style={styles.shareButton}>
            <Text style={styles.shareButtonText}>Share</Text>
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          <View style={styles.receipt}>
            {/* Business Header */}
            <View style={styles.businessHeader}>
              <Text style={styles.businessName}>🌊 OCEAN SOUL SPARKLES 🌊</Text>
              <Text style={styles.businessInfo}>{business_info.address}</Text>
              <Text style={styles.businessInfo}>Phone: {business_info.phone}</Text>
              <Text style={styles.businessInfo}>Email: {business_info.email}</Text>
              {business_info.website && (
                <Text style={styles.businessInfo}>Web: {business_info.website}</Text>
              )}
              {business_info.abn && (
                <Text style={styles.businessInfo}>ABN: {business_info.abn}</Text>
              )}
            </View>

            {/* Customer Information */}
            {transaction.customer && (
              <View style={styles.customerSection}>
                <Text style={styles.sectionTitle}>CUSTOMER</Text>
                <Text style={styles.customerName}>{transaction.customer.full_name}</Text>
                {transaction.customer.email && (
                  <Text style={styles.customerContact}>Email: {transaction.customer.email}</Text>
                )}
                {transaction.customer.phone && (
                  <Text style={styles.customerContact}>Phone: {transaction.customer.phone}</Text>
                )}
              </View>
            )}

            <View style={styles.divider} />

            {/* Receipt Info */}
            <View style={styles.receiptInfo}>
              <Text style={styles.receiptTitle}>RECEIPT</Text>
              <View style={styles.receiptDetails}>
                <View style={styles.receiptRow}>
                  <Text style={styles.receiptLabel}>Receipt #:</Text>
                  <Text style={styles.receiptValue}>{receipt_number}</Text>
                </View>
                <View style={styles.receiptRow}>
                  <Text style={styles.receiptLabel}>Date:</Text>
                  <Text style={styles.receiptValue}>{formatDate(transaction.created_at)}</Text>
                </View>
                <View style={styles.receiptRow}>
                  <Text style={styles.receiptLabel}>Staff:</Text>
                  <Text style={styles.receiptValue}>
                    {transaction.staff?.first_name} {transaction.staff?.last_name}
                  </Text>
                </View>
                {transaction.customer && (
                  <View style={styles.receiptRow}>
                    <Text style={styles.receiptLabel}>Customer:</Text>
                    <Text style={styles.receiptValue}>{transaction.customer.full_name}</Text>
                  </View>
                )}
              </View>
            </View>

            <View style={styles.divider} />

            {/* Items */}
            <View style={styles.itemsSection}>
              <Text style={styles.itemsTitle}>ITEMS</Text>
              {transaction.transaction_items?.map((item, index) => (
                <View key={index} style={styles.item}>
                  <Text style={styles.itemName}>{item.item_name}</Text>
                  <View style={styles.itemDetails}>
                    <Text style={styles.itemQuantity}>
                      {item.quantity} × {formatPrice(item.unit_price)}
                    </Text>
                    <Text style={styles.itemTotal}>{formatPrice(item.total_price)}</Text>
                  </View>
                  {item.notes && (
                    <Text style={styles.itemNotes}>Note: {item.notes}</Text>
                  )}
                </View>
              ))}
            </View>

            <View style={styles.divider} />

            {/* Totals */}
            <View style={styles.totalsSection}>
              <View style={styles.totalRow}>
                <Text style={styles.totalLabel}>Subtotal</Text>
                <Text style={styles.totalValue}>{formatPrice(transaction.subtotal)}</Text>
              </View>
              <View style={styles.totalRow}>
                <Text style={styles.totalLabel}>GST (10%)</Text>
                <Text style={styles.totalValue}>{formatPrice(transaction.tax_amount)}</Text>
              </View>
              <View style={[styles.totalRow, styles.grandTotalRow]}>
                <Text style={styles.grandTotalLabel}>TOTAL</Text>
                <Text style={styles.grandTotalValue}>{formatPrice(transaction.total_amount)}</Text>
              </View>
            </View>

            <View style={styles.divider} />

            {/* Payment Info */}
            <View style={styles.paymentSection}>
              <View style={styles.paymentRow}>
                <Text style={styles.paymentLabel}>Payment Method:</Text>
                <Text style={styles.paymentValue}>
                  {safeToUpperCase(transaction.payment_method || 'LOYALTY TRANSACTION')}
                </Text>
              </View>
              <View style={styles.paymentRow}>
                <Text style={styles.paymentLabel}>Status:</Text>
                <Text style={[styles.paymentValue, styles.paymentStatus]}>
                  {safeToUpperCase(transaction.payment_status || transaction.transaction_type || 'COMPLETED')}
                </Text>
              </View>
            </View>

            {/* Footer */}
            <View style={styles.footer}>
              <Text style={styles.thankYou}>Thank you for choosing</Text>
              <Text style={styles.thankYou}>Ocean Soul Sparkles! 🌊✨</Text>
              <Text style={styles.generated}>Generated: {formatDate(generated_at)}</Text>
            </View>
          </View>
        </ScrollView>

        {/* Action Buttons */}
        <View style={styles.actions}>
          <TouchableOpacity style={styles.actionButton} onPress={handleEmail}>
            <Text style={styles.actionButtonText}>📧 Email Receipt</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.actionButton} onPress={handleShare}>
            <Text style={styles.actionButtonText}>📤 Share Receipt</Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  closeButton: {
    padding: 8,
  },
  closeButtonText: {
    fontSize: 16,
    color: '#666',
    fontWeight: '600',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  shareButton: {
    padding: 8,
  },
  shareButtonText: {
    fontSize: 16,
    color: '#FF9A8B',
    fontWeight: '600',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  receipt: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 24,
    marginBottom: 16,
  },
  businessHeader: {
    alignItems: 'center',
    marginBottom: 20,
  },
  businessName: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#FF9A8B',
    marginBottom: 8,
    textAlign: 'center',
  },
  businessInfo: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    marginBottom: 2,
  },
  customerSection: {
    alignItems: 'center',
    marginBottom: 20,
    paddingVertical: 12,
    borderTopWidth: 1,
    borderBottomWidth: 1,
    borderColor: '#e0e0e0',
  },
  customerName: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
    marginBottom: 4,
    textAlign: 'center',
  },
  customerContact: {
    fontSize: 12,
    color: '#666',
    textAlign: 'center',
    marginBottom: 2,
  },
  divider: {
    height: 1,
    backgroundColor: '#e0e0e0',
    marginVertical: 16,
  },
  receiptInfo: {
    alignItems: 'center',
  },
  receiptTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 16,
  },
  receiptDetails: {
    width: '100%',
  },
  receiptRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  receiptLabel: {
    fontSize: 14,
    color: '#666',
  },
  receiptValue: {
    fontSize: 14,
    color: '#333',
    fontWeight: '500',
  },
  itemsSection: {
    marginBottom: 16,
  },
  itemsTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 12,
    textAlign: 'center',
  },
  item: {
    marginBottom: 12,
    paddingBottom: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  itemName: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
    marginBottom: 4,
  },
  itemDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  itemQuantity: {
    fontSize: 14,
    color: '#666',
  },
  itemTotal: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FF9A8B',
  },
  itemNotes: {
    fontSize: 12,
    color: '#999',
    fontStyle: 'italic',
    marginTop: 4,
  },
  totalsSection: {
    marginBottom: 16,
  },
  totalRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  grandTotalRow: {
    marginTop: 8,
    paddingTop: 8,
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
  },
  totalLabel: {
    fontSize: 14,
    color: '#666',
  },
  totalValue: {
    fontSize: 14,
    color: '#333',
  },
  grandTotalLabel: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  grandTotalValue: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#FF9A8B',
  },
  paymentSection: {
    marginBottom: 16,
  },
  paymentRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  paymentLabel: {
    fontSize: 14,
    color: '#666',
  },
  paymentValue: {
    fontSize: 14,
    color: '#333',
    fontWeight: '500',
  },
  paymentStatus: {
    color: '#4CAF50',
  },
  footer: {
    alignItems: 'center',
    marginTop: 16,
  },
  thankYou: {
    fontSize: 16,
    color: '#FF9A8B',
    fontWeight: '500',
    textAlign: 'center',
  },
  generated: {
    fontSize: 12,
    color: '#999',
    marginTop: 12,
  },
  actions: {
    flexDirection: 'row',
    padding: 16,
    gap: 12,
    backgroundColor: '#fff',
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
  },
  actionButton: {
    flex: 1,
    backgroundColor: '#FF9A8B',
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
  },
  actionButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default ReceiptModal;
