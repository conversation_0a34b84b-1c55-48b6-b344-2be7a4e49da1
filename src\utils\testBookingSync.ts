/**
 * Test booking data synchronization with admin dashboard
 */

import { bookingService } from '@/services/database/bookingService';
import { inspectBookingTables } from './databaseTest';

export const testBookingSync = async () => {
  console.log('🧪 Testing booking data synchronization...');
  
  try {
    // 1. First inspect what booking tables exist
    console.log('1. Inspecting booking tables...');
    const inspection = await inspectBookingTables();
    console.log('Inspection result:', inspection);
    
    // Find accessible booking tables
    const accessibleTables = Object.entries(inspection)
      .filter(([_, result]) => result.accessible)
      .map(([table, result]) => ({ table, count: result.count, columns: result.columns }));
    
    if (accessibleTables.length === 0) {
      return { 
        success: false, 
        error: 'No accessible booking tables found',
        suggestion: 'Check if booking tables exist in the database or if permissions are correct'
      };
    }
    
    console.log('✅ Found accessible booking tables:', accessibleTables);
    
    // 2. Test loading bookings
    console.log('2. Testing booking loading...');
    const result = await bookingService.getBookings({
      limit: 10
    });
    
    if (result.error) {
      console.log('❌ Booking loading failed:', result.error.message);
      return { 
        success: false, 
        error: result.error.message,
        accessibleTables,
        suggestion: 'Check database relationships and column names'
      };
    }
    
    console.log('✅ Booking loading succeeded!');
    console.log('📊 Loaded bookings:', result.data?.length || 0);
    
    // 3. Analyze the booking data
    const bookings = result.data || [];
    const analysis = {
      totalBookings: bookings.length,
      withCustomers: bookings.filter(b => b.customer).length,
      withStaff: bookings.filter(b => b.staff).length,
      withServices: bookings.filter(b => b.service).length,
      statuses: [...new Set(bookings.map(b => b.status))],
      dateRange: bookings.length > 0 ? {
        earliest: Math.min(...bookings.map(b => new Date(b.booking_date).getTime())),
        latest: Math.max(...bookings.map(b => new Date(b.booking_date).getTime()))
      } : null
    };
    
    console.log('📈 Booking analysis:', analysis);
    
    if (bookings.length > 0) {
      console.log('📋 Sample booking:', bookings[0]);
    }
    
    // 4. Check for today's bookings specifically
    console.log('3. Testing today\'s bookings...');
    const todayResult = await bookingService.getTodaysBookings();
    
    if (todayResult.error) {
      console.log('❌ Today\'s bookings failed:', todayResult.error.message);
    } else {
      console.log(`✅ Today's bookings: ${todayResult.data?.length || 0}`);
    }
    
    return { 
      success: true, 
      totalBookings: bookings.length,
      todaysBookings: todayResult.data?.length || 0,
      analysis,
      accessibleTables,
      sampleBooking: bookings[0] || null
    };
    
  } catch (error) {
    console.log('❌ Test failed:', error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    };
  }
};

// Test if we can access the same data as the admin dashboard
export const testAdminDashboardSync = async () => {
  console.log('🔗 Testing admin dashboard synchronization...');
  
  try {
    // Try to load all bookings to see if we get the same 4 bookings mentioned
    const allBookingsResult = await bookingService.getBookings({
      limit: 20, // Get more than 4 to see all available
      order_by: 'created_at',
      order_direction: 'desc'
    });
    
    if (allBookingsResult.error) {
      return {
        success: false,
        error: `Failed to load bookings: ${allBookingsResult.error.message}`,
        suggestion: 'Check database connection and table permissions'
      };
    }
    
    const bookings = allBookingsResult.data || [];
    console.log(`📊 Found ${bookings.length} total bookings`);
    
    // Check if we have exactly 4 bookings as mentioned in the admin dashboard
    if (bookings.length === 4) {
      console.log('✅ Found exactly 4 bookings - matches admin dashboard!');
    } else if (bookings.length > 4) {
      console.log(`ℹ️ Found ${bookings.length} bookings - more than the 4 in admin dashboard`);
    } else {
      console.log(`⚠️ Found only ${bookings.length} bookings - less than the 4 in admin dashboard`);
    }
    
    // Analyze booking details
    const bookingDetails = bookings.map(booking => ({
      id: booking.id,
      date: booking.booking_date,
      time: booking.start_time,
      customer: booking.customer?.full_name || 'Unknown',
      service: booking.service?.name || 'Unknown',
      staff: booking.staff ? `${booking.staff.first_name} ${booking.staff.last_name}` : 'Unknown',
      status: booking.status,
      amount: booking.total_amount
    }));
    
    console.log('📋 Booking details:', bookingDetails);
    
    return {
      success: true,
      totalBookings: bookings.length,
      expectedBookings: 4,
      matches: bookings.length === 4,
      bookingDetails,
      message: bookings.length === 4 
        ? 'Perfect sync with admin dashboard!' 
        : `Found ${bookings.length} bookings instead of expected 4`
    };
    
  } catch (error) {
    console.error('❌ Admin dashboard sync test failed:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
};
