#!/usr/bin/env node

/**
 * Ocean Soul Sparkles Mobile App - Metro Bundler Startup Script
 * Starts Metro bundler with proper configuration for Android Studio development
 */

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

// Load development environment variables
require('dotenv').config({ path: path.join(__dirname, '..', '.env.development') });

console.log('🚀 Starting Metro bundler for Android Studio development...\n');

// Check if Metro config exists
const metroConfigPath = path.join(__dirname, '..', 'metro.config.js');
if (!fs.existsSync(metroConfigPath)) {
  console.error('❌ Metro configuration not found at:', metroConfigPath);
  process.exit(1);
}

// Metro bundler options
const metroOptions = [
  'react-native',
  'start',
  '--port', '8081',
  '--host', 'localhost',
  '--config', metroConfigPath,
  '--reset-cache'
];

// Add verbose logging in development
if (process.env.EXPO_PUBLIC_DEBUG_MODE === 'true') {
  metroOptions.push('--verbose');
}

console.log('📋 Metro configuration:');
console.log('   Port: 8081');
console.log('   Host: localhost');
console.log('   Config:', metroConfigPath);
console.log('   Environment:', process.env.EXPO_PUBLIC_ENVIRONMENT || 'development');
console.log('   Debug mode:', process.env.EXPO_PUBLIC_DEBUG_MODE || 'false');
console.log('');

// Start Metro bundler
const isWindows = process.platform === 'win32';
const command = isWindows ? 'npx.cmd' : 'npx';

const metro = spawn(command, metroOptions, {
  stdio: 'inherit',
  cwd: path.join(__dirname, '..'),
  shell: isWindows,
  env: {
    ...process.env,
    NODE_ENV: 'development',
    METRO_HOST: 'localhost',
    METRO_PORT: '8081',
    RCT_METRO_PORT: '8081',
  }
});

// Handle Metro process events
metro.on('error', (error) => {
  console.error('❌ Failed to start Metro bundler:', error.message);
  process.exit(1);
});

metro.on('close', (code) => {
  if (code !== 0) {
    console.error(`❌ Metro bundler exited with code ${code}`);
    process.exit(code);
  } else {
    console.log('✅ Metro bundler stopped successfully');
  }
});

// Handle process termination
process.on('SIGINT', () => {
  console.log('\n🛑 Stopping Metro bundler...');
  metro.kill('SIGINT');
});

process.on('SIGTERM', () => {
  console.log('\n🛑 Stopping Metro bundler...');
  metro.kill('SIGTERM');
});

console.log('🎯 Metro bundler is starting...');
console.log('📱 Once Metro is running, you can:');
console.log('   1. Open Android Studio');
console.log('   2. Open the android/ folder as a project');
console.log('   3. Build and run the app');
console.log('   4. Metro will serve the JavaScript bundle');
console.log('');
console.log('🔗 Metro will be available at: http://localhost:8081');
console.log('⏹️  Press Ctrl+C to stop Metro bundler');
console.log('='.repeat(60));
