/**
 * Ocean Soul Sparkles Mobile App - Real-Time Data Synchronization Service
 * Ensures data consistency between mobile app and admin portal with conflict resolution
 */

import { supabase } from '@/services/database/supabase';
import { adminPortalAPIService } from './adminPortalAPIService';
import { RealtimeChannel, RealtimePostgresChangesPayload } from '@supabase/supabase-js';
import { pushNotificationService } from '@/services/notifications/pushNotificationService';
import { staffCommunicationService } from '@/services/communication/staffCommunicationService';
import { AppState, AppStateStatus } from 'react-native';
import { supabaseConnectionPool } from '@/utils/supabaseConnectionPool';

export interface SyncConfiguration {
  tables: string[];
  conflictResolution: 'admin_wins' | 'mobile_wins' | 'latest_wins' | 'manual';
  syncInterval: number;
  batchSize: number;
  enableRealTime: boolean;
}

export interface SyncStatus {
  table: string;
  lastSync: string;
  recordCount: number;
  pendingChanges: number;
  conflicts: number;
  status: 'synced' | 'syncing' | 'conflict' | 'error';
  error?: string;
}

export interface DataConflict {
  id: string;
  table: string;
  field: string;
  mobileValue: any;
  adminValue: any;
  lastModifiedMobile: string;
  lastModifiedAdmin: string;
  resolution?: 'admin' | 'mobile' | 'manual';
}

export interface SyncEvent {
  type: 'INSERT' | 'UPDATE' | 'DELETE';
  table: string;
  record: any;
  timestamp: string;
  source: 'mobile' | 'admin';
}

export class RealTimeDataSyncService {
  private static instance: RealTimeDataSyncService;
  private channels: Map<string, RealtimeChannel> = new Map();
  private syncStatus: Map<string, SyncStatus> = new Map();
  private conflicts: DataConflict[] = [];
  private isInitialized = false;
  private appStateSubscription: any = null;
  private backgroundCleanupTimeout: NodeJS.Timeout | null = null;
  private periodicSyncInterval: NodeJS.Timeout | null = null;

  // Memory management constants
  private readonly MAX_CONFLICTS = 100; // Maximum conflicts to keep in memory
  private readonly MAX_SYNC_HISTORY = 50; // Maximum sync status entries per table
  private readonly CLEANUP_INTERVAL = 5 * 60 * 1000; // 5 minutes
  private memoryCleanupInterval: NodeJS.Timeout | null = null;

  private readonly DEFAULT_CONFIG: SyncConfiguration = {
    tables: ['customers', 'bookings', 'quotes', 'services', 'transactions'],
    conflictResolution: 'latest_wins',
    syncInterval: 30000, // 30 seconds
    batchSize: 50,
    enableRealTime: true,
  };

  private constructor() {}

  public static getInstance(): RealTimeDataSyncService {
    if (!RealTimeDataSyncService.instance) {
      RealTimeDataSyncService.instance = new RealTimeDataSyncService();
    }
    return RealTimeDataSyncService.instance;
  }

  /**
   * Initialize real-time synchronization
   */
  async initialize(config: Partial<SyncConfiguration> = {}): Promise<void> {
    if (this.isInitialized) {
      console.log('🔄 Real-time sync already initialized');
      return;
    }

    const finalConfig = { ...this.DEFAULT_CONFIG, ...config };
    
    try {
      console.log('🚀 Initializing real-time data synchronization...');

      // Initialize sync status for all tables
      for (const table of finalConfig.tables) {
        this.syncStatus.set(table, {
          table,
          lastSync: new Date().toISOString(),
          recordCount: 0,
          pendingChanges: 0,
          conflicts: 0,
          status: 'synced',
        });
      }

      // Set up real-time subscriptions if enabled
      if (finalConfig.enableRealTime) {
        await this.setupRealTimeSubscriptions(finalConfig.tables);
      }

      // Start periodic sync
      this.startPeriodicSync(finalConfig.syncInterval);

      // Start memory cleanup interval
      this.startMemoryCleanup();

      // Set up app state change listeners for cleanup
      this.setupAppStateListeners();

      this.isInitialized = true;
      console.log('✅ Real-time data synchronization initialized');

    } catch (error) {
      console.error('❌ Failed to initialize real-time sync:', error);
      throw error;
    }
  }

  /**
   * Set up app state change listeners for automatic cleanup
   */
  private setupAppStateListeners(): void {
    try {
      // Listen for app state changes
      this.appStateSubscription = AppState.addEventListener('change', (nextAppState: AppStateStatus) => {
        console.log(`📱 App state changed to: ${nextAppState}`);

        if (nextAppState === 'background' || nextAppState === 'inactive') {
          // App is going to background, schedule cleanup
          this.scheduleBackgroundCleanup();
        } else if (nextAppState === 'active') {
          // App is becoming active, cancel cleanup and ensure connections
          this.cancelBackgroundCleanup();
          this.ensureConnectionHealth();
        }
      });

      console.log('📱 App state listeners set up for subscription cleanup');
    } catch (error) {
      console.error('❌ Failed to set up app state listeners:', error);
    }
  }

  /**
   * Schedule cleanup when app goes to background
   */
  private scheduleBackgroundCleanup(): void {
    // Cancel any existing cleanup timeout
    this.cancelBackgroundCleanup();

    // Schedule cleanup after 5 minutes in background
    this.backgroundCleanupTimeout = setTimeout(() => {
      console.log('🧹 Performing background cleanup of subscriptions...');
      this.performBackgroundCleanup();
    }, 5 * 60 * 1000); // 5 minutes

    console.log('⏰ Background cleanup scheduled for 5 minutes');
  }

  /**
   * Cancel background cleanup
   */
  private cancelBackgroundCleanup(): void {
    if (this.backgroundCleanupTimeout) {
      clearTimeout(this.backgroundCleanupTimeout);
      this.backgroundCleanupTimeout = null;
      console.log('⏰ Background cleanup cancelled');
    }
  }

  /**
   * Perform background cleanup to save resources
   */
  private performBackgroundCleanup(): void {
    try {
      // Temporarily pause real-time subscriptions to save battery/data
      const mainChannel = this.channels.get('main_channel');
      if (mainChannel) {
        mainChannel.unsubscribe();
        console.log('📡 Real-time subscriptions paused for background mode');
      }
    } catch (error) {
      console.error('❌ Error during background cleanup:', error);
    }
  }

  /**
   * Ensure connection health when app becomes active
   */
  private ensureConnectionHealth(): void {
    try {
      // Check if main channel is still connected
      const mainChannel = this.channels.get('main_channel');
      if (mainChannel && mainChannel.state !== 'joined') {
        console.log('🔄 Reconnecting real-time subscriptions...');
        // Reconnect if needed
        mainChannel.subscribe();
      }
    } catch (error) {
      console.error('❌ Error ensuring connection health:', error);
    }
  }

  /**
   * Set up optimized real-time subscriptions using connection pooling
   */
  private async setupRealTimeSubscriptions(tables: string[]): Promise<void> {
    try {
      // Initialize the connection pool
      supabaseConnectionPool.initialize();

      console.log(`📡 Setting up real-time subscriptions for ${tables.length} tables using connection pool`);

      // Subscribe to each table using the connection pool
      for (const table of tables) {
        // Use a unique subscriber ID for each table
        const subscriberId = `real_time_sync_${table}`;

        // Subscribe to the table
        supabaseConnectionPool.subscribe(
          subscriberId,
          table,
          (payload: any) => {
            this.handleRealTimeChange(table, payload);
          },
          {
            event: '*'
          }
        );

        console.log(`📡 Subscribed to table ${table} using connection pool`);
      }

      console.log(`✅ Optimized real-time subscriptions set up for ${tables.length} tables using connection pool`);

    } catch (error) {
      console.error('❌ Failed to set up real-time subscriptions:', error);
      throw error;
    }
  }

  /**
   * Handle real-time database changes
   */
  private handleRealTimeChange(
    table: string,
    payload: RealtimePostgresChangesPayload<any>
  ): void {
    try {
      console.log(`🔄 Real-time change detected in ${table}:`, payload.eventType);

      const syncEvent: SyncEvent = {
        type: payload.eventType as 'INSERT' | 'UPDATE' | 'DELETE',
        table,
        record: payload.new || payload.old,
        timestamp: new Date().toISOString(),
        source: 'mobile',
      };

      // Handle new pending bookings for notifications
      if (table === 'bookings' && payload.eventType === 'INSERT') {
        this.handleNewBookingNotification(payload.new);
      }

      // Update sync status
      const status = this.syncStatus.get(table);
      if (status) {
        status.lastSync = new Date().toISOString();
        status.recordCount += payload.eventType === 'INSERT' ? 1 : 
                             payload.eventType === 'DELETE' ? -1 : 0;
        this.syncStatus.set(table, status);
      }

      this.notifyAdminPortalOfChange(syncEvent);

    } catch (error) {
      console.error(`❌ Error handling real-time change for ${table}:`, error);
    }
  }

  /**
   * Handle new booking notifications
   */
  private async handleNewBookingNotification(booking: any): Promise<void> {
    try {
      // Only send notifications for pending bookings
      if (booking.status !== 'pending') return;

      console.log('🔔 Sending new booking notification for:', booking.id);

      // Send push notification to staff
      await pushNotificationService.sendNewBookingNotification(booking);

      // Create chat thread for booking coordination
      const chatThread = await staffCommunicationService.createBookingChatThread(
        booking.id,
        `${booking.customer_name} - ${booking.service_name || 'Service'}`
      );

      if (chatThread) {
        console.log('💬 Chat thread created for booking:', chatThread.id);
      }

    } catch (error) {
      console.error('❌ Failed to handle new booking notification:', error);
    }
  }

  /**
   * Notify admin portal of data changes
   */
  private async notifyAdminPortalOfChange(event: SyncEvent): Promise<void> {
    try {
      // This would typically send a webhook or API call to admin portal
      // For now, we'll just log the event
      console.log('📤 Notifying admin portal of change:', event);

      // In a real implementation, you might:
      // await adminPortalAPIService.notifyDataChange(event);

    } catch (error) {
      console.warn('⚠️ Failed to notify admin portal of change:', error);
    }
  }

  /**
   * Start periodic synchronization with proper cleanup tracking
   */
  private startPeriodicSync(interval: number): void {
    // Clear any existing interval to prevent multiple intervals
    this.stopPeriodicSync();

    this.periodicSyncInterval = setInterval(async () => {
      try {
        await this.performPeriodicSync();
      } catch (error) {
        console.error('❌ Periodic sync failed:', error);
      }
    }, interval);

    console.log(`⏰ Periodic sync started with ${interval}ms interval`);
  }

  /**
   * Stop periodic synchronization
   */
  private stopPeriodicSync(): void {
    if (this.periodicSyncInterval) {
      clearInterval(this.periodicSyncInterval);
      this.periodicSyncInterval = null;
      console.log('⏰ Periodic sync stopped');
    }
  }

  /**
   * Start memory cleanup interval to prevent memory leaks
   */
  private startMemoryCleanup(): void {
    // Clear any existing cleanup interval
    this.stopMemoryCleanup();

    this.memoryCleanupInterval = setInterval(() => {
      this.performMemoryCleanup();
    }, this.CLEANUP_INTERVAL);

    console.log('🧹 Memory cleanup interval started');
  }

  /**
   * Stop memory cleanup interval
   */
  private stopMemoryCleanup(): void {
    if (this.memoryCleanupInterval) {
      clearInterval(this.memoryCleanupInterval);
      this.memoryCleanupInterval = null;
      console.log('🧹 Memory cleanup interval stopped');
    }
  }

  /**
   * Perform memory cleanup to prevent memory leaks
   */
  private performMemoryCleanup(): void {
    try {
      console.log('🧹 Performing memory cleanup...');

      // Clean up old conflicts (keep only the most recent ones)
      if (this.conflicts.length > this.MAX_CONFLICTS) {
        const excessCount = this.conflicts.length - this.MAX_CONFLICTS;
        this.conflicts.splice(0, excessCount);
        console.log(`🧹 Removed ${excessCount} old conflicts`);
      }

      // Clean up old sync status entries
      let totalCleaned = 0;
      for (const [table, status] of this.syncStatus) {
        // Remove sync status entries that are very old (older than 1 hour)
        const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000).toISOString();
        if (status.lastSync && status.lastSync < oneHourAgo && status.status !== 'syncing') {
          // Reset status but keep the entry
          status.recordCount = 0;
          status.pendingChanges = 0;
          status.conflicts = 0;
          totalCleaned++;
        }
      }

      if (totalCleaned > 0) {
        console.log(`🧹 Cleaned up ${totalCleaned} old sync status entries`);
      }

      // Force garbage collection hint (if available)
      if (global.gc) {
        global.gc();
        console.log('🧹 Garbage collection triggered');
      }

    } catch (error) {
      console.error('❌ Memory cleanup failed:', error);
    }
  }

  /**
   * Perform periodic synchronization check
   */
  private async performPeriodicSync(): Promise<void> {
    try {
      console.log('🔄 Performing periodic sync check...');

      // Get sync status from admin portal
      const adminSyncResult = await adminPortalAPIService.getDataSyncStatus();
      
      if (adminSyncResult.success && adminSyncResult.data) {
        // Compare with local sync status and identify discrepancies
        for (const adminStatus of adminSyncResult.data) {
          const localStatus = this.syncStatus.get(adminStatus.table);
          
          if (localStatus) {
            // Check for conflicts or sync issues
            if (adminStatus.recordCount !== localStatus.recordCount) {
              console.warn(`⚠️ Record count mismatch for ${adminStatus.table}: Admin=${adminStatus.recordCount}, Mobile=${localStatus.recordCount}`);
              localStatus.status = 'conflict';
              localStatus.conflicts += 1;
            }

            // Update last sync time
            localStatus.lastSync = new Date().toISOString();
            this.syncStatus.set(adminStatus.table, localStatus);
          }
        }
      }

    } catch (error) {
      console.error('❌ Periodic sync check failed:', error);
    }
  }

  /**
   * Manually trigger full synchronization
   */
  async triggerFullSync(tables?: string[]): Promise<{ success: boolean; results: SyncStatus[]; error?: string }> {
    try {
      console.log('🔄 Triggering full synchronization...');

      const tablesToSync = tables || Array.from(this.syncStatus.keys());
      const results: SyncStatus[] = [];

      for (const table of tablesToSync) {
        const status = this.syncStatus.get(table);
        if (status) {
          status.status = 'syncing';
          this.syncStatus.set(table, status);

          try {
            // Trigger sync on admin portal
            const syncResult = await adminPortalAPIService.triggerDataSync([table]);
            
            if (syncResult.success) {
              status.status = 'synced';
              status.lastSync = new Date().toISOString();
              status.pendingChanges = 0;
            } else {
              status.status = 'error';
              status.error = syncResult.error;
            }

          } catch (error) {
            status.status = 'error';
            status.error = error instanceof Error ? error.message : 'Sync failed';
          }

          this.syncStatus.set(table, status);
          results.push(status);
        }
      }

      console.log(`✅ Full synchronization completed for ${results.length} tables`);
      return { success: true, results };

    } catch (error) {
      console.error('❌ Full synchronization failed:', error);
      return {
        success: false,
        results: [],
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Get current synchronization status
   */
  getSyncStatus(): SyncStatus[] {
    return Array.from(this.syncStatus.values());
  }

  /**
   * Get synchronization status for specific table
   */
  getTableSyncStatus(table: string): SyncStatus | null {
    return this.syncStatus.get(table) || null;
  }

  /**
   * Add conflict with automatic memory management
   */
  private addConflict(conflict: DataConflict): void {
    this.conflicts.push(conflict);

    // Trim conflicts if we exceed the limit
    if (this.conflicts.length > this.MAX_CONFLICTS) {
      const excessCount = this.conflicts.length - this.MAX_CONFLICTS;
      this.conflicts.splice(0, excessCount);
      console.log(`🧹 Auto-trimmed ${excessCount} old conflicts`);
    }
  }

  /**
   * Get pending conflicts
   */
  getConflicts(): DataConflict[] {
    return [...this.conflicts];
  }

  /**
   * Resolve data conflict
   */
  async resolveConflict(
    conflictId: string,
    resolution: 'admin' | 'mobile' | 'manual',
    manualValue?: any
  ): Promise<{ success: boolean; error?: string }> {
    try {
      const conflict = this.conflicts.find(c => c.id === conflictId);
      if (!conflict) {
        return { success: false, error: 'Conflict not found' };
      }

      let resolvedValue: any;
      
      switch (resolution) {
        case 'admin':
          resolvedValue = conflict.adminValue;
          break;
        case 'mobile':
          resolvedValue = conflict.mobileValue;
          break;
        case 'manual':
          resolvedValue = manualValue;
          break;
      }

      // Apply resolution to database
      const { error } = await supabase
        .from(conflict.table)
        .update({ [conflict.field]: resolvedValue })
        .eq('id', conflict.id);

      if (error) {
        return { success: false, error: error.message };
      }

      // Remove resolved conflict
      this.conflicts = this.conflicts.filter(c => c.id !== conflictId);

      console.log(`✅ Conflict resolved for ${conflict.table}.${conflict.field}`);
      return { success: true };

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Resolution failed',
      };
    }
  }

  /**
   * Test real-time connectivity
   */
  async testRealTimeConnectivity(): Promise<{ connected: boolean; latency: number; channels: number }> {
    try {
      const startTime = Date.now();
      
      // Test admin portal real-time connectivity
      const adminResult = await adminPortalAPIService.testRealTimeConnectivity();
      
      const latency = Date.now() - startTime;
      const connected = adminResult.success && this.channels.size > 0;

      return {
        connected,
        latency,
        channels: this.channels.size,
      };

    } catch (error) {
      console.error('❌ Real-time connectivity test failed:', error);
      return {
        connected: false,
        latency: 0,
        channels: this.channels.size,
      };
    }
  }

  /**
   * Get synchronization metrics
   */
  getSyncMetrics(): {
    totalTables: number;
    syncedTables: number;
    conflictTables: number;
    errorTables: number;
    totalConflicts: number;
    lastSyncTime: string;
  } {
    const statuses = Array.from(this.syncStatus.values());
    
    return {
      totalTables: statuses.length,
      syncedTables: statuses.filter(s => s.status === 'synced').length,
      conflictTables: statuses.filter(s => s.status === 'conflict').length,
      errorTables: statuses.filter(s => s.status === 'error').length,
      totalConflicts: this.conflicts.length,
      lastSyncTime: statuses.reduce((latest, status) => 
        status.lastSync > latest ? status.lastSync : latest, 
        '1970-01-01T00:00:00.000Z'
      ),
    };
  }

  /**
   * Cleanup and disconnect
   */
  async cleanup(): Promise<void> {
    try {
      console.log('🧹 Cleaning up real-time sync service...');

      // Cancel background cleanup timeout
      this.cancelBackgroundCleanup();

      // Stop periodic sync and memory cleanup
      this.stopPeriodicSync();
      this.stopMemoryCleanup();

      // Remove app state listeners
      if (this.appStateSubscription) {
        this.appStateSubscription.remove();
        this.appStateSubscription = null;
        console.log('📱 App state listeners removed');
      }

      // Cleanup connection pool (handles all subscriptions)
      supabaseConnectionPool.cleanup();
      console.log('📡 Connection pool cleaned up');

      // Clean up any remaining individual channels (for backward compatibility)
      for (const [channelName, channel] of this.channels) {
        try {
          await supabase.removeChannel(channel);
          console.log(`📡 Unsubscribed from ${channelName} channel`);
        } catch (error) {
          console.error(`❌ Error cleaning up channel ${channelName}:`, error);
        }
      }

      this.channels.clear();
      this.syncStatus.clear();
      this.conflicts = [];
      this.isInitialized = false;

      console.log('✅ Real-time sync service cleanup completed');

    } catch (error) {
      console.error('❌ Cleanup failed:', error);
    }
  }
}

// Export singleton instance
export const realTimeDataSyncService = RealTimeDataSyncService.getInstance();


