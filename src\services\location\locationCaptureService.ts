/**
 * Ocean Soul Sparkles Mobile App - Location Capture Service
 * Handles location capture, geocoding, and address validation for distance pricing
 */

import * as Location from 'expo-location';
import { LocationData } from '@/services/pricing/distancePricingService';

export interface LocationPermissionStatus {
  granted: boolean;
  canAskAgain: boolean;
  status: Location.PermissionStatus;
}

export interface GeocodeResult {
  success: boolean;
  location?: LocationData;
  error?: string;
  source: 'gps' | 'geocoding' | 'manual';
}

export interface AddressValidationResult {
  isValid: boolean;
  formattedAddress?: string;
  suggestions?: string[];
  confidence: 'high' | 'medium' | 'low';
}

export class LocationCaptureService {
  private static instance: LocationCaptureService;

  private constructor() {}

  public static getInstance(): LocationCaptureService {
    if (!LocationCaptureService.instance) {
      LocationCaptureService.instance = new LocationCaptureService();
    }
    return LocationCaptureService.instance;
  }

  /**
   * Check and request location permissions
   */
  async checkLocationPermissions(): Promise<LocationPermissionStatus> {
    try {
      console.log('📍 Checking location permissions...');

      // Check current permission status
      const { status } = await Location.getForegroundPermissionsAsync();
      
      if (status === 'granted') {
        return {
          granted: true,
          canAskAgain: true,
          status,
        };
      }

      // Request permission if not granted
      const { status: newStatus, canAskAgain } = await Location.requestForegroundPermissionsAsync();
      
      return {
        granted: newStatus === 'granted',
        canAskAgain,
        status: newStatus,
      };

    } catch (error) {
      console.error('❌ Location permission error:', error);
      return {
        granted: false,
        canAskAgain: false,
        status: 'denied',
      };
    }
  }

  /**
   * Get current device location
   */
  async getCurrentLocation(): Promise<GeocodeResult> {
    try {
      console.log('📍 Getting current device location...');

      // Check permissions first
      const permissions = await this.checkLocationPermissions();
      if (!permissions.granted) {
        return {
          success: false,
          error: 'Location permission not granted',
          source: 'gps',
        };
      }

      // Get current position
      const location = await Location.getCurrentPositionAsync({
        accuracy: Location.Accuracy.Balanced,
        timeInterval: 10000, // 10 seconds timeout
      });

      // Reverse geocode to get address
      const reverseGeocode = await Location.reverseGeocodeAsync({
        latitude: location.coords.latitude,
        longitude: location.coords.longitude,
      });

      const address = reverseGeocode[0];
      if (!address) {
        return {
          success: true,
          location: {
            address: `${location.coords.latitude}, ${location.coords.longitude}`,
            latitude: location.coords.latitude,
            longitude: location.coords.longitude,
          },
          source: 'gps',
        };
      }

      // Format address
      const formattedAddress = this.formatAddress(address);

      return {
        success: true,
        location: {
          address: formattedAddress,
          city: address.city || address.subregion,
          state: address.region,
          postal_code: address.postalCode,
          latitude: location.coords.latitude,
          longitude: location.coords.longitude,
        },
        source: 'gps',
      };

    } catch (error) {
      console.error('❌ Get current location error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get location',
        source: 'gps',
      };
    }
  }

  /**
   * Geocode address string to coordinates
   */
  async geocodeAddress(addressString: string): Promise<GeocodeResult> {
    try {
      console.log('🗺️ Geocoding address:', addressString);

      if (!addressString.trim()) {
        return {
          success: false,
          error: 'Address string is empty',
          source: 'geocoding',
        };
      }

      // Use Expo Location geocoding
      const geocodeResult = await Location.geocodeAsync(addressString);
      
      if (!geocodeResult || geocodeResult.length === 0) {
        return {
          success: false,
          error: 'Address not found',
          source: 'geocoding',
        };
      }

      const result = geocodeResult[0];

      // Try to reverse geocode to get formatted address
      let formattedLocation: LocationData = {
        address: addressString,
        latitude: result.latitude,
        longitude: result.longitude,
      };

      try {
        const reverseGeocode = await Location.reverseGeocodeAsync({
          latitude: result.latitude,
          longitude: result.longitude,
        });

        const address = reverseGeocode[0];
        if (address) {
          formattedLocation = {
            address: this.formatAddress(address),
            city: address.city || address.subregion,
            state: address.region,
            postal_code: address.postalCode,
            latitude: result.latitude,
            longitude: result.longitude,
          };
        }
      } catch (reverseError) {
        console.warn('Reverse geocoding failed, using original address');
      }

      return {
        success: true,
        location: formattedLocation,
        source: 'geocoding',
      };

    } catch (error) {
      console.error('❌ Geocoding error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Geocoding failed',
        source: 'geocoding',
      };
    }
  }

  /**
   * Validate and format address manually entered by user
   */
  async validateAddress(addressInput: {
    street?: string;
    city?: string;
    state?: string;
    postalCode?: string;
  }): Promise<AddressValidationResult> {
    try {
      console.log('✅ Validating address input...');

      // Build address string
      const addressParts = [
        addressInput.street,
        addressInput.city,
        addressInput.state,
        addressInput.postalCode,
      ].filter(Boolean);

      if (addressParts.length === 0) {
        return {
          isValid: false,
          confidence: 'low',
        };
      }

      const addressString = addressParts.join(', ');

      // Basic validation
      let confidence: 'high' | 'medium' | 'low' = 'low';
      
      if (addressInput.postalCode && addressInput.city && addressInput.state) {
        confidence = 'high';
      } else if (addressInput.city && addressInput.state) {
        confidence = 'medium';
      }

      // Try to geocode for validation
      try {
        const geocodeResult = await this.geocodeAddress(addressString);
        if (geocodeResult.success) {
          return {
            isValid: true,
            formattedAddress: geocodeResult.location?.address,
            confidence: 'high',
          };
        }
      } catch (geocodeError) {
        console.warn('Geocoding validation failed, using basic validation');
      }

      // Basic format validation
      const isValid = this.isValidAddressFormat(addressInput);

      return {
        isValid,
        formattedAddress: isValid ? addressString : undefined,
        confidence,
        suggestions: isValid ? [] : this.generateAddressSuggestions(addressInput),
      };

    } catch (error) {
      console.error('❌ Address validation error:', error);
      return {
        isValid: false,
        confidence: 'low',
      };
    }
  }

  /**
   * Create location data from customer information
   */
  createLocationFromCustomer(customer: {
    address?: string;
    city?: string;
    state?: string;
    postal_code?: string;
  }): LocationData {
    const addressParts = [
      customer.address,
      customer.city,
      customer.state,
      customer.postal_code,
    ].filter(Boolean);

    return {
      address: addressParts.join(', ') || 'Address not provided',
      city: customer.city,
      state: customer.state,
      postal_code: customer.postal_code,
    };
  }

  /**
   * Format address from reverse geocoding result
   */
  private formatAddress(address: Location.LocationGeocodedAddress): string {
    const parts = [
      address.streetNumber,
      address.street,
      address.city || address.subregion,
      address.region,
      address.postalCode,
    ].filter(Boolean);

    return parts.join(', ');
  }

  /**
   * Basic address format validation
   */
  private isValidAddressFormat(addressInput: {
    street?: string;
    city?: string;
    state?: string;
    postalCode?: string;
  }): boolean {
    // Must have at least city or postal code
    if (!addressInput.city && !addressInput.postalCode) {
      return false;
    }

    // Validate Australian postal code format if provided
    if (addressInput.postalCode) {
      const postalCodeRegex = /^\d{4}$/;
      if (!postalCodeRegex.test(addressInput.postalCode)) {
        return false;
      }
    }

    // Basic state validation for Australia
    if (addressInput.state) {
      const validStates = ['NSW', 'VIC', 'QLD', 'WA', 'SA', 'TAS', 'ACT', 'NT'];
      const stateUpper = addressInput.state.toUpperCase();
      const isValidState = validStates.includes(stateUpper) || 
                          validStates.some(state => stateUpper.includes(state));
      
      if (!isValidState) {
        return false;
      }
    }

    return true;
  }

  /**
   * Generate address suggestions for invalid input
   */
  private generateAddressSuggestions(addressInput: {
    street?: string;
    city?: string;
    state?: string;
    postalCode?: string;
  }): string[] {
    const suggestions: string[] = [];

    if (!addressInput.city && !addressInput.postalCode) {
      suggestions.push('Please provide at least a city or postal code');
    }

    if (addressInput.postalCode && !/^\d{4}$/.test(addressInput.postalCode)) {
      suggestions.push('Australian postal codes should be 4 digits (e.g., 2000)');
    }

    if (addressInput.state) {
      const stateUpper = addressInput.state.toUpperCase();
      const validStates = ['NSW', 'VIC', 'QLD', 'WA', 'SA', 'TAS', 'ACT', 'NT'];
      
      if (!validStates.some(state => stateUpper.includes(state))) {
        suggestions.push('Please use a valid Australian state (NSW, VIC, QLD, WA, SA, TAS, ACT, NT)');
      }
    }

    if (suggestions.length === 0) {
      suggestions.push('Try including more address details for better accuracy');
    }

    return suggestions;
  }

  /**
   * Get location permission status text for UI
   */
  getPermissionStatusText(status: LocationPermissionStatus): string {
    switch (status.status) {
      case 'granted':
        return 'Location access granted';
      case 'denied':
        return status.canAskAgain 
          ? 'Location access denied. Tap to request again.'
          : 'Location access permanently denied. Please enable in device settings.';
      case 'undetermined':
        return 'Location permission not requested yet';
      default:
        return 'Unknown location permission status';
    }
  }

  /**
   * Check if location services are available
   */
  async isLocationServicesEnabled(): Promise<boolean> {
    try {
      return await Location.hasServicesEnabledAsync();
    } catch (error) {
      console.error('❌ Check location services error:', error);
      return false;
    }
  }
}

// Export singleton instance
export const locationCaptureService = LocationCaptureService.getInstance();
