/**
 * Ocean Soul Sparkles Mobile App - Staff Service
 * Handles staff/admin user management and queries
 */

import { supabase } from './supabase';
import { AdminUser, DatabaseResponse, DatabaseListResponse, QueryFilters } from '@/types/database';

export class StaffService {

  /**
   * Get all staff members (admin users)
   */
  async getStaff(filters?: QueryFilters): Promise<DatabaseListResponse<AdminUser>> {
    try {
      console.log('👥 Loading staff members...');
      
      let query = supabase
        .from('admin_users')
        .select('*');

      // Apply search filter
      if (filters?.search) {
        query = query.or(`first_name.ilike.%${filters.search}%,last_name.ilike.%${filters.search}%,email.ilike.%${filters.search}%`);
      }

      // Apply role filter
      if (filters?.filters?.role) {
        query = query.eq('role', filters.filters.role);
      }

      // Apply active status filter
      if (filters?.filters?.is_active !== undefined) {
        query = query.eq('is_active', filters.filters.is_active);
      }

      // Apply ordering
      const orderBy = filters?.order_by || 'first_name';
      const orderDirection = filters?.order_direction || 'asc';
      query = query.order(orderBy, { ascending: orderDirection === 'asc' });

      // Apply pagination
      if (filters?.limit) {
        query = query.limit(filters.limit);
      }
      if (filters?.offset) {
        query = query.range(filters.offset, filters.offset + (filters.limit || 20) - 1);
      }

      const { data, error, count } = await query;

      if (error) {
        console.error('❌ Error fetching staff:', error);
        return { data: null, error, count: 0 };
      }

      console.log(`✅ Loaded ${data?.length || 0} staff members`);
      return { data: data || [], error: null, count: count || data?.length || 0 };
    } catch (error) {
      console.error('❌ Staff service error:', error);
      return { data: null, error: error as Error, count: 0 };
    }
  }

  /**
   * Get staff member by ID
   */
  async getStaffById(id: string): Promise<DatabaseResponse<AdminUser>> {
    try {
      const { data, error } = await supabase
        .from('admin_users')
        .select('*')
        .eq('id', id)
        .single();

      if (error) {
        console.error('❌ Get staff member error:', error);
        return { data: null, error };
      }

      return { data, error: null };
    } catch (error) {
      console.error('❌ Get staff member service error:', error);
      return { data: null, error: error as Error };
    }
  }

  /**
   * Update staff member status
   */
  async updateStaffStatus(id: string, isActive: boolean): Promise<DatabaseResponse<AdminUser>> {
    try {
      const { data, error } = await supabase
        .from('admin_users')
        .update({
          is_active: isActive,
          updated_at: new Date().toISOString(),
        })
        .eq('id', id)
        .select()
        .single();

      if (error) {
        console.error('❌ Update staff status error:', error);
        return { data: null, error };
      }

      return { data, error: null };
    } catch (error) {
      console.error('❌ Update staff status service error:', error);
      return { data: null, error: error as Error };
    }
  }

  /**
   * Get staff statistics
   */
  async getStaffStats(): Promise<{
    total: number;
    active: number;
    inactive: number;
    byRole: Record<string, number>;
  }> {
    try {
      const { data, error } = await supabase
        .from('admin_users')
        .select('role, is_active');

      if (error) {
        console.error('❌ Get staff stats error:', error);
        return { total: 0, active: 0, inactive: 0, byRole: {} };
      }

      const stats = {
        total: data?.length || 0,
        active: data?.filter(s => s.is_active).length || 0,
        inactive: data?.filter(s => !s.is_active).length || 0,
        byRole: {} as Record<string, number>
      };

      // Count by role
      data?.forEach(staff => {
        stats.byRole[staff.role] = (stats.byRole[staff.role] || 0) + 1;
      });

      return stats;
    } catch (error) {
      console.error('❌ Get staff stats service error:', error);
      return { total: 0, active: 0, inactive: 0, byRole: {} };
    }
  }

  /**
   * Get role color for UI display
   */
  getRoleColor(role: string): string {
    const roleColors: Record<string, string> = {
      'DEV': '#6366f1',      // Indigo
      'Admin': '#ef4444',    // Red
      'Artist': '#f59e0b',   // Amber
      'Braider': '#10b981',  // Emerald
    };
    return roleColors[role] || '#6b7280'; // Gray fallback
  }

  /**
   * Get role icon for UI display
   */
  getRoleIcon(role: string): string {
    const roleIcons: Record<string, string> = {
      'DEV': '👨‍💻',
      'Admin': '👑',
      'Artist': '🎨',
      'Braider': '✨',
    };
    return roleIcons[role] || '👤';
  }
}

// Export singleton instance
export const staffService = new StaffService();
