# Ocean Soul Sparkles Mobile App - Email System Documentation

## 📧 Overview

The Ocean Soul Sparkles mobile app includes a comprehensive email notification system that integrates with the existing admin portal's email infrastructure. This system automatically sends professional email notifications for quotes, booking confirmations, and reminders.

## 🏗️ Architecture

### Core Components

1. **EmailService** (`src/services/email/emailService.ts`)
   - Main email service that integrates with admin portal API
   - Handles template loading from Supabase database
   - Manages email sending and delivery tracking

2. **QuoteEmailService** (`src/services/email/quoteEmailService.ts`)
   - Specialized service for quote-related emails
   - Handles quote notifications, reminders, and booking confirmations
   - Loads related data (customer, booking, service, staff)

3. **EmailConfigService** (`src/services/email/emailConfig.ts`)
   - Manages email service configuration and authentication
   - Handles initialization and token management

4. **Email Templates** (`src/services/email/emailTemplates.ts`)
   - Fallback email templates when Supabase templates unavailable
   - Professional HTML and text templates for all email types

5. **Email Testing** (`src/utils/emailTesting.ts`)
   - Comprehensive testing utilities for email system validation
   - Status checking and connectivity testing

## 🔧 Integration Points

### Admin Portal Integration
- Uses same API endpoint: `https://admin.oceansoulsparkles.com.au/api`
- Shares authentication tokens with admin portal
- Utilizes existing email templates from Supabase database
- Maintains consistency with admin portal email workflows

### Database Integration
- Loads email templates from `email_templates` table in Supabase
- Tracks email delivery status (if supported by admin portal)
- Integrates with existing customer, booking, and quote data

### Authentication Integration
- Automatically initializes when user signs in
- Uses admin portal authentication tokens
- Clears authentication on sign out

## 📨 Email Types

### 1. Quote Emails
**Trigger**: When a quote is created in QuoteScreen
**Template Type**: `quote`
**Recipients**: Customer email address
**Content**: Quote details, pricing, validity period, contact information

### 2. Booking Confirmation Emails
**Trigger**: When a quote is accepted and converted to booking
**Template Type**: `booking_confirmation`
**Recipients**: Customer email address
**Content**: Booking details, date/time, service information, staff details

### 3. Quote Reminder Emails
**Trigger**: Manual or automated reminders for expiring quotes
**Template Type**: `reminder`
**Recipients**: Customer email address
**Content**: Quote expiry warning, urgency messaging, contact information

## 🚀 Usage Examples

### Automatic Quote Email (Integrated in QuoteScreen)
```typescript
// Email is automatically sent when quote is created
const quoteResult = await quoteService.createQuote(quoteData);
if (quoteResult.data) {
  const emailResult = await quoteEmailService.sendQuoteEmail(quoteResult.data);
  // Email status is shown in UI
}
```

### Manual Email Sending
```typescript
import { emailService } from '@/services/email/emailService';

const emailResult = await emailService.sendEmail({
  to: '<EMAIL>',
  to_name: 'Customer Name',
  template_type: 'quote',
  template_variables: {
    customer_name: 'Customer Name',
    quote_number: 'OSS-Q20241127-001',
    total_amount: '150.00',
    // ... other variables
  },
});
```

### Email System Testing
```typescript
import { emailTesting } from '@/utils/emailTesting';

// Run comprehensive test
const testResults = await emailTesting.runComprehensiveEmailTest();
console.log(`Success rate: ${testResults.summary.successRate}%`);

// Check system status
const status = await emailTesting.getEmailSystemStatus();
console.log(`Email system ready: ${status.overall}`);
```

## 🔧 Configuration

### Environment Variables
```bash
# Required
EXPO_PUBLIC_API_BASE_URL=https://admin.oceansoulsparkles.com.au/api

# Optional
EXPO_PUBLIC_EMAIL_ENABLED=true
EXPO_PUBLIC_EMAIL_FALLBACK_TEMPLATES=true
EXPO_PUBLIC_BUSINESS_EMAIL=<EMAIL>
EXPO_PUBLIC_BUSINESS_PHONE=+61 XXX XXX XXX
EXPO_PUBLIC_BUSINESS_WEBSITE=oceansoulsparkles.com.au
```

### Email Template Variables
All email templates support these variables:
- `customer_name` - Customer's full name
- `business_name` - "Ocean Soul Sparkles"
- `business_email` - Business contact email
- `business_phone` - Business contact phone
- `business_website` - Business website URL
- `staff_name` - Staff member handling the service

**Quote-specific variables:**
- `quote_number` - Generated quote number
- `quote_title` - Quote title/description
- `quote_description` - Detailed quote breakdown
- `total_amount` - Total quote amount
- `valid_until` - Quote expiry date
- `service_name` - Service being quoted
- `booking_date` - Requested booking date

**Booking-specific variables:**
- `booking_number` - Generated booking number
- `booking_date` - Confirmed booking date
- `start_time` - Appointment start time
- `end_time` - Appointment end time (if applicable)
- `total_amount` - Total booking amount
- `notes` - Additional booking notes

## 🧪 Testing & Validation

### Email Status Indicator Component
```typescript
import EmailStatusIndicator from '@/components/email/EmailStatusIndicator';

// Simple indicator
<EmailStatusIndicator />

// Detailed status with testing
<EmailStatusIndicator 
  showDetails={true}
  onStatusChange={(status) => console.log('Email status:', status)}
/>
```

### Manual Testing
1. **Connectivity Test**: Verify admin portal API connection
2. **Template Test**: Ensure email templates load correctly
3. **Authentication Test**: Validate token-based authentication
4. **Sending Test**: Test email sending process (simulation mode)

### Production Validation
1. Create a test quote with valid customer email
2. Verify email is sent and received
3. Check email formatting and content
4. Validate all template variables are populated
5. Confirm delivery tracking (if available)

## 🔒 Security Considerations

### Authentication
- Uses secure token-based authentication with admin portal
- Tokens are managed automatically by auth store
- No email credentials stored in mobile app

### Data Privacy
- Customer email addresses are only used for legitimate business communications
- Email content follows business communication standards
- No sensitive data exposed in email templates

### Error Handling
- Graceful fallback to built-in templates if Supabase templates unavailable
- Comprehensive error logging for debugging
- User-friendly error messages in UI

## 🚨 Troubleshooting

### Common Issues

**Email not sending:**
1. Check authentication status in EmailStatusIndicator
2. Verify admin portal API connectivity
3. Ensure customer has valid email address
4. Check email service logs for errors

**Template not found:**
1. Verify email templates exist in Supabase database
2. Check template type matches expected values
3. Fallback templates should load automatically

**Authentication failed:**
1. Ensure user is signed in to mobile app
2. Check admin portal API credentials
3. Verify token is being passed correctly

### Debug Mode
Enable debug logging by setting:
```bash
EXPO_PUBLIC_DEBUG_MODE=true
EXPO_PUBLIC_LOG_LEVEL=debug
```

## 📈 Future Enhancements

### Planned Features
1. **Email Delivery Tracking**: Real-time delivery status updates
2. **Email Templates Editor**: In-app template customization
3. **Automated Reminders**: Scheduled reminder emails
4. **Email Analytics**: Open rates and engagement tracking
5. **Multi-language Support**: Localized email templates

### Integration Opportunities
1. **SMS Notifications**: Backup communication channel
2. **Push Notifications**: In-app notification system
3. **Calendar Integration**: Appointment reminders
4. **Customer Portal**: Self-service email preferences

## 📞 Support

For email system issues or questions:
1. Check EmailStatusIndicator for system status
2. Run comprehensive email test for detailed diagnostics
3. Review logs for specific error messages
4. Contact development team with test results and error details

The email system is designed to be robust and self-healing, with comprehensive fallback mechanisms to ensure reliable communication with customers.
