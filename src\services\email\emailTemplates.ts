/**
 * Ocean Soul Sparkles Mobile App - Email Templates
 * Fallback email templates when Supabase templates are not available
 */

import { EmailTemplate } from '@/types/database';

export const fallbackEmailTemplates: Record<string, EmailTemplate> = {
  quote: {
    id: 'fallback-quote',
    name: 'Quote Email Template',
    subject: 'Your Quote from {{business_name}} - {{service_name}}',
    html_content: `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quote from {{business_name}}</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 20px; background-color: #f4f4f4; }
        .container { max-width: 600px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 0 10px rgba(0,0,0,0.1); }
        .header { text-align: center; border-bottom: 3px solid #FF9A8B; padding-bottom: 20px; margin-bottom: 30px; }
        .logo { font-size: 28px; font-weight: bold; color: #FF9A8B; margin-bottom: 10px; }
        .quote-details { background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0; }
        .amount { font-size: 24px; font-weight: bold; color: #FF9A8B; text-align: center; margin: 20px 0; }
        .footer { text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee; color: #666; }
        .button { display: inline-block; background: #FF9A8B; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; margin: 20px 0; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">{{business_name}}</div>
            <p>Professional Beauty Services</p>
        </div>
        
        <h2>Hello {{customer_name}},</h2>
        
        <p>Thank you for your interest in our services! We're pleased to provide you with the following quote:</p>
        
        <div class="quote-details">
            <h3>{{service_name}}</h3>
            <p><strong>Quote Number:</strong> {{quote_number}}</p>
            {{#if event_date}}<p><strong>Event Date:</strong> {{event_date}}</p>{{/if}}
            <p><strong>Valid Until:</strong> {{expires_at}}</p>

            <div style="margin: 15px 0;">
                <strong>Description:</strong><br>
                {{service_description}}
            </div>
        </div>
        
        <div class="amount">
            Total: ${{estimated_total}}
        </div>
        
        <p>This quote is valid until {{expires_at}}. To proceed with booking, please contact us at your earliest convenience.</p>
        
        <div style="text-align: center;">
            <a href="mailto:{{business_email}}" class="button">Accept Quote</a>
        </div>
        
        <p>If you have any questions or would like to discuss this quote, please don't hesitate to contact us.</p>
        
        <p>Best regards,<br>
        {{staff_name}}<br>
        {{business_name}}</p>
        
        <div class="footer">
            <p>{{business_name}}<br>
            Email: {{business_email}}<br>
            Phone: {{business_phone}}<br>
            Website: {{business_website}}</p>
        </div>
    </div>
</body>
</html>`,
    text_content: `
Hello {{customer_name}},

Thank you for your interest in our services! We're pleased to provide you with the following quote:

{{service_name}}
Quote Number: {{quote_number}}
{{#if event_date}}Event Date: {{event_date}}{{/if}}
Valid Until: {{expires_at}}

Description:
{{service_description}}

Total: ${{estimated_total}}

This quote is valid until {{expires_at}}. To proceed with booking, please contact us at your earliest convenience.

If you have any questions or would like to discuss this quote, please don't hesitate to contact us.

Best regards,
{{staff_name}}
{{business_name}}

Contact Information:
Email: {{business_email}}
Phone: {{business_phone}}
Website: {{business_website}}
`,
    template_type: 'quote',
    variables: ['customer_name', 'quote_number', 'service_name', 'service_description', 'estimated_total', 'expires_at', 'event_date', 'staff_name', 'business_name', 'business_email', 'business_phone', 'business_website'],
    is_active: true,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  },

  booking_confirmation: {
    id: 'fallback-booking-confirmation',
    name: 'Booking Confirmation Email Template',
    subject: 'Booking Confirmed - {{service_name}} on {{booking_date}}',
    html_content: `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Booking Confirmation - {{business_name}}</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 20px; background-color: #f4f4f4; }
        .container { max-width: 600px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 0 10px rgba(0,0,0,0.1); }
        .header { text-align: center; border-bottom: 3px solid #10b981; padding-bottom: 20px; margin-bottom: 30px; }
        .logo { font-size: 28px; font-weight: bold; color: #10b981; margin-bottom: 10px; }
        .booking-details { background: #f0fdf4; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #10b981; }
        .confirmation { background: #10b981; color: white; padding: 15px; border-radius: 8px; text-align: center; margin: 20px 0; }
        .footer { text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee; color: #666; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">{{business_name}}</div>
            <p>Professional Beauty Services</p>
        </div>
        
        <div class="confirmation">
            <h2 style="margin: 0;">✅ Booking Confirmed!</h2>
        </div>
        
        <h2>Hello {{customer_name}},</h2>
        
        <p>Great news! Your booking has been confirmed. Here are the details:</p>
        
        <div class="booking-details">
            <h3>{{service_name}}</h3>
            <p><strong>Booking Number:</strong> {{booking_number}}</p>
            <p><strong>Date:</strong> {{booking_date}}</p>
            <p><strong>Time:</strong> {{start_time}}{{#if end_time}} - {{end_time}}{{/if}}</p>
            <p><strong>Service Provider:</strong> {{staff_name}}</p>
            {{#if total_amount}}<p><strong>Total Amount:</strong> ${{total_amount}}</p>{{/if}}
            {{#if notes}}<p><strong>Notes:</strong> {{notes}}</p>{{/if}}
        </div>
        
        <p>We're looking forward to seeing you! Please arrive 5-10 minutes early for your appointment.</p>
        
        <p>If you need to reschedule or cancel your appointment, please contact us as soon as possible.</p>
        
        <p>Best regards,<br>
        {{staff_name}}<br>
        {{business_name}}</p>
        
        <div class="footer">
            <p>{{business_name}}<br>
            Email: {{business_email}}<br>
            Phone: {{business_phone}}<br>
            Website: {{business_website}}</p>
        </div>
    </div>
</body>
</html>`,
    text_content: `
✅ BOOKING CONFIRMED

Hello {{customer_name}},

Great news! Your booking has been confirmed. Here are the details:

{{service_name}}
Booking Number: {{booking_number}}
Date: {{booking_date}}
Time: {{start_time}}{{#if end_time}} - {{end_time}}{{/if}}
Service Provider: {{staff_name}}
{{#if total_amount}}Total Amount: ${{total_amount}}{{/if}}
{{#if notes}}Notes: {{notes}}{{/if}}

We're looking forward to seeing you! Please arrive 5-10 minutes early for your appointment.

If you need to reschedule or cancel your appointment, please contact us as soon as possible.

Best regards,
{{staff_name}}
{{business_name}}

Contact Information:
Email: {{business_email}}
Phone: {{business_phone}}
Website: {{business_website}}
`,
    template_type: 'booking_confirmation',
    variables: ['customer_name', 'booking_number', 'service_name', 'booking_date', 'start_time', 'end_time', 'staff_name', 'total_amount', 'notes', 'business_name', 'business_email', 'business_phone', 'business_website'],
    is_active: true,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  },

  reminder: {
    id: 'fallback-reminder',
    name: 'Quote Reminder Email Template',
    subject: 'Reminder: Your Quote from {{business_name}} Expires Soon',
    html_content: `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quote Reminder - {{business_name}}</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 20px; background-color: #f4f4f4; }
        .container { max-width: 600px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 0 10px rgba(0,0,0,0.1); }
        .header { text-align: center; border-bottom: 3px solid #f59e0b; padding-bottom: 20px; margin-bottom: 30px; }
        .logo { font-size: 28px; font-weight: bold; color: #f59e0b; margin-bottom: 10px; }
        .reminder-box { background: #fef3c7; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #f59e0b; }
        .urgency { background: #f59e0b; color: white; padding: 15px; border-radius: 8px; text-align: center; margin: 20px 0; }
        .footer { text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee; color: #666; }
        .button { display: inline-block; background: #f59e0b; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; margin: 20px 0; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">{{business_name}}</div>
            <p>Professional Beauty Services</p>
        </div>
        
        <div class="urgency">
            <h2 style="margin: 0;">⏰ Quote Expires in {{days_until_expiry}} days</h2>
        </div>
        
        <h2>Hello {{customer_name}},</h2>
        
        <p>We wanted to remind you about your pending quote that expires soon:</p>
        
        <div class="reminder-box">
            <h3>{{service_name}}</h3>
            <p><strong>Quote Number:</strong> {{quote_number}}</p>
            <p><strong>Amount:</strong> ${{estimated_total}}</p>
            <p><strong>Expires:</strong> {{expires_at}}</p>
        </div>
        
        <p>Don't miss out on securing your appointment! This quote expires in {{days_until_expiry}} days.</p>
        
        <div style="text-align: center;">
            <a href="mailto:{{business_email}}" class="button">Accept Quote Now</a>
        </div>
        
        <p>If you have any questions or would like to discuss this quote, please contact us immediately.</p>
        
        <p>Best regards,<br>
        {{staff_name}}<br>
        {{business_name}}</p>
        
        <div class="footer">
            <p>{{business_name}}<br>
            Email: {{business_email}}<br>
            Phone: {{business_phone}}<br>
            Website: {{business_website}}</p>
        </div>
    </div>
</body>
</html>`,
    text_content: `
⏰ QUOTE REMINDER - EXPIRES IN {{days_until_expiry}} DAYS

Hello {{customer_name}},

We wanted to remind you about your pending quote that expires soon:

{{service_name}}
Quote Number: {{quote_number}}
Amount: ${{estimated_total}}
Expires: {{expires_at}}

Don't miss out on securing your appointment! This quote expires in {{days_until_expiry}} days.

To accept this quote, please contact us immediately at {{business_email}} or {{business_phone}}.

If you have any questions or would like to discuss this quote, please contact us immediately.

Best regards,
{{staff_name}}
{{business_name}}

Contact Information:
Email: {{business_email}}
Phone: {{business_phone}}
Website: {{business_website}}
`,
    template_type: 'reminder',
    variables: ['customer_name', 'quote_number', 'service_name', 'estimated_total', 'expires_at', 'days_until_expiry', 'staff_name', 'business_name', 'business_email', 'business_phone', 'business_website'],
    is_active: true,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  },
};

/**
 * Get fallback template by type
 */
export function getFallbackTemplate(templateType: string): EmailTemplate | null {
  return fallbackEmailTemplates[templateType] || null;
}
