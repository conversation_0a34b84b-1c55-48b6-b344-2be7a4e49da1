# Ocean Soul Sparkles Mobile App

A cross-platform mobile application for Ocean Soul Sparkles, providing Point of Sale functionality and business management tools.

## 🚀 Quick Start

### Prerequisites
- Node.js 18+ 
- npm or yarn
- Expo CLI (`npm install -g @expo/cli`)
- iOS Simulator (Mac) or Android Studio (for emulators)

### Installation
```bash
cd oceansoulapp
npm install
npm start
```

## 📱 Features

### Core Functionality
- **Point of Sale (POS)** - Square payment integration
- **Staff Management** - Team member CRUD operations
- **Product & Service Management** - Inventory and service catalog
- **Booking Management** - Appointments and scheduling
- **Quote Management** - Estimates and proposals
- **Basic Invoicing** - Simple invoice generation

### Technical Features
- Cross-platform (iOS & Android)
- Offline-capable with sync
- Real-time data updates
- Secure authentication
- Square payment processing

## 🏗️ Architecture

### Technology Stack
- **React Native** with TypeScript
- **Expo SDK** for development workflow
- **Supabase** for database and authentication
- **Square SDK** for payment processing
- **Zustand** for state management
- **React Query** for data fetching

### Database Integration
- Shares Supabase database with admin portal
- Real-time synchronization
- Row Level Security (RLS) for access control

## 🔧 Development

### Environment Setup
1. Copy `.env.example` to `.env.local`
2. Configure Supabase and Square credentials
3. Run `npm start` to start development server

### Available Scripts
- `npm start` - Start Expo development server
- `npm run android` - Run on Android emulator
- `npm run ios` - Run on iOS simulator
- `npm run build` - Create production build
- `npm test` - Run tests
- `npm run lint` - Run ESLint

## 📦 Project Structure

```
src/
├── components/     # Reusable UI components
├── screens/        # Screen components
├── navigation/     # Navigation configuration
├── services/       # API and business logic
├── hooks/          # Custom React hooks
├── store/          # State management
├── utils/          # Utility functions
├── types/          # TypeScript definitions
└── assets/         # Static assets
```

## 🚀 Deployment

### Development
- Use Expo Go app for testing
- Hot reload for rapid development

### Production
- EAS Build for app store builds
- Automated CI/CD pipeline
- App Store and Google Play deployment

## 🔐 Security

- Secure credential storage
- API key protection
- User authentication via Supabase
- PCI compliance for payments

## 📄 License

Private - Ocean Soul Sparkles Internal Use Only
