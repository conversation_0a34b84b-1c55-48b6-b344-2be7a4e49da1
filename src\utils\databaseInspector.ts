/**
 * Ocean Soul Sparkles - Database Inspector Utility
 * Helps identify existing table structures and relationships
 */

import { supabase } from '@/services/database/supabase';

export class DatabaseInspector {
  
  /**
   * Get all table names in the public schema
   */
  static async getAllTables(): Promise<string[]> {
    try {
      const { data, error } = await supabase
        .from('information_schema.tables')
        .select('table_name')
        .eq('table_schema', 'public');
      
      if (error) {
        return [];
      }
      
      return data?.map(row => row.table_name) || [];
    } catch (error) {
      return [];
    }
  }
  
  /**
   * Find tables that might be related to transactions
   */
  static async findTransactionTables(): Promise<string[]> {
    try {
      const allTables = await this.getAllTables();
      const transactionTables = allTables.filter(table => 
        table.toLowerCase().includes('transaction') ||
        table.toLowerCase().includes('sale') ||
        table.toLowerCase().includes('order') ||
        table.toLowerCase().includes('payment')
      );
      
      console.log('🔍 Found potential transaction tables:', transactionTables);
      return transactionTables;
    } catch (error) {
      console.error('❌ Error finding transaction tables:', error);
      return [];
    }
  }
  
  /**
   * Test if a table exists and is accessible
   */
  static async testTableAccess(tableName: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from(tableName)
        .select('*')
        .limit(1);
      
      if (error) {
        console.log(`❌ Table '${tableName}' not accessible:`, error.message);
        return false;
      }
      
      console.log(`✅ Table '${tableName}' is accessible`);
      return true;
    } catch (error) {
      console.log(`❌ Table '${tableName}' test failed:`, error);
      return false;
    }
  }
  
  /**
   * Get table structure for a specific table
   */
  static async getTableStructure(tableName: string): Promise<any[]> {
    try {
      // Try to get a sample record to understand the structure
      const { data, error } = await supabase
        .from(tableName)
        .select('*')
        .limit(1);
      
      if (error) {
        console.error(`❌ Error getting structure for ${tableName}:`, error);
        return [];
      }
      
      if (data && data.length > 0) {
        const columns = Object.keys(data[0]);
        console.log(`📋 Table '${tableName}' columns:`, columns);
        return columns;
      }
      
      console.log(`📋 Table '${tableName}' is empty, cannot determine structure`);
      return [];
    } catch (error) {
      console.error(`❌ Error inspecting ${tableName}:`, error);
      return [];
    }
  }
  
  /**
   * Comprehensive database inspection
   */
  static async inspectDatabase(): Promise<{
    allTables: string[];
    transactionTables: string[];
    accessibleTables: string[];
    tableStructures: Record<string, string[]>;
  }> {
    console.log('🔍 Starting comprehensive database inspection...');
    
    const allTables = await this.getAllTables();
    console.log('📊 All tables found:', allTables);
    
    const transactionTables = await this.findTransactionTables();
    console.log('💳 Transaction-related tables:', transactionTables);
    
    const accessibleTables: string[] = [];
    const tableStructures: Record<string, string[]> = {};
    
    // Test access to key tables
    const tablesToTest = [
      'transactions',
      'loyalty_transactions', 
      'sales',
      'orders',
      'payments',
      'transaction_items',
      'sale_items',
      'order_items',
      ...transactionTables
    ];
    
    for (const table of tablesToTest) {
      const isAccessible = await this.testTableAccess(table);
      if (isAccessible) {
        accessibleTables.push(table);
        const structure = await this.getTableStructure(table);
        tableStructures[table] = structure;
      }
    }
    
    console.log('✅ Accessible tables:', accessibleTables);
    console.log('📋 Table structures:', tableStructures);
    
    return {
      allTables,
      transactionTables,
      accessibleTables,
      tableStructures
    };
  }
  
  /**
   * Find the best table to use for transactions
   */
  static async findBestTransactionTable(): Promise<string | null> {
    const inspection = await this.inspectDatabase();
    
    // Priority order for transaction tables
    const priorities = [
      'transactions',
      'loyalty_transactions',
      'sales',
      'orders',
      'payments'
    ];
    
    for (const table of priorities) {
      if (inspection.accessibleTables.includes(table)) {
        console.log(`🎯 Best transaction table found: ${table}`);
        return table;
      }
    }
    
    // If none of the priority tables exist, use the first transaction-related table
    if (inspection.transactionTables.length > 0) {
      const fallback = inspection.transactionTables[0];
      console.log(`🔄 Using fallback transaction table: ${fallback}`);
      return fallback;
    }
    
    console.log('❌ No suitable transaction table found');
    return null;
  }
}

// Export a simple function to run the inspection
export const inspectDatabase = () => DatabaseInspector.inspectDatabase();
export const findBestTransactionTable = () => DatabaseInspector.findBestTransactionTable();
