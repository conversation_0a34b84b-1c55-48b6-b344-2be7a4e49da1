/**
 * Ocean Soul Sparkles Mobile App - Acknowledgment Workflow Service
 * Handles booking and quote acknowledgment workflows using SQL functions
 */

import { sqlFunctionsService, AcknowledgmentResult } from '@/services/database/sqlFunctionsService';
import { supabase } from '@/services/database/supabase';
import { emailService } from '@/services/email/emailService';
import { Booking, Quote, Customer, DatabaseResponse } from '@/types/database';

export interface AcknowledgmentWorkflowResult {
  success: boolean;
  acknowledgment_id?: string;
  email_sent?: boolean;
  sms_sent?: boolean;
  steps: AcknowledgmentWorkflowStep[];
  error?: string;
}

export interface AcknowledgmentWorkflowStep {
  step: string;
  status: 'pending' | 'in_progress' | 'completed' | 'failed';
  message: string;
  duration?: number;
  error?: string;
}

export interface AcknowledgmentConfig {
  send_email: boolean;
  send_sms: boolean;
  email_template: string;
  include_calendar_invite: boolean;
  send_copy_to_staff: boolean;
  auto_follow_up: boolean;
  follow_up_hours: number;
}

export interface PendingAcknowledgment {
  id: string;
  type: 'booking' | 'quote';
  reference_id: string;
  customer_id: string;
  status: 'pending' | 'sent' | 'failed';
  created_at: string;
  attempts: number;
  last_attempt?: string;
  error_message?: string;
}

export class AcknowledgmentWorkflowService {
  private static instance: AcknowledgmentWorkflowService;

  private readonly DEFAULT_BOOKING_CONFIG: AcknowledgmentConfig = {
    send_email: true,
    send_sms: false,
    email_template: 'booking_confirmation',
    include_calendar_invite: true,
    send_copy_to_staff: true,
    auto_follow_up: false,
    follow_up_hours: 24,
  };

  private readonly DEFAULT_QUOTE_CONFIG: AcknowledgmentConfig = {
    send_email: true,
    send_sms: false,
    email_template: 'quote_acknowledgment',
    include_calendar_invite: false,
    send_copy_to_staff: true,
    auto_follow_up: true,
    follow_up_hours: 48,
  };

  private constructor() {}

  public static getInstance(): AcknowledgmentWorkflowService {
    if (!AcknowledgmentWorkflowService.instance) {
      AcknowledgmentWorkflowService.instance = new AcknowledgmentWorkflowService();
    }
    return AcknowledgmentWorkflowService.instance;
  }

  /**
   * Create booking acknowledgment with complete workflow
   */
  async createBookingAcknowledgment(
    bookingId: string,
    config: Partial<AcknowledgmentConfig> = {}
  ): Promise<AcknowledgmentWorkflowResult> {
    const finalConfig = { ...this.DEFAULT_BOOKING_CONFIG, ...config };
    const steps: AcknowledgmentWorkflowStep[] = [];

    try {
      console.log('📧 Starting booking acknowledgment workflow for:', bookingId);

      // Step 1: Validate booking
      steps.push(this.createStep('validate_booking', 'in_progress', 'Validating booking...'));
      
      const booking = await this.getBookingById(bookingId);
      if (!booking) {
        steps[steps.length - 1] = this.updateStep(steps[steps.length - 1], 'failed', 'Booking not found');
        return { success: false, steps, error: 'Booking not found' };
      }

      steps[steps.length - 1] = this.updateStep(steps[steps.length - 1], 'completed', 'Booking validated');

      // Step 2: Create acknowledgment using SQL function
      steps.push(this.createStep('create_acknowledgment', 'in_progress', 'Creating acknowledgment record...'));
      
      const acknowledgmentResult = await sqlFunctionsService.createBookingAcknowledgment(bookingId);
      if (!acknowledgmentResult.success) {
        steps[steps.length - 1] = this.updateStep(steps[steps.length - 1], 'failed', acknowledgmentResult.error || 'Acknowledgment creation failed');
        return { success: false, steps, error: acknowledgmentResult.error };
      }

      steps[steps.length - 1] = this.updateStep(steps[steps.length - 1], 'completed', 'Acknowledgment record created');

      // Step 3: Send email notification
      let emailSent = false;
      if (finalConfig.send_email) {
        steps.push(this.createStep('send_email', 'in_progress', 'Sending acknowledgment email...'));
        
        const emailResult = await this.sendBookingAcknowledgmentEmail(booking, finalConfig);
        emailSent = emailResult.success;
        
        if (emailResult.success) {
          steps[steps.length - 1] = this.updateStep(steps[steps.length - 1], 'completed', 'Acknowledgment email sent');
        } else {
          steps[steps.length - 1] = this.updateStep(steps[steps.length - 1], 'failed', emailResult.error || 'Email sending failed');
        }
      }

      // Step 4: Send SMS notification (if configured)
      let smsSent = false;
      if (finalConfig.send_sms) {
        steps.push(this.createStep('send_sms', 'in_progress', 'Sending SMS notification...'));
        
        const smsResult = await this.sendBookingAcknowledgmentSMS(booking);
        smsSent = smsResult.success;
        
        if (smsResult.success) {
          steps[steps.length - 1] = this.updateStep(steps[steps.length - 1], 'completed', 'SMS notification sent');
        } else {
          steps[steps.length - 1] = this.updateStep(steps[steps.length - 1], 'failed', smsResult.error || 'SMS sending failed');
        }
      }

      console.log(`✅ Booking acknowledgment workflow completed: ${acknowledgmentResult.acknowledgment_id}`);

      return {
        success: true,
        acknowledgment_id: acknowledgmentResult.acknowledgment_id,
        email_sent: emailSent,
        sms_sent: smsSent,
        steps,
      };

    } catch (error) {
      console.error('❌ Booking acknowledgment workflow failed:', error);
      
      if (steps.length > 0) {
        steps[steps.length - 1] = this.updateStep(
          steps[steps.length - 1], 
          'failed', 
          error instanceof Error ? error.message : 'Unknown error'
        );
      }

      return {
        success: false,
        steps,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Send quote acknowledgment with complete workflow
   */
  async sendQuoteAcknowledgment(
    quoteId: string,
    config: Partial<AcknowledgmentConfig> = {}
  ): Promise<AcknowledgmentWorkflowResult> {
    const finalConfig = { ...this.DEFAULT_QUOTE_CONFIG, ...config };
    const steps: AcknowledgmentWorkflowStep[] = [];

    try {
      console.log('📧 Starting quote acknowledgment workflow for:', quoteId);

      // Step 1: Validate quote
      steps.push(this.createStep('validate_quote', 'in_progress', 'Validating quote...'));
      
      const quote = await this.getQuoteById(quoteId);
      if (!quote) {
        steps[steps.length - 1] = this.updateStep(steps[steps.length - 1], 'failed', 'Quote not found');
        return { success: false, steps, error: 'Quote not found' };
      }

      steps[steps.length - 1] = this.updateStep(steps[steps.length - 1], 'completed', 'Quote validated');

      // Step 2: Send acknowledgment using SQL function
      steps.push(this.createStep('send_acknowledgment', 'in_progress', 'Sending acknowledgment...'));
      
      const acknowledgmentResult = await sqlFunctionsService.sendQuoteAcknowledgment(quoteId);
      if (!acknowledgmentResult.success) {
        steps[steps.length - 1] = this.updateStep(steps[steps.length - 1], 'failed', acknowledgmentResult.error || 'Acknowledgment sending failed');
        return { success: false, steps, error: acknowledgmentResult.error };
      }

      steps[steps.length - 1] = this.updateStep(steps[steps.length - 1], 'completed', 'Acknowledgment sent');

      // Step 3: Send email notification
      let emailSent = false;
      if (finalConfig.send_email) {
        steps.push(this.createStep('send_email', 'in_progress', 'Sending acknowledgment email...'));
        
        const emailResult = await this.sendQuoteAcknowledgmentEmail(quote, finalConfig);
        emailSent = emailResult.success;
        
        if (emailResult.success) {
          steps[steps.length - 1] = this.updateStep(steps[steps.length - 1], 'completed', 'Acknowledgment email sent');
        } else {
          steps[steps.length - 1] = this.updateStep(steps[steps.length - 1], 'failed', emailResult.error || 'Email sending failed');
        }
      }

      console.log(`✅ Quote acknowledgment workflow completed: ${acknowledgmentResult.acknowledgment_id}`);

      return {
        success: true,
        acknowledgment_id: acknowledgmentResult.acknowledgment_id,
        email_sent: emailSent,
        sms_sent: false,
        steps,
      };

    } catch (error) {
      console.error('❌ Quote acknowledgment workflow failed:', error);
      
      if (steps.length > 0) {
        steps[steps.length - 1] = this.updateStep(
          steps[steps.length - 1], 
          'failed', 
          error instanceof Error ? error.message : 'Unknown error'
        );
      }

      return {
        success: false,
        steps,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Get pending acknowledgments
   */
  async getPendingAcknowledgments(): Promise<{ success: boolean; acknowledgments?: PendingAcknowledgment[]; error?: string }> {
    try {
      console.log('📋 Getting pending acknowledgments...');

      const result = await sqlFunctionsService.getPendingAcknowledgments();
      
      if (!result.success) {
        return { success: false, error: result.error };
      }

      return { success: true, acknowledgments: result.data || [] };

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Process pending acknowledgments
   */
  async processPendingAcknowledgments(): Promise<{ processed: number; failed: number; errors: string[] }> {
    try {
      console.log('⚙️ Processing pending acknowledgments...');

      const pendingResult = await this.getPendingAcknowledgments();
      if (!pendingResult.success || !pendingResult.acknowledgments) {
        return { processed: 0, failed: 1, errors: [pendingResult.error || 'Failed to get pending acknowledgments'] };
      }

      let processed = 0;
      let failed = 0;
      const errors: string[] = [];

      for (const acknowledgment of pendingResult.acknowledgments) {
        try {
          let result: AcknowledgmentWorkflowResult;

          if (acknowledgment.type === 'booking') {
            result = await this.createBookingAcknowledgment(acknowledgment.reference_id);
          } else {
            result = await this.sendQuoteAcknowledgment(acknowledgment.reference_id);
          }

          if (result.success) {
            processed++;
          } else {
            failed++;
            errors.push(`${acknowledgment.type} ${acknowledgment.reference_id}: ${result.error}`);
          }

        } catch (error) {
          failed++;
          errors.push(`${acknowledgment.type} ${acknowledgment.reference_id}: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
      }

      console.log(`✅ Processed ${processed} acknowledgments, ${failed} failed`);
      return { processed, failed, errors };

    } catch (error) {
      return {
        processed: 0,
        failed: 1,
        errors: [error instanceof Error ? error.message : 'Unknown error'],
      };
    }
  }

  /**
   * Get booking by ID
   */
  private async getBookingById(bookingId: string): Promise<Booking | null> {
    try {
      const { data, error } = await supabase
        .from('bookings')
        .select('*, customer:customers(*), service:services(*)')
        .eq('id', bookingId)
        .single();

      if (error || !data) {
        console.error('Booking retrieval failed:', error);
        return null;
      }

      return data;

    } catch (error) {
      console.error('Booking retrieval error:', error);
      return null;
    }
  }

  /**
   * Get quote by ID
   */
  private async getQuoteById(quoteId: string): Promise<Quote | null> {
    try {
      const { data, error } = await supabase
        .from('quotes')
        .select('*, customer:customers(*)')
        .eq('id', quoteId)
        .single();

      if (error || !data) {
        console.error('Quote retrieval failed:', error);
        return null;
      }

      return data;

    } catch (error) {
      console.error('Quote retrieval error:', error);
      return null;
    }
  }

  /**
   * Send booking acknowledgment email
   */
  private async sendBookingAcknowledgmentEmail(
    booking: Booking,
    config: AcknowledgmentConfig
  ): Promise<{ success: boolean; error?: string }> {
    try {
      if (!booking.customer) {
        return { success: false, error: 'Customer information not available' };
      }

      const emailResult = await emailService.sendEmail({
        to: booking.customer.email,
        subject: `Booking Confirmation - Ocean Soul Sparkles`,
        template: config.email_template,
        variables: {
          customer_name: booking.customer.full_name,
          booking_date: new Date(booking.booking_date).toLocaleDateString(),
          booking_time: booking.start_time,
          service_name: booking.service?.name || 'Service',
          total_amount: booking.total_amount?.toFixed(2) || '0.00',
        },
      });

      return { success: emailResult.success, error: emailResult.error };

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Send quote acknowledgment email
   */
  private async sendQuoteAcknowledgmentEmail(
    quote: Quote,
    config: AcknowledgmentConfig
  ): Promise<{ success: boolean; error?: string }> {
    try {
      if (!quote.customer) {
        return { success: false, error: 'Customer information not available' };
      }

      const emailResult = await emailService.sendEmail({
        to: quote.customer.email,
        subject: `Quote ${quote.service_name} - Ocean Soul Sparkles`,
        template: config.email_template,
        variables: {
          customer_name: quote.customer.full_name,
          quote_title: quote.service_name,
          estimated_total: quote.estimated_total.toFixed(2),
          valid_until: quote.expires_at ? new Date(quote.expires_at).toLocaleDateString() : 'Contact us',
        },
      });

      return { success: emailResult.success, error: emailResult.error };

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Send booking acknowledgment SMS
   */
  private async sendBookingAcknowledgmentSMS(booking: Booking): Promise<{ success: boolean; error?: string }> {
    try {
      // SMS functionality would be implemented here
      // For now, return success as placeholder
      console.log('📱 SMS functionality not yet implemented');
      return { success: true };

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Create workflow step
   */
  private createStep(step: string, status: AcknowledgmentWorkflowStep['status'], message: string): AcknowledgmentWorkflowStep {
    return { step, status, message };
  }

  /**
   * Update workflow step
   */
  private updateStep(
    step: AcknowledgmentWorkflowStep, 
    status: AcknowledgmentWorkflowStep['status'], 
    message: string,
    error?: string
  ): AcknowledgmentWorkflowStep {
    return { ...step, status, message, error };
  }
}

// Export singleton instance
export const acknowledgmentWorkflowService = AcknowledgmentWorkflowService.getInstance();
