/**
 * Ocean Soul Sparkles Mobile App - Products Management Screen
 * Displays products and services with search, categories, and management functionality
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  FlatList,
  TouchableOpacity,
  TextInput,
  ActivityIndicator,
  Alert,
  RefreshControl,
  Image,
} from 'react-native';
import { Product, Service } from '@/types/database';
import { productService } from '@/services/database/productService';

const ProductsScreen: React.FC = () => {
  const [products, setProducts] = useState<Product[]>([]);
  const [services, setServices] = useState<Service[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [activeTab, setActiveTab] = useState<'all' | 'products' | 'services'>('all');
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);

  useEffect(() => {
    loadData();
  }, [searchQuery, selectedCategory]);

  const loadData = async (isRefresh = false) => {
    try {
      if (isRefresh) {
        setRefreshing(true);
      } else {
        setLoading(true);
      }

      const [productsResult, servicesResult] = await Promise.all([
        productService.getProducts({
          search: searchQuery,
          filters: {
            category: selectedCategory || undefined,
          },
          order_by: 'name',
          order_direction: 'asc',
        }),
        productService.getServices({
          search: searchQuery,
          filters: {
            category: selectedCategory || undefined,
          },
          order_by: 'name',
          order_direction: 'asc',
        })
      ]);

      if (productsResult.error) {
        console.error('Error loading products:', productsResult.error);
      } else {
        setProducts(productsResult.data || []);
      }

      if (servicesResult.error) {
        console.error('Error loading services:', servicesResult.error);
      } else {
        setServices(servicesResult.data || []);
      }
    } catch (error) {
      console.error('Load data error:', error);
      Alert.alert('Error', 'Failed to load products and services');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const getFilteredItems = () => {
    const allItems = [
      ...products.map(p => ({ item: p, type: 'product' as const })),
      ...services.map(s => ({ item: s, type: 'service' as const }))
    ];

    switch (activeTab) {
      case 'products':
        return products.map(p => ({ item: p, type: 'product' as const }));
      case 'services':
        return services.map(s => ({ item: s, type: 'service' as const }));
      default:
        return allItems;
    }
  };

  const getCategories = () => {
    const productCategories = products.map(p => p.category).filter(Boolean);
    const serviceCategories = services.map(s => s.category).filter(Boolean);
    return [...new Set([...productCategories, ...serviceCategories])];
  };

  const formatPrice = (price: number | undefined) => {
    if (price === undefined || price === null) {
      return '$0.00';
    }
    return `$${price.toFixed(2)}`;
  };

  const getItemIcon = (type: 'product' | 'service', category?: string) => {
    if (type === 'service') {
      if (category?.toLowerCase().includes('hair')) return '💇‍♀️';
      if (category?.toLowerCase().includes('body')) return '🎨';
      if (category?.toLowerCase().includes('face')) return '✨';
      return '🌟';
    } else {
      if (category?.toLowerCase().includes('airbrush')) return '🎨';
      if (category?.toLowerCase().includes('hair')) return '💇‍♀️';
      if (category?.toLowerCase().includes('body')) return '🎭';
      return '📦';
    }
  };

  const getStockStatus = (product: Product) => {
    if (product.stock_quantity === undefined) return null;
    if (product.stock_quantity <= 0) return { text: 'Out of Stock', color: '#ef4444' };
    if (product.stock_quantity <= 5) return { text: 'Low Stock', color: '#f59e0b' };
    return { text: 'In Stock', color: '#10b981' };
  };
  const renderItem = ({ item }: { item: { item: Product | Service; type: 'product' | 'service' } }) => {
    const { item: data, type } = item;
    const price = type === 'product' ? (data as Product).price : (data as Service).base_price;
    const stockStatus = type === 'product' ? getStockStatus(data as Product) : null;

    return (
      <TouchableOpacity style={styles.itemCard} activeOpacity={0.7}>
        <View style={styles.itemImageContainer}>
          {data.image_url ? (
            <Image
              source={{ uri: data.image_url }}
              style={styles.itemImage}
              resizeMode="cover"
            />
          ) : (
            <View style={styles.placeholderImage}>
              <Text style={styles.placeholderIcon}>
                {getItemIcon(type, data.category)}
              </Text>
            </View>
          )}

          <View style={styles.typeBadge}>
            <Text style={styles.typeBadgeText}>
              {type === 'product' ? '📦' : '🌟'} {type.toUpperCase()}
            </Text>
          </View>
        </View>

        <View style={styles.itemInfo}>
          <Text style={styles.itemName} numberOfLines={2}>
            {data.name}
          </Text>

          {data.description && (
            <Text style={styles.itemDescription} numberOfLines={2}>
              {data.description}
            </Text>
          )}

          <View style={styles.itemFooter}>
            <Text style={styles.itemPrice}>{formatPrice(price)}</Text>

            {type === 'service' && (data as Service).duration_minutes && (
              <Text style={styles.duration}>
                ⏱️ {(data as Service).duration_minutes}min
              </Text>
            )}

            {stockStatus && (
              <View style={[styles.stockBadge, { backgroundColor: stockStatus.color }]}>
                <Text style={styles.stockText}>{stockStatus.text}</Text>
              </View>
            )}
          </View>

          {data.category && (
            <Text style={styles.category}>🏷️ {data.category}</Text>
          )}
        </View>
      </TouchableOpacity>
    );
  };

  const renderTabNavigation = () => (
    <View style={styles.tabContainer}>
      <TouchableOpacity
        style={[styles.tab, activeTab === 'all' && styles.activeTab]}
        onPress={() => setActiveTab('all')}
      >
        <Text style={[styles.tabText, activeTab === 'all' && styles.activeTabText]}>
          All ({products.length + services.length})
        </Text>
      </TouchableOpacity>
      <TouchableOpacity
        style={[styles.tab, activeTab === 'products' && styles.activeTab]}
        onPress={() => setActiveTab('products')}
      >
        <Text style={[styles.tabText, activeTab === 'products' && styles.activeTabText]}>
          📦 Products ({products.length})
        </Text>
      </TouchableOpacity>
      <TouchableOpacity
        style={[styles.tab, activeTab === 'services' && styles.activeTab]}
        onPress={() => setActiveTab('services')}
      >
        <Text style={[styles.tabText, activeTab === 'services' && styles.activeTabText]}>
          🌟 Services ({services.length})
        </Text>
      </TouchableOpacity>
    </View>
  );

  const renderCategoryFilters = () => {
    const categories = getCategories();
    if (categories.length === 0) return null;

    return (
      <View style={styles.categoryContainer}>
        <TouchableOpacity
          style={[styles.categoryFilter, !selectedCategory && styles.activeCategoryFilter]}
          onPress={() => setSelectedCategory(null)}
        >
          <Text style={[styles.categoryFilterText, !selectedCategory && styles.activeCategoryFilterText]}>
            All Categories
          </Text>
        </TouchableOpacity>

        {categories.map(category => (
          <TouchableOpacity
            key={category}
            style={[styles.categoryFilter, selectedCategory === category && styles.activeCategoryFilter]}
            onPress={() => setSelectedCategory(selectedCategory === category ? null : category)}
          >
            <Text style={[styles.categoryFilterText, selectedCategory === category && styles.activeCategoryFilterText]}>
              {category}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    );
  };

  const renderStatsHeader = () => (
    <View style={styles.statsContainer}>
      <View style={styles.statCard}>
        <Text style={styles.statNumber}>{products.length + services.length}</Text>
        <Text style={styles.statLabel}>Total Items</Text>
      </View>
      <View style={styles.statCard}>
        <Text style={[styles.statNumber, { color: '#f59e0b' }]}>{products.length}</Text>
        <Text style={styles.statLabel}>Products</Text>
      </View>
      <View style={styles.statCard}>
        <Text style={[styles.statNumber, { color: '#8b5cf6' }]}>{services.length}</Text>
        <Text style={styles.statLabel}>Services</Text>
      </View>
    </View>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyContainer}>
      <Text style={styles.emptyIcon}>📦</Text>
      <Text style={styles.emptyText}>No items found</Text>
      <Text style={styles.emptySubtext}>
        {searchQuery
          ? 'Try adjusting your search terms'
          : 'Products and services will appear here'
        }
      </Text>
    </View>
  );

  const filteredItems = getFilteredItems();

  if (loading && filteredItems.length === 0) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#FF9A8B" />
          <Text style={styles.loadingText}>Loading products and services...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.headerTitle}>📦 Products & Services</Text>
      </View>

      {/* Stats */}
      {renderStatsHeader()}

      {/* Search */}
      <View style={styles.searchContainer}>
        <TextInput
          style={styles.searchInput}
          placeholder="Search products and services..."
          value={searchQuery}
          onChangeText={setSearchQuery}
          clearButtonMode="while-editing"
        />
      </View>

      {/* Tab Navigation */}
      {renderTabNavigation()}

      {/* Category Filters */}
      {renderCategoryFilters()}

      {/* Items Grid */}
      <FlatList
        data={filteredItems}
        renderItem={renderItem}
        keyExtractor={(item) => `${item.type}-${item.item.id}`}
        numColumns={2}
        contentContainerStyle={styles.listContainer}
        columnWrapperStyle={styles.row}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={() => loadData(true)}
            colors={['#FF9A8B']}
          />
        }
        ListEmptyComponent={!loading ? renderEmptyState : null}
      />
    </SafeAreaView>
  );
};
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    backgroundColor: '#FF9A8B',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomLeftRadius: 20,
    borderBottomRightRadius: 20,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#fff',
    textAlign: 'center',
  },
  statsContainer: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingVertical: 16,
    justifyContent: 'space-between',
  },
  statCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    flex: 1,
    marginHorizontal: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  statNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
  },
  statLabel: {
    fontSize: 12,
    color: '#666',
    marginTop: 4,
  },
  searchContainer: {
    paddingHorizontal: 20,
    paddingBottom: 16,
  },
  searchInput: {
    backgroundColor: '#fff',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  tabContainer: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingBottom: 16,
  },
  tab: {
    backgroundColor: '#fff',
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginRight: 12,
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  activeTab: {
    backgroundColor: '#FF9A8B',
    borderColor: '#FF9A8B',
  },
  tabText: {
    fontSize: 14,
    color: '#666',
    fontWeight: '500',
  },
  activeTabText: {
    color: '#fff',
  },
  categoryContainer: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingBottom: 16,
    flexWrap: 'wrap',
  },
  categoryFilter: {
    backgroundColor: '#fff',
    borderRadius: 16,
    paddingHorizontal: 12,
    paddingVertical: 6,
    marginRight: 8,
    marginBottom: 8,
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  activeCategoryFilter: {
    backgroundColor: '#8b5cf6',
    borderColor: '#8b5cf6',
  },
  categoryFilterText: {
    fontSize: 12,
    color: '#666',
    fontWeight: '500',
  },
  activeCategoryFilterText: {
    color: '#fff',
  },
  listContainer: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  row: {
    justifyContent: 'space-between',
  },
  itemCard: {
    backgroundColor: '#fff',
    borderRadius: 16,
    marginBottom: 16,
    width: '48%',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  itemImageContainer: {
    position: 'relative',
    height: 120,
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    overflow: 'hidden',
  },
  itemImage: {
    width: '100%',
    height: '100%',
  },
  placeholderImage: {
    width: '100%',
    height: '100%',
    backgroundColor: '#f0f0f0',
    justifyContent: 'center',
    alignItems: 'center',
  },
  placeholderIcon: {
    fontSize: 32,
  },
  typeBadge: {
    position: 'absolute',
    top: 8,
    right: 8,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    borderRadius: 8,
    paddingHorizontal: 6,
    paddingVertical: 2,
  },
  typeBadgeText: {
    color: '#fff',
    fontSize: 10,
    fontWeight: 'bold',
  },
  itemInfo: {
    padding: 12,
  },
  itemName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  itemDescription: {
    fontSize: 12,
    color: '#666',
    marginBottom: 8,
  },
  itemFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  itemPrice: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#FF9A8B',
  },
  duration: {
    fontSize: 10,
    color: '#666',
  },
  stockBadge: {
    borderRadius: 8,
    paddingHorizontal: 6,
    paddingVertical: 2,
  },
  stockText: {
    color: '#fff',
    fontSize: 10,
    fontWeight: 'bold',
  },
  category: {
    fontSize: 10,
    color: '#666',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    color: '#666',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 60,
  },
  emptyIcon: {
    fontSize: 48,
    marginBottom: 16,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  emptySubtext: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    paddingHorizontal: 40,
  },
});

export default ProductsScreen;