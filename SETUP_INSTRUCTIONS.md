# Ocean Soul Sparkles Mobile App - Setup Instructions

## 🚀 Quick Setup Guide

### 1. Prerequisites Installation

**Required Software:**
```bash
# Node.js 18+ (recommended: use nvm)
node --version  # Should be 18.0.0 or higher

# Install Expo CLI globally
npm install -g @expo/cli

# Install EAS CLI for builds
npm install -g eas-cli
```

**Development Environment:**
- **iOS Development**: Xcode (Mac only) + iOS Simulator
- **Android Development**: Android Studio + Android Emulator
- **Physical Device Testing**: Expo Go app (iOS/Android)

### 2. Project Setup

```bash
# Navigate to project directory
cd oceansoulapp

# Install dependencies
npm install

# Copy environment configuration
cp .env.example .env.local

# Edit environment variables (see section below)
# nano .env.local  # or use your preferred editor
```

### 3. Environment Configuration

Edit `.env.local` with your actual credentials:

```bash
# Supabase (shared with admin portal)
EXPO_PUBLIC_SUPABASE_URL=https://ndlgbcsbidyhxbpqzgqp.supabase.co
EXPO_PUBLIC_SUPABASE_ANON_KEY=your-actual-supabase-anon-key

# Square Payments (shared with admin portal)
EXPO_PUBLIC_SQUARE_APPLICATION_ID=*****************************
EXPO_PUBLIC_SQUARE_LOCATION_ID=LBZPW61WHXG6F
SQUARE_ACCESS_TOKEN=your-actual-square-access-token

# API Configuration
EXPO_PUBLIC_API_BASE_URL=https://admin.oceansoulsparkles.com.au/api

# App Configuration
EXPO_PUBLIC_APP_NAME=Ocean Soul Sparkles
EXPO_PUBLIC_ENVIRONMENT=development
EXPO_PUBLIC_DEBUG_MODE=true
```

### 4. Git Repository Setup

```bash
# Initialize Git repository
git init

# Add all files
git add .

# Initial commit
git commit -m "Initial Ocean Soul Sparkles mobile app setup"

# Add remote repository (replace with your actual repo URL)
git remote add origin https://github.com/Thorlee15/oceansoulapp.git

# Push to remote
git push -u origin main
```

### 5. Development Workflow

```bash
# Start development server
npm start

# Run on specific platforms
npm run ios      # iOS Simulator (Mac only)
npm run android  # Android Emulator
npm run web      # Web browser (for testing)

# Test on physical device
# 1. Install Expo Go app on your phone
# 2. Scan QR code from terminal
# 3. App will load on your device
```

## 🔧 Advanced Setup

### EAS Build Configuration

```bash
# Login to Expo account
eas login

# Configure EAS project
eas build:configure

# Create development build
eas build --platform all --profile development

# Create production build
eas build --platform all --profile production
```

### Database Setup Verification

The app shares the same Supabase database as your admin portal. Verify connection:

```bash
# Test database connection
npm run test:db

# Check authentication flow
npm run test:auth
```

### Square Payment Testing

```bash
# Test Square SDK initialization
npm run test:payments

# Use Square's test card numbers for development:
# Visa: 4111 1111 1111 1111
# Mastercard: 5555 5555 5555 4444
# CVV: Any 3 digits
# Expiry: Any future date
```

## 📱 Platform-Specific Setup

### iOS Setup (Mac Required)

```bash
# Install iOS dependencies
cd ios && pod install && cd ..

# Open in Xcode (if needed)
xed ios

# Run on iOS Simulator
npm run ios
```

### Android Setup

```bash
# Ensure Android SDK is installed
# Set ANDROID_HOME environment variable

# Run on Android Emulator
npm run android

# For physical device, enable USB debugging
# and run: adb devices
```

## 🧪 Testing Setup

```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run type checking
npm run type-check

# Run linting
npm run lint
```

## 🚀 Deployment Setup

### Development Deployment

```bash
# Create development build
eas build --platform all --profile development

# Install on device
eas build:run --platform ios
eas build:run --platform android
```

### Production Deployment

```bash
# Create production build
eas build --platform all --profile production

# Submit to app stores
eas submit --platform all
```

## 🔐 Security Checklist

- [ ] Environment variables configured correctly
- [ ] Supabase RLS policies enabled
- [ ] Square credentials secured
- [ ] API endpoints protected
- [ ] Authentication flow tested
- [ ] Payment flow tested in sandbox mode

## 📋 Next Steps

1. **Complete Environment Setup**: Ensure all credentials are configured
2. **Test Database Connection**: Verify Supabase integration works
3. **Test Authentication**: Sign in with admin portal credentials
4. **Test Payment Flow**: Process test payment with Square
5. **Begin Development**: Start with POS interface implementation

## 🆘 Troubleshooting

### Common Issues

**Metro bundler issues:**
```bash
npx expo start --clear
```

**iOS build issues:**
```bash
cd ios && pod install && cd ..
npx expo run:ios --clean
```

**Android build issues:**
```bash
npx expo run:android --clean
```

**Environment variable issues:**
```bash
# Restart development server after changing .env.local
npx expo start --clear
```

### Getting Help

- **Expo Documentation**: https://docs.expo.dev/
- **React Native Documentation**: https://reactnative.dev/docs/getting-started
- **Supabase Documentation**: https://supabase.com/docs
- **Square Developer Documentation**: https://developer.squareup.com/

## 📞 Support

For project-specific issues, contact the development team or refer to the main project documentation.
