/**
 * Ocean Soul Sparkles Mobile App - System Performance Validation Service
 * End-to-end performance testing to ensure production performance standards
 */

import { supabase } from '@/services/database/supabase';
import { adminPortalAPIService } from '@/services/adminPortal/adminPortalAPIService';
import { distancePricingService } from '@/services/pricing/distancePricingService';
import { emailService } from '@/services/email/emailService';
import { sqlFunctionsService } from '@/services/database/sqlFunctionsService';

export interface PerformanceTestResult {
  testName: string;
  category: 'database' | 'api' | 'computation' | 'integration';
  duration: number;
  status: 'pass' | 'fail' | 'warning';
  threshold: number;
  actualValue: number;
  message: string;
  details?: any;
}

export interface PerformanceMetrics {
  responseTime: number;
  throughput: number;
  errorRate: number;
  memoryUsage?: number;
  cpuUsage?: number;
}

export interface SystemPerformanceReport {
  timestamp: string;
  overallScore: number;
  performanceGrade: 'A' | 'B' | 'C' | 'D' | 'F';
  testResults: PerformanceTestResult[];
  categoryScores: {
    database: number;
    api: number;
    computation: number;
    integration: number;
  };
  recommendations: string[];
  criticalIssues: string[];
  summary: string;
}

export class SystemPerformanceValidator {
  private static instance: SystemPerformanceValidator;

  // Performance thresholds (in milliseconds)
  private readonly PERFORMANCE_THRESHOLDS = {
    database: {
      simpleQuery: 200,
      complexQuery: 500,
      insert: 300,
      update: 300,
      delete: 200,
    },
    api: {
      healthCheck: 1000,
      authentication: 2000,
      dataRetrieval: 3000,
      dataSubmission: 5000,
    },
    computation: {
      distancePricing: 1000,
      sqlFunction: 500,
      dataProcessing: 2000,
    },
    integration: {
      emailSending: 10000,
      adminPortalSync: 5000,
      realTimeUpdate: 3000,
    },
  };

  private constructor() {}

  public static getInstance(): SystemPerformanceValidator {
    if (!SystemPerformanceValidator.instance) {
      SystemPerformanceValidator.instance = new SystemPerformanceValidator();
    }
    return SystemPerformanceValidator.instance;
  }

  /**
   * Test database performance
   */
  async testDatabasePerformance(): Promise<PerformanceTestResult[]> {
    const results: PerformanceTestResult[] = [];

    // Test simple query performance
    const simpleQueryStart = Date.now();
    try {
      const { data, error } = await supabase
        .from('customers')
        .select('id, full_name')
        .limit(10);

      const duration = Date.now() - simpleQueryStart;
      const threshold = this.PERFORMANCE_THRESHOLDS.database.simpleQuery;

      results.push({
        testName: 'Simple Database Query',
        category: 'database',
        duration,
        status: duration <= threshold ? 'pass' : duration <= threshold * 1.5 ? 'warning' : 'fail',
        threshold,
        actualValue: duration,
        message: `Query completed in ${duration}ms (threshold: ${threshold}ms)`,
        details: { recordCount: data?.length || 0, hasError: !!error },
      });

    } catch (error) {
      results.push({
        testName: 'Simple Database Query',
        category: 'database',
        duration: Date.now() - simpleQueryStart,
        status: 'fail',
        threshold: this.PERFORMANCE_THRESHOLDS.database.simpleQuery,
        actualValue: 0,
        message: 'Database query failed',
        details: { error: error instanceof Error ? error.message : 'Unknown error' },
      });
    }

    // Test complex query performance
    const complexQueryStart = Date.now();
    try {
      const { data, error } = await supabase
        .from('bookings')
        .select(`
          id,
          booking_date,
          status,
          total_amount,
          customer:customers(full_name, email),
          service:services(name, base_price)
        `)
        .limit(20);

      const duration = Date.now() - complexQueryStart;
      const threshold = this.PERFORMANCE_THRESHOLDS.database.complexQuery;

      results.push({
        testName: 'Complex Database Query',
        category: 'database',
        duration,
        status: duration <= threshold ? 'pass' : duration <= threshold * 1.5 ? 'warning' : 'fail',
        threshold,
        actualValue: duration,
        message: `Complex query completed in ${duration}ms (threshold: ${threshold}ms)`,
        details: { recordCount: data?.length || 0, hasError: !!error },
      });

    } catch (error) {
      results.push({
        testName: 'Complex Database Query',
        category: 'database',
        duration: Date.now() - complexQueryStart,
        status: 'fail',
        threshold: this.PERFORMANCE_THRESHOLDS.database.complexQuery,
        actualValue: 0,
        message: 'Complex database query failed',
        details: { error: error instanceof Error ? error.message : 'Unknown error' },
      });
    }

    // Test SQL function performance
    const sqlFunctionStart = Date.now();
    try {
      const result = await sqlFunctionsService.generateTransactionNumber();
      const duration = Date.now() - sqlFunctionStart;
      const threshold = this.PERFORMANCE_THRESHOLDS.computation.sqlFunction;

      results.push({
        testName: 'SQL Function Execution',
        category: 'database',
        duration,
        status: duration <= threshold ? 'pass' : duration <= threshold * 1.5 ? 'warning' : 'fail',
        threshold,
        actualValue: duration,
        message: `SQL function executed in ${duration}ms (threshold: ${threshold}ms)`,
        details: { success: result.success, transactionNumber: result.transaction_number },
      });

    } catch (error) {
      results.push({
        testName: 'SQL Function Execution',
        category: 'database',
        duration: Date.now() - sqlFunctionStart,
        status: 'fail',
        threshold: this.PERFORMANCE_THRESHOLDS.computation.sqlFunction,
        actualValue: 0,
        message: 'SQL function execution failed',
        details: { error: error instanceof Error ? error.message : 'Unknown error' },
      });
    }

    return results;
  }

  /**
   * Test API performance
   */
  async testAPIPerformance(): Promise<PerformanceTestResult[]> {
    const results: PerformanceTestResult[] = [];

    // Test admin portal health check
    const healthCheckStart = Date.now();
    try {
      const healthResult = await adminPortalAPIService.testHealth();
      const duration = Date.now() - healthCheckStart;
      const threshold = this.PERFORMANCE_THRESHOLDS.api.healthCheck;

      results.push({
        testName: 'Admin Portal Health Check',
        category: 'api',
        duration,
        status: duration <= threshold ? 'pass' : duration <= threshold * 1.5 ? 'warning' : 'fail',
        threshold,
        actualValue: duration,
        message: `Health check completed in ${duration}ms (threshold: ${threshold}ms)`,
        details: { success: healthResult.success, status: healthResult.data?.status },
      });

    } catch (error) {
      results.push({
        testName: 'Admin Portal Health Check',
        category: 'api',
        duration: Date.now() - healthCheckStart,
        status: 'fail',
        threshold: this.PERFORMANCE_THRESHOLDS.api.healthCheck,
        actualValue: 0,
        message: 'Admin portal health check failed',
        details: { error: error instanceof Error ? error.message : 'Unknown error' },
      });
    }

    // Test authentication performance
    const authStart = Date.now();
    try {
      const authResult = await adminPortalAPIService.validateToken();
      const duration = Date.now() - authStart;
      const threshold = this.PERFORMANCE_THRESHOLDS.api.authentication;

      results.push({
        testName: 'Authentication Validation',
        category: 'api',
        duration,
        status: duration <= threshold ? 'pass' : duration <= threshold * 1.5 ? 'warning' : 'fail',
        threshold,
        actualValue: duration,
        message: `Authentication validated in ${duration}ms (threshold: ${threshold}ms)`,
        details: { success: authResult.success },
      });

    } catch (error) {
      results.push({
        testName: 'Authentication Validation',
        category: 'api',
        duration: Date.now() - authStart,
        status: 'fail',
        threshold: this.PERFORMANCE_THRESHOLDS.api.authentication,
        actualValue: 0,
        message: 'Authentication validation failed',
        details: { error: error instanceof Error ? error.message : 'Unknown error' },
      });
    }

    return results;
  }

  /**
   * Test computation performance
   */
  async testComputationPerformance(): Promise<PerformanceTestResult[]> {
    const results: PerformanceTestResult[] = [];

    // Test distance pricing calculation
    const pricingStart = Date.now();
    try {
      const testService = {
        id: 'test-service',
        name: 'Performance Test Service',
        base_price: 100,
        description: '',
        duration_minutes: 60,
        is_active: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };

      const testLocation = {
        address: 'Sydney, NSW, Australia',
        city: 'Sydney',
        state: 'NSW',
        postal_code: '2000',
        latitude: -33.8688,
        longitude: 151.2093,
      };

      const pricingResult = await distancePricingService.calculatePricing(testService, testLocation);
      const duration = Date.now() - pricingStart;
      const threshold = this.PERFORMANCE_THRESHOLDS.computation.distancePricing;

      results.push({
        testName: 'Distance Pricing Calculation',
        category: 'computation',
        duration,
        status: duration <= threshold ? 'pass' : duration <= threshold * 1.5 ? 'warning' : 'fail',
        threshold,
        actualValue: duration,
        message: `Distance pricing calculated in ${duration}ms (threshold: ${threshold}ms)`,
        details: { 
          success: pricingResult.success, 
          distance: pricingResult.distance_km,
          totalPrice: pricingResult.total_price,
        },
      });

    } catch (error) {
      results.push({
        testName: 'Distance Pricing Calculation',
        category: 'computation',
        duration: Date.now() - pricingStart,
        status: 'fail',
        threshold: this.PERFORMANCE_THRESHOLDS.computation.distancePricing,
        actualValue: 0,
        message: 'Distance pricing calculation failed',
        details: { error: error instanceof Error ? error.message : 'Unknown error' },
      });
    }

    return results;
  }

  /**
   * Test integration performance
   */
  async testIntegrationPerformance(): Promise<PerformanceTestResult[]> {
    const results: PerformanceTestResult[] = [];

    // Test email sending performance
    const emailStart = Date.now();
    try {
      const emailResult = await emailService.sendEmail({
        to: '<EMAIL>',
        subject: 'Performance Test Email',
        template: 'test',
        variables: { testType: 'performance' },
      });

      const duration = Date.now() - emailStart;
      const threshold = this.PERFORMANCE_THRESHOLDS.integration.emailSending;

      results.push({
        testName: 'Email Sending Integration',
        category: 'integration',
        duration,
        status: duration <= threshold ? 'pass' : duration <= threshold * 1.5 ? 'warning' : 'fail',
        threshold,
        actualValue: duration,
        message: `Email sent in ${duration}ms (threshold: ${threshold}ms)`,
        details: { success: emailResult.success, emailId: emailResult.emailId },
      });

    } catch (error) {
      results.push({
        testName: 'Email Sending Integration',
        category: 'integration',
        duration: Date.now() - emailStart,
        status: 'fail',
        threshold: this.PERFORMANCE_THRESHOLDS.integration.emailSending,
        actualValue: 0,
        message: 'Email sending integration failed',
        details: { error: error instanceof Error ? error.message : 'Unknown error' },
      });
    }

    return results;
  }

  /**
   * Calculate category score
   */
  private calculateCategoryScore(results: PerformanceTestResult[]): number {
    if (results.length === 0) return 0;

    const scores = results.map(result => {
      switch (result.status) {
        case 'pass': return 100;
        case 'warning': return 75;
        case 'fail': return 0;
        default: return 0;
      }
    });

    return scores.reduce((sum, score) => sum + score, 0) / scores.length;
  }

  /**
   * Generate performance recommendations
   */
  private generateRecommendations(results: PerformanceTestResult[]): string[] {
    const recommendations: string[] = [];
    const failedTests = results.filter(r => r.status === 'fail');
    const warningTests = results.filter(r => r.status === 'warning');

    if (failedTests.length === 0 && warningTests.length === 0) {
      recommendations.push('System performance is excellent and meets all production standards');
    } else {
      if (failedTests.length > 0) {
        recommendations.push(`Address ${failedTests.length} critical performance issues before deployment`);
        
        const dbFailures = failedTests.filter(t => t.category === 'database');
        if (dbFailures.length > 0) {
          recommendations.push('Optimize database queries and consider indexing improvements');
        }

        const apiFailures = failedTests.filter(t => t.category === 'api');
        if (apiFailures.length > 0) {
          recommendations.push('Investigate API connectivity and response time issues');
        }
      }

      if (warningTests.length > 0) {
        recommendations.push(`Monitor ${warningTests.length} performance warnings for potential optimization`);
      }
    }

    return recommendations;
  }

  /**
   * Run comprehensive system performance validation
   */
  async runSystemPerformanceValidation(): Promise<SystemPerformanceReport> {
    console.log('⚡ Running comprehensive system performance validation...');

    const startTime = Date.now();

    try {
      const [
        databaseResults,
        apiResults,
        computationResults,
        integrationResults,
      ] = await Promise.all([
        this.testDatabasePerformance(),
        this.testAPIPerformance(),
        this.testComputationPerformance(),
        this.testIntegrationPerformance(),
      ]);

      const allResults = [
        ...databaseResults,
        ...apiResults,
        ...computationResults,
        ...integrationResults,
      ];

      // Calculate category scores
      const categoryScores = {
        database: this.calculateCategoryScore(databaseResults),
        api: this.calculateCategoryScore(apiResults),
        computation: this.calculateCategoryScore(computationResults),
        integration: this.calculateCategoryScore(integrationResults),
      };

      // Calculate overall score
      const overallScore = Object.values(categoryScores).reduce((sum, score) => sum + score, 0) / 4;

      // Determine performance grade
      let performanceGrade: 'A' | 'B' | 'C' | 'D' | 'F';
      if (overallScore >= 90) performanceGrade = 'A';
      else if (overallScore >= 80) performanceGrade = 'B';
      else if (overallScore >= 70) performanceGrade = 'C';
      else if (overallScore >= 60) performanceGrade = 'D';
      else performanceGrade = 'F';

      // Generate recommendations
      const recommendations = this.generateRecommendations(allResults);

      // Collect critical issues
      const criticalIssues = allResults
        .filter(r => r.status === 'fail')
        .map(r => `${r.testName}: ${r.message}`);

      // Generate summary
      const passedTests = allResults.filter(r => r.status === 'pass').length;
      const totalTests = allResults.length;
      const summary = `Performance validation completed: ${passedTests}/${totalTests} tests passed. ` +
                     `Overall score: ${overallScore.toFixed(1)}% (Grade ${performanceGrade}). ` +
                     `${criticalIssues.length} critical performance issues identified.`;

      const report: SystemPerformanceReport = {
        timestamp: new Date().toISOString(),
        overallScore: Math.round(overallScore * 100) / 100,
        performanceGrade,
        testResults: allResults,
        categoryScores,
        recommendations,
        criticalIssues,
        summary,
      };

      console.log(`✅ System performance validation completed: ${overallScore.toFixed(1)}% (Grade ${performanceGrade})`);

      return report;

    } catch (error) {
      console.error('❌ System performance validation failed:', error);
      
      return {
        timestamp: new Date().toISOString(),
        overallScore: 0,
        performanceGrade: 'F',
        testResults: [],
        categoryScores: {
          database: 0,
          api: 0,
          computation: 0,
          integration: 0,
        },
        recommendations: ['Fix performance validation system and retry'],
        criticalIssues: [error instanceof Error ? error.message : 'Performance validation failed'],
        summary: 'Performance validation system failure',
      };
    }
  }
}

// Export singleton instance
export const systemPerformanceValidator = SystemPerformanceValidator.getInstance();
