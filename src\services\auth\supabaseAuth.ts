/**
 * Ocean Soul Sparkles Mobile App - Supabase Authentication Service
 * Handles authentication using Supabase Auth instead of admin portal API
 */

import { supabase } from '@/services/database/supabase';
import { AdminUser } from '@/types/database';

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface AuthResult {
  success: boolean;
  user?: AdminUser;
  error?: string;
  requiresMFA?: boolean;
}

export class SupabaseAuthService {
  /**
   * Sign in with email and password
   */
  async signIn(credentials: LoginCredentials): Promise<AuthResult> {
    try {
      console.log('🔐 Signing in with Admin Portal API...');

      // Use the same API as your admin portal
      const response = await fetch('https://admin.oceansoulsparkles.com.au/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: credentials.email,
          password: credentials.password,
        }),
      });

      const result = await response.json();

      if (!response.ok || !result.success) {
        console.error('❌ Admin portal sign in error:', result.error || 'Login failed');
        return {
          success: false,
          error: result.error || 'Invalid credentials',
        };
      }

      if (!result.user) {
        return {
          success: false,
          error: 'No user data returned',
        };
      }

      // Establish Supabase session for RLS compatibility
      try {
        // Sign in to Supabase using the same credentials for RLS access
        const { data: supabaseAuth, error: supabaseError } = await supabase.auth.signInWithPassword({
          email: credentials.email,
          password: credentials.password,
        });

        if (supabaseError) {
          console.warn('⚠️ Supabase auth failed, using admin portal auth only:', supabaseError.message);
          // Continue with admin portal auth only - some features may be limited
        } else {
          console.log('✅ Supabase session established for RLS compatibility');
        }
      } catch (supabaseError) {
        console.warn('⚠️ Supabase auth error:', supabaseError);
        // Continue with admin portal auth only
      }

      // Convert admin portal user format to our format
      const adminUser: AdminUser = {
        id: result.user.id,
        email: result.user.email,
        first_name: result.user.firstName || 'Unknown',
        last_name: result.user.lastName || 'User',
        role: result.user.role as 'DEV' | 'Admin' | 'Artist' | 'Braider',
        is_active: result.user.isActive,
        mfa_enabled: result.user.mfaEnabled || false,
        last_activity: new Date().toISOString(),
        created_at: result.user.createdAt || new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };

      console.log('✅ Sign in successful');
      return {
        success: true,
        user: adminUser,
      };
    } catch (error) {
      console.error('❌ Sign in service error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Network error',
      };
    }
  }

  /**
   * Sign out current user from both admin portal and Supabase
   */
  async signOut(): Promise<void> {
    try {
      console.log('🔐 Signing out...');

      // Sign out from Supabase to clear RLS session
      try {
        const { error } = await supabase.auth.signOut();
        if (error) {
          console.warn('⚠️ Supabase sign out error:', error.message);
        } else {
          console.log('✅ Supabase session cleared');
        }
      } catch (supabaseError) {
        console.warn('⚠️ Supabase sign out failed:', supabaseError);
      }

      // Clear admin portal session (local only)
      console.log('✅ Admin portal session cleared');
      console.log('✅ Sign out successful');
    } catch (error) {
      console.error('❌ Sign out service error:', error);
      throw error;
    }
  }

  /**
   * Get current authenticated user
   */
  async getCurrentUser(): Promise<AdminUser | null> {
    try {
      const { data: { user }, error } = await supabase.auth.getUser();
      
      if (error || !user) {
        return null;
      }

      return await this.getAdminUserProfile(user.id);
    } catch (error) {
      console.error('❌ Get current user error:', error);
      return null;
    }
  }

  /**
   * Verify current session
   */
  async verifyToken(): Promise<boolean> {
    try {
      const { data: { session }, error } = await supabase.auth.getSession();
      
      if (error || !session) {
        return false;
      }

      // Check if session is still valid
      const now = Math.floor(Date.now() / 1000);
      return session.expires_at ? session.expires_at > now : true;
    } catch (error) {
      console.error('❌ Token verification error:', error);
      return false;
    }
  }

  /**
   * Refresh authentication token
   */
  async refreshToken(): Promise<void> {
    try {
      const { error } = await supabase.auth.refreshSession();
      
      if (error) {
        console.error('❌ Token refresh error:', error);
        throw error;
      }
    } catch (error) {
      console.error('❌ Refresh token service error:', error);
      throw error;
    }
  }

  /**
   * Get admin user profile from admin_users table
   */
  private async getAdminUserProfile(userId: string): Promise<AdminUser | null> {
    try {
      const { data, error } = await supabase
        .from('admin_users')
        .select('*')
        .eq('id', userId)
        .single();

      if (error) {
        console.error('❌ Get admin user profile error:', error);
        return null;
      }

      return data;
    } catch (error) {
      console.error('❌ Get admin user profile service error:', error);
      return null;
    }
  }

  /**
   * Initialize authentication state
   */
  async initialize(): Promise<AdminUser | null> {
    try {
      console.log('🔐 Initializing admin portal auth...');

      // For admin portal auth, we don't have persistent sessions
      // The app will require login each time
      console.log('📝 No persistent session - login required');
      return null;
    } catch (error) {
      console.error('❌ Initialize auth service error:', error);
      return null;
    }
  }

  /**
   * Role-based access control helpers
   */
  hasRole(user: AdminUser | null, allowedRoles: string[]): boolean {
    if (!user || !user.role) return false;
    return allowedRoles.includes(user.role);
  }

  isAdmin(user: AdminUser | null): boolean {
    return this.hasRole(user, ['DEV', 'Admin']);
  }

  canAccessPOS(user: AdminUser | null): boolean {
    return this.hasRole(user, ['DEV', 'Admin', 'Staff']);
  }

  /**
   * Listen to auth state changes
   */
  onAuthStateChange(callback: (user: AdminUser | null) => void) {
    return supabase.auth.onAuthStateChange(async (event, session) => {
      console.log('🔐 Auth state changed:', event);
      
      if (event === 'SIGNED_OUT' || !session?.user) {
        callback(null);
        return;
      }

      if (event === 'SIGNED_IN' || event === 'TOKEN_REFRESHED') {
        const adminUser = await this.getAdminUserProfile(session.user.id);
        callback(adminUser);
      }
    });
  }
}

// Export singleton instance
export const supabaseAuth = new SupabaseAuthService();
