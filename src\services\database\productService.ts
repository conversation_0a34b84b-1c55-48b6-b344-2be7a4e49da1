/**
 * Ocean Soul Sparkles Mobile App - Product Database Service
 * Handles product and service data operations for POS
 */

import { supabase } from './supabase';
import { Product, Service, DatabaseListResponse, QueryFilters } from '@/types/database';

// Use the shared Supabase client instead of creating a new one

export class ProductService {
  
  /**
   * Get all active products with optional filtering
   */
  async getProducts(filters?: QueryFilters): Promise<DatabaseListResponse<Product>> {
    try {
      console.log('🔍 Querying products table...');
      let query = supabase
        .from('products')
        .select('*');

      // Only filter by is_active if the column exists
      // For now, we'll assume all products are active if the column doesn't exist

      // Apply search filter with performance optimization
      if (filters?.search) {
        const searchTerm = filters.search.trim();
        if (searchTerm.length >= 2) { // Minimum search length for performance
          query = query.or(`name.ilike.%${searchTerm}%,description.ilike.%${searchTerm}%`);
        }
      }

      // Apply category filter (indexed field, very efficient)
      if (filters?.filters?.category) {
        query = query.eq('category', filters.filters.category);
      }

      // Apply ordering with performance consideration
      const orderBy = filters?.order_by || 'name';
      const orderDirection = filters?.order_direction || 'asc';
      query = query.order(orderBy, { ascending: orderDirection === 'asc' });

      // Apply pagination with performance-conscious defaults
      const limit = filters?.limit || 50; // Default limit for products
      const offset = filters?.offset || 0;

      query = query.limit(limit);
      if (offset > 0) {
        query = query.range(offset, offset + limit - 1);
      }

      const { data, error, count } = await query;

      if (error) {
        console.error('Error fetching products:', error);
        return { data: null, error, count: 0 };
      }

      return { data: data || [], error: null, count: count || data?.length || 0 };
    } catch (error) {
      console.error('Product service error:', error);
      return { data: null, error: error as Error, count: 0 };
    }
  }

  /**
   * Get all active services with optional filtering
   */
  async getServices(filters?: QueryFilters): Promise<DatabaseListResponse<Service>> {
    try {
      console.log('🔍 Querying services table...');
      let query = supabase
        .from('services')
        .select('*');

      // Only filter by is_active if the column exists
      // For now, we'll assume all services are active if the column doesn't exist

      // Apply search filter with performance optimization
      if (filters?.search) {
        const searchTerm = filters.search.trim();
        if (searchTerm.length >= 2) { // Minimum search length for performance
          query = query.or(`name.ilike.%${searchTerm}%,description.ilike.%${searchTerm}%`);
        }
      }

      // Apply category filter (indexed field, very efficient)
      if (filters?.filters?.category) {
        query = query.eq('category', filters.filters.category);
      }

      // Apply ordering with performance consideration
      const orderBy = filters?.order_by || 'name';
      const orderDirection = filters?.order_direction || 'asc';
      query = query.order(orderBy, { ascending: orderDirection === 'asc' });

      // Apply pagination with performance-conscious defaults
      const limit = filters?.limit || 50; // Default limit for services
      const offset = filters?.offset || 0;

      query = query.limit(limit);
      if (offset > 0) {
        query = query.range(offset, offset + limit - 1);
      }

      const { data, error, count } = await query;

      if (error) {
        console.error('Error fetching services:', error);
        return { data: null, error, count: 0 };
      }

      return { data: data || [], error: null, count: count || data?.length || 0 };
    } catch (error) {
      console.error('Service service error:', error);
      return { data: null, error: error as Error, count: 0 };
    }
  }

  /**
   * Get unique categories for products
   */
  async getProductCategories(): Promise<DatabaseListResponse<string>> {
    try {
      const { data, error } = await supabase
        .from('products')
        .select('category')
        .not('category', 'is', null);

      if (error) {
        console.error('Error fetching product categories:', error);
        return { data: null, error };
      }

      // Extract unique categories
      const categories = [...new Set(data?.map(item => item.category).filter(Boolean))] as string[];
      
      return { data: categories, error: null, count: categories.length };
    } catch (error) {
      console.error('Product categories error:', error);
      return { data: null, error: error as Error };
    }
  }

  /**
   * Get unique categories for services
   */
  async getServiceCategories(): Promise<DatabaseListResponse<string>> {
    try {
      const { data, error } = await supabase
        .from('services')
        .select('category')
        .not('category', 'is', null);

      if (error) {
        console.error('Error fetching service categories:', error);
        return { data: null, error };
      }

      // Extract unique categories
      const categories = [...new Set(data?.map(item => item.category).filter(Boolean))] as string[];
      
      return { data: categories, error: null, count: categories.length };
    } catch (error) {
      console.error('Service categories error:', error);
      return { data: null, error: error as Error };
    }
  }

  /**
   * Get a single product by ID
   */
  async getProductById(id: string): Promise<{ data: Product | null; error: Error | null }> {
    try {
      const { data, error } = await supabase
        .from('products')
        .select('*')
        .eq('id', id)
        .single();

      if (error) {
        console.error('Error fetching product:', error);
        return { data: null, error };
      }

      return { data, error: null };
    } catch (error) {
      console.error('Product by ID error:', error);
      return { data: null, error: error as Error };
    }
  }

  /**
   * Get a single service by ID
   */
  async getServiceById(id: string): Promise<{ data: Service | null; error: Error | null }> {
    try {
      const { data, error } = await supabase
        .from('services')
        .select('*')
        .eq('id', id)
        .single();

      if (error) {
        console.error('Error fetching service:', error);
        return { data: null, error };
      }

      return { data, error: null };
    } catch (error) {
      console.error('Service by ID error:', error);
      return { data: null, error: error as Error };
    }
  }

  /**
   * Search products and services combined
   */
  async searchItems(searchTerm: string, limit: number = 20): Promise<{
    products: Product[];
    services: Service[];
    error: Error | null;
  }> {
    try {
      const [productsResult, servicesResult] = await Promise.all([
        this.getProducts({ search: searchTerm, limit }),
        this.getServices({ search: searchTerm, limit })
      ]);

      return {
        products: productsResult.data || [],
        services: servicesResult.data || [],
        error: productsResult.error || servicesResult.error
      };
    } catch (error) {
      console.error('Search items error:', error);
      return {
        products: [],
        services: [],
        error: error as Error
      };
    }
  }
}

// Export singleton instance
export const productService = new ProductService();
