/**
 * Ocean Soul Sparkles Mobile App - Database Integration Validation System
 * Comprehensive validation of database integration, SQL functions, and data integrity
 */

import { sqlFunctionsService } from '@/services/database/sqlFunctionsService';
import { databasePerformanceOptimizer, DatabaseHealthReport } from '@/services/database/databasePerformanceOptimizer';
import { invoiceWorkflowService } from '@/services/invoice/invoiceWorkflowService';
import { acknowledgmentWorkflowService } from '@/services/acknowledgment/acknowledgmentWorkflowService';
import { supabase } from '@/services/database/supabase';

export interface DatabaseValidationResult {
  test: string;
  status: 'pass' | 'fail' | 'warning';
  message: string;
  duration: number;
  details?: any;
  error?: string;
}

export interface DatabaseIntegrationStatus {
  connectionHealth: boolean;
  sqlFunctionsWorking: boolean;
  performanceOptimal: boolean;
  dataIntegrityValid: boolean;
  workflowsOperational: boolean;
  overall: boolean;
}

export interface DatabaseValidationSuite {
  connectionTests: DatabaseValidationResult[];
  functionTests: DatabaseValidationResult[];
  performanceTests: DatabaseValidationResult[];
  integrityTests: DatabaseValidationResult[];
  workflowTests: DatabaseValidationResult[];
  summary: {
    totalTests: number;
    passedTests: number;
    failedTests: number;
    warningTests: number;
    successRate: number;
    totalDuration: number;
  };
}

export class DatabaseIntegrationValidator {
  private static instance: DatabaseIntegrationValidator;

  // Performance thresholds
  private readonly PERFORMANCE_THRESHOLDS = {
    queryTime: 1000,        // 1 second
    functionTime: 2000,     // 2 seconds
    connectionTime: 500,    // 500ms
    healthScore: 80,        // 80%
  };

  private constructor() {}

  public static getInstance(): DatabaseIntegrationValidator {
    if (!DatabaseIntegrationValidator.instance) {
      DatabaseIntegrationValidator.instance = new DatabaseIntegrationValidator();
    }
    return DatabaseIntegrationValidator.instance;
  }

  /**
   * Test database connection health
   */
  async testDatabaseConnection(): Promise<DatabaseValidationResult> {
    const startTime = Date.now();
    
    try {
      console.log('🔗 Testing database connection...');

      // Test basic connection
      const { data, error } = await supabase
        .from('customers')
        .select('count')
        .limit(1);

      if (error) {
        return {
          test: 'Database Connection',
          status: 'fail',
          message: 'Database connection failed',
          duration: Date.now() - startTime,
          error: error.message,
        };
      }

      const connectionTime = Date.now() - startTime;
      const status = connectionTime <= this.PERFORMANCE_THRESHOLDS.connectionTime ? 'pass' : 'warning';
      
      return {
        test: 'Database Connection',
        status,
        message: `Database connection ${status === 'pass' ? 'healthy' : 'slow'} (${connectionTime}ms)`,
        duration: connectionTime,
        details: { connectionTime, threshold: this.PERFORMANCE_THRESHOLDS.connectionTime },
      };

    } catch (error) {
      return {
        test: 'Database Connection',
        status: 'fail',
        message: 'Database connection test failed',
        duration: Date.now() - startTime,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Test SQL functions availability and performance
   */
  async testSQLFunctions(): Promise<DatabaseValidationResult[]> {
    const results: DatabaseValidationResult[] = [];

    // Test function availability
    const startTime = Date.now();
    try {
      console.log('⚙️ Testing SQL functions availability...');

      const availableFunctions = await sqlFunctionsService.getAvailableFunctions();
      
      results.push({
        test: 'SQL Functions Availability',
        status: availableFunctions.length > 0 ? 'pass' : 'fail',
        message: `${availableFunctions.length} SQL functions available`,
        duration: Date.now() - startTime,
        details: { availableFunctions },
      });

    } catch (error) {
      results.push({
        test: 'SQL Functions Availability',
        status: 'fail',
        message: 'Failed to check SQL functions',
        duration: Date.now() - startTime,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }

    // Test specific functions
    const functionTests = [
      {
        name: 'Transaction Number Generation',
        test: () => sqlFunctionsService.generateTransactionNumber(),
      },
      {
        name: 'Invoice Number Generation',
        test: () => sqlFunctionsService.generateInvoiceNumber(),
      },
    ];

    for (const functionTest of functionTests) {
      const testStartTime = Date.now();
      try {
        const result = await functionTest.test();
        const duration = Date.now() - testStartTime;
        
        const status = result.success ? 
          (duration <= this.PERFORMANCE_THRESHOLDS.functionTime ? 'pass' : 'warning') : 'fail';

        results.push({
          test: functionTest.name,
          status,
          message: result.success ? 
            `Function working (${duration}ms)` : 
            result.error || 'Function failed',
          duration,
          details: result.success ? { result: result.data } : undefined,
          error: result.success ? undefined : result.error,
        });

      } catch (error) {
        results.push({
          test: functionTest.name,
          status: 'fail',
          message: 'Function test failed',
          duration: Date.now() - testStartTime,
          error: error instanceof Error ? error.message : 'Unknown error',
        });
      }
    }

    return results;
  }

  /**
   * Test database performance
   */
  async testDatabasePerformance(): Promise<DatabaseValidationResult[]> {
    const results: DatabaseValidationResult[] = [];

    // Test performance metrics
    const startTime = Date.now();
    try {
      console.log('📊 Testing database performance...');

      const metrics = await databasePerformanceOptimizer.measurePerformanceMetrics();
      
      // Query performance test
      const queryStatus = metrics.queryTime <= this.PERFORMANCE_THRESHOLDS.queryTime ? 'pass' : 
                         metrics.queryTime <= this.PERFORMANCE_THRESHOLDS.queryTime * 2 ? 'warning' : 'fail';

      results.push({
        test: 'Query Performance',
        status: queryStatus,
        message: `Average query time: ${metrics.queryTime}ms`,
        duration: Date.now() - startTime,
        details: { 
          queryTime: metrics.queryTime, 
          threshold: this.PERFORMANCE_THRESHOLDS.queryTime 
        },
      });

      // Overall health score test
      const healthStatus = metrics.overallScore >= this.PERFORMANCE_THRESHOLDS.healthScore ? 'pass' : 
                          metrics.overallScore >= this.PERFORMANCE_THRESHOLDS.healthScore * 0.8 ? 'warning' : 'fail';

      results.push({
        test: 'Database Health Score',
        status: healthStatus,
        message: `Health score: ${metrics.overallScore}%`,
        duration: 0,
        details: { 
          healthScore: metrics.overallScore, 
          threshold: this.PERFORMANCE_THRESHOLDS.healthScore 
        },
      });

      // Index efficiency test
      const indexStatus = metrics.indexEfficiency >= 80 ? 'pass' : 
                         metrics.indexEfficiency >= 60 ? 'warning' : 'fail';

      results.push({
        test: 'Index Efficiency',
        status: indexStatus,
        message: `Index efficiency: ${metrics.indexEfficiency}%`,
        duration: 0,
        details: { indexEfficiency: metrics.indexEfficiency },
      });

    } catch (error) {
      results.push({
        test: 'Database Performance',
        status: 'fail',
        message: 'Performance testing failed',
        duration: Date.now() - startTime,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }

    return results;
  }

  /**
   * Test data integrity
   */
  async testDataIntegrity(): Promise<DatabaseValidationResult[]> {
    const results: DatabaseValidationResult[] = [];

    // Test table existence
    const startTime = Date.now();
    try {
      console.log('🔍 Testing data integrity...');

      const requiredTables = ['customers', 'bookings', 'quotes', 'services', 'staff'];
      
      for (const table of requiredTables) {
        const tableStartTime = Date.now();
        try {
          const { data, error } = await supabase
            .from(table)
            .select('count')
            .limit(1);

          results.push({
            test: `Table: ${table}`,
            status: error ? 'fail' : 'pass',
            message: error ? `Table ${table} not accessible` : `Table ${table} accessible`,
            duration: Date.now() - tableStartTime,
            error: error?.message,
          });

        } catch (error) {
          results.push({
            test: `Table: ${table}`,
            status: 'fail',
            message: `Table ${table} test failed`,
            duration: Date.now() - tableStartTime,
            error: error instanceof Error ? error.message : 'Unknown error',
          });
        }
      }

      // Test foreign key constraints (simplified)
      const constraintStartTime = Date.now();
      try {
        const { data, error } = await supabase
          .from('bookings')
          .select('customer_id, service_id')
          .not('customer_id', 'is', null)
          .not('service_id', 'is', null)
          .limit(5);

        results.push({
          test: 'Foreign Key Constraints',
          status: error ? 'warning' : 'pass',
          message: error ? 'Some constraint issues detected' : 'Foreign key constraints working',
          duration: Date.now() - constraintStartTime,
          details: { sampleRecords: data?.length || 0 },
        });

      } catch (error) {
        results.push({
          test: 'Foreign Key Constraints',
          status: 'warning',
          message: 'Constraint testing incomplete',
          duration: Date.now() - constraintStartTime,
          error: error instanceof Error ? error.message : 'Unknown error',
        });
      }

    } catch (error) {
      results.push({
        test: 'Data Integrity',
        status: 'fail',
        message: 'Data integrity testing failed',
        duration: Date.now() - startTime,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }

    return results;
  }

  /**
   * Test workflow integrations
   */
  async testWorkflowIntegrations(): Promise<DatabaseValidationResult[]> {
    const results: DatabaseValidationResult[] = [];

    // Test invoice workflow (simulation)
    const invoiceStartTime = Date.now();
    try {
      console.log('🧾 Testing invoice workflow...');

      // Test invoice number generation (safe operation)
      const invoiceNumberResult = await sqlFunctionsService.generateInvoiceNumber();
      
      results.push({
        test: 'Invoice Workflow',
        status: invoiceNumberResult.success ? 'pass' : 'fail',
        message: invoiceNumberResult.success ? 
          'Invoice workflow functional' : 
          'Invoice workflow failed',
        duration: Date.now() - invoiceStartTime,
        details: invoiceNumberResult.success ? { 
          invoiceNumber: invoiceNumberResult.data 
        } : undefined,
        error: invoiceNumberResult.success ? undefined : invoiceNumberResult.error,
      });

    } catch (error) {
      results.push({
        test: 'Invoice Workflow',
        status: 'fail',
        message: 'Invoice workflow test failed',
        duration: Date.now() - invoiceStartTime,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }

    // Test acknowledgment workflow
    const ackStartTime = Date.now();
    try {
      console.log('📧 Testing acknowledgment workflow...');

      // Test getting pending acknowledgments (safe operation)
      const pendingResult = await acknowledgmentWorkflowService.getPendingAcknowledgments();
      
      results.push({
        test: 'Acknowledgment Workflow',
        status: pendingResult.success ? 'pass' : 'warning',
        message: pendingResult.success ? 
          `Acknowledgment workflow functional (${pendingResult.acknowledgments?.length || 0} pending)` : 
          'Acknowledgment workflow issues detected',
        duration: Date.now() - ackStartTime,
        details: pendingResult.success ? { 
          pendingCount: pendingResult.acknowledgments?.length || 0 
        } : undefined,
        error: pendingResult.success ? undefined : pendingResult.error,
      });

    } catch (error) {
      results.push({
        test: 'Acknowledgment Workflow',
        status: 'fail',
        message: 'Acknowledgment workflow test failed',
        duration: Date.now() - ackStartTime,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }

    return results;
  }

  /**
   * Get database integration status
   */
  async getDatabaseIntegrationStatus(): Promise<DatabaseIntegrationStatus> {
    try {
      const [
        connectionResult,
        functionResults,
        performanceResults,
        integrityResults,
        workflowResults,
      ] = await Promise.all([
        this.testDatabaseConnection(),
        this.testSQLFunctions(),
        this.testDatabasePerformance(),
        this.testDataIntegrity(),
        this.testWorkflowIntegrations(),
      ]);

      const status = {
        connectionHealth: connectionResult.status === 'pass',
        sqlFunctionsWorking: functionResults.every(r => r.status === 'pass'),
        performanceOptimal: performanceResults.every(r => r.status === 'pass'),
        dataIntegrityValid: integrityResults.filter(r => r.status === 'fail').length === 0,
        workflowsOperational: workflowResults.every(r => r.status !== 'fail'),
        overall: false,
      };

      // Overall status is true if critical components are working
      status.overall = status.connectionHealth && 
                      status.sqlFunctionsWorking && 
                      status.dataIntegrityValid;

      return status;

    } catch (error) {
      console.error('❌ Failed to get database integration status:', error);
      return {
        connectionHealth: false,
        sqlFunctionsWorking: false,
        performanceOptimal: false,
        dataIntegrityValid: false,
        workflowsOperational: false,
        overall: false,
      };
    }
  }

  /**
   * Run comprehensive database integration validation
   */
  async runCompleteDatabaseValidation(): Promise<DatabaseValidationSuite> {
    console.log('🧪 Running comprehensive database integration validation...');

    const startTime = Date.now();

    const connectionTests = [await this.testDatabaseConnection()];
    const functionTests = await this.testSQLFunctions();
    const performanceTests = await this.testDatabasePerformance();
    const integrityTests = await this.testDataIntegrity();
    const workflowTests = await this.testWorkflowIntegrations();

    const allTests = [
      ...connectionTests,
      ...functionTests,
      ...performanceTests,
      ...integrityTests,
      ...workflowTests,
    ];

    const passedTests = allTests.filter(t => t.status === 'pass').length;
    const failedTests = allTests.filter(t => t.status === 'fail').length;
    const warningTests = allTests.filter(t => t.status === 'warning').length;
    const successRate = allTests.length > 0 ? (passedTests / allTests.length) * 100 : 0;
    const totalDuration = Date.now() - startTime;

    const summary = {
      totalTests: allTests.length,
      passedTests,
      failedTests,
      warningTests,
      successRate: Math.round(successRate * 100) / 100,
      totalDuration,
    };

    console.log(`✅ Database validation completed: ${passedTests}/${allTests.length} tests passed (${summary.successRate}%)`);

    return {
      connectionTests,
      functionTests,
      performanceTests,
      integrityTests,
      workflowTests,
      summary,
    };
  }
}

// Export singleton instance
export const databaseIntegrationValidator = DatabaseIntegrationValidator.getInstance();
