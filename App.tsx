/**
 * Ocean Soul Sparkles Mobile App - Main Entry Point
 */

import React, { useEffect, useState } from 'react';
import { StatusBar } from 'expo-status-bar';
import { StyleSheet, Text, View, SafeAreaView, ActivityIndicator } from 'react-native';
import Constants from 'expo-constants';
import { NavigationContainer } from '@react-navigation/native';
import { useAuth } from '@/store/authStore.minimal';
import LoginScreen from '@/screens/auth/LoginScreen';
import RoleBasedNavigation from '@/components/navigation/RoleBasedNavigation';

// Main app with authentication

// Environment status check
function getEnvironmentStatus() {
  const supabaseUrl = Constants.expoConfig?.extra?.EXPO_PUBLIC_SUPABASE_URL || process.env.EXPO_PUBLIC_SUPABASE_URL;
  const squareAppId = Constants.expoConfig?.extra?.EXPO_PUBLIC_SQUARE_APPLICATION_ID || process.env.EXPO_PUBLIC_SQUARE_APPLICATION_ID;

  return {
    supabaseConfigured: !!supabaseUrl,
    squareConfigured: !!squareAppId,
    environment: process.env.EXPO_PUBLIC_ENVIRONMENT || 'development'
  };
}

// Temporary screens for initial setup
function WelcomeScreen() {
  const envStatus = getEnvironmentStatus();

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.content}>
        <Text style={styles.title}>🌊 Ocean Soul Sparkles</Text>
        <Text style={styles.subtitle}>Mobile App</Text>
        <Text style={styles.description}>
          Point of Sale & Business Management
        </Text>
        <View style={styles.statusContainer}>
          <Text style={styles.statusText}>✅ App Structure: Ready</Text>
          <Text style={styles.statusText}>
            {envStatus.supabaseConfigured ? '✅' : '⚠️'} Database: {envStatus.supabaseConfigured ? 'Configured' : 'Needs Setup'}
          </Text>
          <Text style={styles.statusText}>
            {envStatus.squareConfigured ? '✅' : '⚠️'} Payments: {envStatus.squareConfigured ? 'Configured' : 'Needs Setup'}
          </Text>
          <Text style={styles.statusText}>🚧 UI: In Development</Text>
          <Text style={styles.statusText}>🌍 Environment: {envStatus.environment}</Text>
        </View>
      </View>
      <StatusBar style="auto" />
    </SafeAreaView>
  );
}

function LoadingScreen() {
  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.content}>
        <ActivityIndicator size="large" color="#FF9A8B" />
        <Text style={styles.loadingText}>Initializing Ocean Soul Sparkles...</Text>
      </View>
      <StatusBar style="auto" />
    </SafeAreaView>
  );
}

// Main App Component - Minimal
function AppContent() {
  const { user, isLoading, initialize } = useAuth();
  const [isInitializing, setIsInitializing] = useState(true);

  useEffect(() => {
    const initAuth = async () => {
      try {
        await initialize();
      } catch (error) {
        console.error('Auth initialization error:', error);
      } finally {
        setIsInitializing(false);
      }
    };

    initAuth();
  }, [initialize]);

  if (isInitializing || isLoading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.content}>
          <ActivityIndicator size="large" color="#FF9A8B" />
          <Text style={styles.loadingText}>Loading Ocean Soul Sparkles...</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (!user) {
    return <LoginScreen />;
  }

  return <RoleBasedNavigation />;
}

// Root App Component - With Navigation
export default function App() {
  return (
    <NavigationContainer>
      <StatusBar style="auto" />
      <AppContent />
    </NavigationContainer>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  content: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
  },
  title: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#FF9A8B',
    marginBottom: 8,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 24,
    fontWeight: '600',
    color: '#333',
    marginBottom: 16,
    textAlign: 'center',
  },
  description: {
    fontSize: 16,
    color: '#666',
    marginBottom: 40,
    textAlign: 'center',
  },
  statusContainer: {
    alignItems: 'flex-start',
    backgroundColor: '#f8f9fa',
    padding: 20,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#e9ecef',
  },
  statusText: {
    fontSize: 14,
    marginBottom: 8,
    fontFamily: 'monospace',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
  },
});
