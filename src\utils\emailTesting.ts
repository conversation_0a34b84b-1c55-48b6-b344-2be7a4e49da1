/**
 * Ocean Soul Sparkles Mobile App - Email Testing Utilities
 * Utilities for testing email functionality and integration
 */

import { emailService } from '@/services/email/emailService';
import { quoteEmailService } from '@/services/email/quoteEmailService';
import { emailConfigService } from '@/services/email/emailConfig';
import { Quote, Customer, Booking, Service, AdminUser } from '@/types/database';

export interface EmailTestResult {
  test: string;
  success: boolean;
  message: string;
  details?: any;
}

export interface EmailSystemStatus {
  emailService: boolean;
  authentication: boolean;
  templates: boolean;
  adminPortalConnection: boolean;
  overall: boolean;
}

export class EmailTestingService {
  /**
   * Test email service connectivity and configuration
   */
  static async testEmailServiceConnectivity(): Promise<EmailTestResult[]> {
    const results: EmailTestResult[] = [];

    // Test 1: Email service initialization
    try {
      const status = emailConfigService.getStatus();
      results.push({
        test: 'Email Service Initialization',
        success: status.initialized,
        message: status.initialized ? 'Email service initialized successfully' : 'Email service not initialized',
        details: status,
      });
    } catch (error) {
      results.push({
        test: 'Email Service Initialization',
        success: false,
        message: 'Failed to check email service status',
        details: error,
      });
    }

    // Test 2: Authentication status
    try {
      const status = emailConfigService.getStatus();
      results.push({
        test: 'Email Service Authentication',
        success: status.authenticated,
        message: status.authenticated ? 'Email service authenticated' : 'Email service not authenticated',
        details: { authenticated: status.authenticated },
      });
    } catch (error) {
      results.push({
        test: 'Email Service Authentication',
        success: false,
        message: 'Failed to check authentication status',
        details: error,
      });
    }

    // Test 3: Template loading
    try {
      const templates = await emailService.getEmailTemplates();
      const hasQuoteTemplate = templates.some(t => t.template_type === 'quote');
      const hasBookingTemplate = templates.some(t => t.template_type === 'booking_confirmation');
      
      results.push({
        test: 'Email Template Loading',
        success: templates.length > 0,
        message: `Loaded ${templates.length} email templates`,
        details: {
          totalTemplates: templates.length,
          hasQuoteTemplate,
          hasBookingTemplate,
          templateTypes: templates.map(t => t.template_type),
        },
      });
    } catch (error) {
      results.push({
        test: 'Email Template Loading',
        success: false,
        message: 'Failed to load email templates',
        details: error,
      });
    }

    // Test 4: Admin portal connectivity
    try {
      const connectivityTest = await emailConfigService.testEmailService();
      results.push({
        test: 'Admin Portal Connectivity',
        success: connectivityTest,
        message: connectivityTest ? 'Admin portal connection successful' : 'Admin portal connection failed',
        details: { connected: connectivityTest },
      });
    } catch (error) {
      results.push({
        test: 'Admin Portal Connectivity',
        success: false,
        message: 'Failed to test admin portal connectivity',
        details: error,
      });
    }

    return results;
  }

  /**
   * Test quote email functionality with mock data
   */
  static async testQuoteEmailFunctionality(): Promise<EmailTestResult[]> {
    const results: EmailTestResult[] = [];

    // Create mock quote data
    const mockQuote: Quote = {
      id: 'test-quote-' + Date.now(),
      customer_id: 'test-customer',
      customer_first_name: 'John',
      customer_last_name: 'Doe',
      customer_email: '<EMAIL>',
      customer_phone: '+61123456789',
      service_name: 'Test Quote for Email Validation',
      service_description: 'This is a test quote created for email system validation',
      estimated_total: 150.00,
      status: 'draft',
      expires_at: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
      notes: 'Test quote for email functionality',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };

    // Test 1: Quote email data loading
    try {
      // This would normally load from database, but we'll simulate it
      results.push({
        test: 'Quote Email Data Preparation',
        success: true,
        message: 'Quote email data prepared successfully',
        details: {
          quoteId: mockQuote.id,
          hasCustomer: !!mockQuote.customer_id,
          hasAmount: !!mockQuote.estimated_total,
          hasValidUntil: !!mockQuote.valid_until,
        },
      });
    } catch (error) {
      results.push({
        test: 'Quote Email Data Preparation',
        success: false,
        message: 'Failed to prepare quote email data',
        details: error,
      });
    }

    // Test 2: Template processing
    try {
      const template = await emailService.getEmailTemplate('quote');
      if (template) {
        results.push({
          test: 'Quote Template Processing',
          success: true,
          message: 'Quote email template loaded and processed',
          details: {
            templateId: template.id,
            templateName: template.name,
            hasSubject: !!template.subject,
            hasHtmlContent: !!template.html_content,
            variableCount: template.variables.length,
          },
        });
      } else {
        results.push({
          test: 'Quote Template Processing',
          success: false,
          message: 'Quote email template not found',
          details: { template: null },
        });
      }
    } catch (error) {
      results.push({
        test: 'Quote Template Processing',
        success: false,
        message: 'Failed to process quote email template',
        details: error,
      });
    }

    // Test 3: Email sending simulation (without actually sending)
    try {
      // Simulate email sending process without actually sending
      const emailData = {
        to: '<EMAIL>',
        customerName: 'Test Customer',
        quoteData: {
          quote_number: 'OSS-Q20241127-TEST01',
          quote_title: mockQuote.title,
          quote_description: mockQuote.description || '',
          estimated_total: mockQuote.estimated_total || 0,
          valid_until: mockQuote.valid_until || '',
          service_name: 'Test Service',
          booking_date: new Date().toISOString().split('T')[0],
          staff_name: 'Test Staff Member',
        },
      };

      results.push({
        test: 'Email Sending Simulation',
        success: true,
        message: 'Email sending process simulated successfully',
        details: {
          recipientEmail: emailData.to,
          customerName: emailData.customerName,
          quoteNumber: emailData.quoteData.quote_number,
          amount: emailData.quoteData.estimated_total,
        },
      });
    } catch (error) {
      results.push({
        test: 'Email Sending Simulation',
        success: false,
        message: 'Failed to simulate email sending process',
        details: error,
      });
    }

    return results;
  }

  /**
   * Get overall email system status
   */
  static async getEmailSystemStatus(): Promise<EmailSystemStatus> {
    try {
      const connectivityResults = await this.testEmailServiceConnectivity();
      
      const emailService = connectivityResults.find(r => r.test === 'Email Service Initialization')?.success || false;
      const authentication = connectivityResults.find(r => r.test === 'Email Service Authentication')?.success || false;
      const templates = connectivityResults.find(r => r.test === 'Email Template Loading')?.success || false;
      const adminPortalConnection = connectivityResults.find(r => r.test === 'Admin Portal Connectivity')?.success || false;

      const overall = emailService && authentication && templates;

      return {
        emailService,
        authentication,
        templates,
        adminPortalConnection,
        overall,
      };
    } catch (error) {
      console.error('❌ Failed to get email system status:', error);
      return {
        emailService: false,
        authentication: false,
        templates: false,
        adminPortalConnection: false,
        overall: false,
      };
    }
  }

  /**
   * Run comprehensive email system test
   */
  static async runComprehensiveEmailTest(): Promise<{
    connectivity: EmailTestResult[];
    functionality: EmailTestResult[];
    status: EmailSystemStatus;
    summary: {
      totalTests: number;
      passedTests: number;
      failedTests: number;
      successRate: number;
    };
  }> {
    console.log('🧪 Running comprehensive email system test...');

    const connectivity = await this.testEmailServiceConnectivity();
    const functionality = await this.testQuoteEmailFunctionality();
    const status = await this.getEmailSystemStatus();

    const allTests = [...connectivity, ...functionality];
    const passedTests = allTests.filter(t => t.success).length;
    const failedTests = allTests.length - passedTests;
    const successRate = allTests.length > 0 ? (passedTests / allTests.length) * 100 : 0;

    const summary = {
      totalTests: allTests.length,
      passedTests,
      failedTests,
      successRate: Math.round(successRate * 100) / 100,
    };

    console.log(`✅ Email system test completed: ${passedTests}/${allTests.length} tests passed (${summary.successRate}%)`);

    return {
      connectivity,
      functionality,
      status,
      summary,
    };
  }
}

// Export for easy access
export const emailTesting = EmailTestingService;
