/**
 * Ocean Soul Sparkles Mobile App - Platform Compatibility Tester
 * Tests cross-platform compatibility and platform-specific features
 */

import { Platform } from 'react-native';
import { expoSDKCompatibilityValidator } from './expoSDKCompatibilityValidator';
import { expoVers<PERSON><PERSON><PERSON>cker } from './expoVersionChecker';
import { androidNotificationTester } from './androidNotificationTester';
import { iOSNotificationTester } from './iOSNotificationTester';

interface PlatformTestResult {
  testName: string;
  platform: 'ios' | 'android' | 'both';
  success: boolean;
  message: string;
  details?: any;
  severity: 'info' | 'warning' | 'error';
}

interface CompatibilityReport {
  platform: string;
  overallCompatibility: boolean;
  testResults: PlatformTestResult[];
  summary: {
    passed: number;
    failed: number;
    warnings: number;
    errors: number;
  };
  recommendations: string[];
}

class PlatformCompatibilityTester {
  
  /**
   * Run comprehensive platform compatibility tests
   */
  async runFullCompatibilityTest(): Promise<CompatibilityReport> {
    console.log('🧪 Starting comprehensive platform compatibility tests...');
    
    const testResults: PlatformTestResult[] = [];
    
    // Test 1: Expo SDK Compatibility
    testResults.push(...await this.testExpoSDKCompatibility());
    
    // Test 2: Platform-specific features
    testResults.push(...await this.testPlatformSpecificFeatures());
    
    // Test 3: Notification system compatibility
    testResults.push(...await this.testNotificationCompatibility());
    
    // Test 4: Permission compatibility
    testResults.push(...await this.testPermissionCompatibility());
    
    // Test 5: Configuration validation
    testResults.push(...await this.testConfigurationCompatibility());

    const summary = this.generateSummary(testResults);
    const recommendations = this.generateRecommendations(testResults);
    
    return {
      platform: Platform.OS,
      overallCompatibility: summary.errors === 0,
      testResults,
      summary,
      recommendations,
    };
  }

  /**
   * Test Expo SDK compatibility
   */
  private async testExpoSDKCompatibility(): Promise<PlatformTestResult[]> {
    const results: PlatformTestResult[] = [];
    
    try {
      // Test Expo SDK version
      const versionReport = expoVersionChecker.getCompatibilityReport();
      
      results.push({
        testName: 'Expo SDK Version',
        platform: 'both',
        success: versionReport.summary.isCompatible,
        message: versionReport.summary.isCompatible 
          ? `SDK ${versionReport.summary.currentSDK} is compatible`
          : `SDK ${versionReport.summary.currentSDK} is incompatible`,
        details: versionReport,
        severity: versionReport.summary.isCompatible ? 'info' : 'error',
      });

      // Test Expo modules
      const sdkResults = await expoSDKCompatibilityValidator.runAllCompatibilityTests();
      
      sdkResults.forEach(result => {
        results.push({
          testName: `Expo ${result.testName}`,
          platform: result.platform as any || 'both',
          success: result.success,
          message: result.message,
          details: result.details,
          severity: result.success ? 'info' : 'warning',
        });
      });

    } catch (error) {
      results.push({
        testName: 'Expo SDK Compatibility',
        platform: 'both',
        success: false,
        message: `SDK compatibility test failed: ${error}`,
        severity: 'error',
      });
    }
    
    return results;
  }

  /**
   * Test platform-specific features
   */
  private async testPlatformSpecificFeatures(): Promise<PlatformTestResult[]> {
    const results: PlatformTestResult[] = [];
    
    // Test platform detection
    results.push({
      testName: 'Platform Detection',
      platform: Platform.OS as any,
      success: true,
      message: `Running on ${Platform.OS} platform`,
      details: { 
        platform: Platform.OS, 
        version: Platform.Version,
        constants: Platform.constants,
      },
      severity: 'info',
    });

    // Test platform-specific APIs
    if (Platform.OS === 'ios') {
      results.push(...await this.testIOSSpecificFeatures());
    } else if (Platform.OS === 'android') {
      results.push(...await this.testAndroidSpecificFeatures());
    }

    return results;
  }

  /**
   * Test iOS-specific features
   */
  private async testIOSSpecificFeatures(): Promise<PlatformTestResult[]> {
    const results: PlatformTestResult[] = [];
    
    try {
      // Test iOS version compatibility
      const iosVersion = Platform.Version as string;
      const isCompatible = parseFloat(iosVersion) >= 13.0;
      
      results.push({
        testName: 'iOS Version Compatibility',
        platform: 'ios',
        success: isCompatible,
        message: isCompatible 
          ? `iOS ${iosVersion} is compatible`
          : `iOS ${iosVersion} may have compatibility issues`,
        details: { version: iosVersion, minRequired: '13.0' },
        severity: isCompatible ? 'info' : 'warning',
      });

      // Test iOS notification features
      const iOSNotificationHealth = iOSNotificationTester.getNotificationHealth();
      
      results.push({
        testName: 'iOS Notification Features',
        platform: 'ios',
        success: iOSNotificationHealth.isHealthy,
        message: iOSNotificationHealth.isHealthy 
          ? 'iOS notification features are healthy'
          : 'iOS notification features have issues',
        details: iOSNotificationHealth,
        severity: iOSNotificationHealth.isHealthy ? 'info' : 'warning',
      });

    } catch (error) {
      results.push({
        testName: 'iOS Features Test',
        platform: 'ios',
        success: false,
        message: `iOS features test failed: ${error}`,
        severity: 'error',
      });
    }
    
    return results;
  }

  /**
   * Test Android-specific features
   */
  private async testAndroidSpecificFeatures(): Promise<PlatformTestResult[]> {
    const results: PlatformTestResult[] = [];
    
    try {
      // Test Android API level
      const apiLevel = Platform.Version as number;
      const isCompatible = apiLevel >= 23; // Android 6.0+
      
      results.push({
        testName: 'Android API Level',
        platform: 'android',
        success: isCompatible,
        message: isCompatible 
          ? `Android API ${apiLevel} is compatible`
          : `Android API ${apiLevel} may have compatibility issues`,
        details: { apiLevel, minRequired: 23 },
        severity: isCompatible ? 'info' : 'warning',
      });

      // Test Android notification channels
      const androidNotificationHealth = androidNotificationTester.getChannelHealth();
      
      results.push({
        testName: 'Android Notification Channels',
        platform: 'android',
        success: androidNotificationHealth.isHealthy,
        message: androidNotificationHealth.isHealthy 
          ? 'Android notification channels are healthy'
          : 'Android notification channels have issues',
        details: androidNotificationHealth,
        severity: androidNotificationHealth.isHealthy ? 'info' : 'warning',
      });

    } catch (error) {
      results.push({
        testName: 'Android Features Test',
        platform: 'android',
        success: false,
        message: `Android features test failed: ${error}`,
        severity: 'error',
      });
    }
    
    return results;
  }

  /**
   * Test notification compatibility
   */
  private async testNotificationCompatibility(): Promise<PlatformTestResult[]> {
    const results: PlatformTestResult[] = [];
    
    try {
      // Test notification module availability
      const Notifications = await import('expo-notifications');
      
      results.push({
        testName: 'Notification Module',
        platform: 'both',
        success: !!Notifications,
        message: 'expo-notifications module is available',
        severity: 'info',
      });

      // Test permission status
      const { status } = await Notifications.getPermissionsAsync();
      
      results.push({
        testName: 'Notification Permissions',
        platform: 'both',
        success: status === 'granted',
        message: `Notification permission status: ${status}`,
        details: { status },
        severity: status === 'granted' ? 'info' : 'warning',
      });

    } catch (error) {
      results.push({
        testName: 'Notification Compatibility',
        platform: 'both',
        success: false,
        message: `Notification test failed: ${error}`,
        severity: 'error',
      });
    }
    
    return results;
  }

  /**
   * Test permission compatibility
   */
  private async testPermissionCompatibility(): Promise<PlatformTestResult[]> {
    const results: PlatformTestResult[] = [];
    
    try {
      // Test device detection
      const Device = await import('expo-device');
      
      results.push({
        testName: 'Device Detection',
        platform: 'both',
        success: Device.isDevice !== undefined,
        message: Device.isDevice 
          ? 'Running on physical device'
          : 'Running on simulator/emulator',
        details: { 
          isDevice: Device.isDevice,
          deviceType: Device.deviceType,
          brand: Device.brand,
          modelName: Device.modelName,
        },
        severity: 'info',
      });

    } catch (error) {
      results.push({
        testName: 'Permission Compatibility',
        platform: 'both',
        success: false,
        message: `Permission test failed: ${error}`,
        severity: 'error',
      });
    }
    
    return results;
  }

  /**
   * Test configuration compatibility
   */
  private async testConfigurationCompatibility(): Promise<PlatformTestResult[]> {
    const results: PlatformTestResult[] = [];
    
    try {
      const Constants = await import('expo-constants');
      const config = Constants.default.expoConfig;
      
      results.push({
        testName: 'App Configuration',
        platform: 'both',
        success: !!config,
        message: config ? 'App configuration is available' : 'App configuration is missing',
        details: config ? {
          name: config.name,
          version: config.version,
          sdkVersion: config.sdkVersion,
        } : null,
        severity: config ? 'info' : 'error',
      });

      // Test platform-specific config
      if (Platform.OS === 'ios' && config?.ios) {
        results.push({
          testName: 'iOS Configuration',
          platform: 'ios',
          success: !!config.ios.bundleIdentifier,
          message: config.ios.bundleIdentifier 
            ? `iOS bundle ID: ${config.ios.bundleIdentifier}`
            : 'iOS bundle identifier missing',
          severity: config.ios.bundleIdentifier ? 'info' : 'warning',
        });
      }

      if (Platform.OS === 'android' && config?.android) {
        results.push({
          testName: 'Android Configuration',
          platform: 'android',
          success: !!config.android.package,
          message: config.android.package 
            ? `Android package: ${config.android.package}`
            : 'Android package name missing',
          severity: config.android.package ? 'info' : 'warning',
        });
      }

    } catch (error) {
      results.push({
        testName: 'Configuration Compatibility',
        platform: 'both',
        success: false,
        message: `Configuration test failed: ${error}`,
        severity: 'error',
      });
    }
    
    return results;
  }

  /**
   * Generate test summary
   */
  private generateSummary(results: PlatformTestResult[]): {
    passed: number;
    failed: number;
    warnings: number;
    errors: number;
  } {
    return {
      passed: results.filter(r => r.success).length,
      failed: results.filter(r => !r.success).length,
      warnings: results.filter(r => r.severity === 'warning').length,
      errors: results.filter(r => r.severity === 'error').length,
    };
  }

  /**
   * Generate recommendations
   */
  private generateRecommendations(results: PlatformTestResult[]): string[] {
    const recommendations: string[] = [];
    const failedTests = results.filter(r => !r.success);
    const warnings = results.filter(r => r.severity === 'warning');

    if (failedTests.length === 0 && warnings.length === 0) {
      recommendations.push('All platform compatibility tests passed successfully');
      recommendations.push('The app is ready for deployment on the current platform');
      return recommendations;
    }

    if (failedTests.some(t => t.testName.includes('SDK'))) {
      recommendations.push('Update Expo SDK to the latest compatible version');
      recommendations.push('Run: npx expo install --fix');
    }

    if (failedTests.some(t => t.testName.includes('Notification'))) {
      recommendations.push('Check notification permissions and configuration');
      recommendations.push('Test notification functionality on physical device');
    }

    if (failedTests.some(t => t.testName.includes('Configuration'))) {
      recommendations.push('Review app.config.js for missing required fields');
      recommendations.push('Ensure platform-specific configuration is complete');
    }

    if (Platform.OS === 'ios' && warnings.some(w => w.platform === 'ios')) {
      recommendations.push('Review iOS-specific features and permissions');
      recommendations.push('Test on iOS device for full compatibility verification');
    }

    if (Platform.OS === 'android' && warnings.some(w => w.platform === 'android')) {
      recommendations.push('Review Android-specific features and permissions');
      recommendations.push('Test on Android device for full compatibility verification');
    }

    return recommendations;
  }

  /**
   * Log compatibility report
   */
  async logCompatibilityReport(): Promise<void> {
    const report = await this.runFullCompatibilityTest();
    
    console.log('\n🧪 Platform Compatibility Test Report');
    console.log('='.repeat(50));
    console.log(`📱 Platform: ${report.platform}`);
    console.log(`✅ Overall Compatibility: ${report.overallCompatibility ? 'PASS' : 'FAIL'}`);
    console.log(`📊 Summary: ${report.summary.passed} passed, ${report.summary.failed} failed, ${report.summary.warnings} warnings, ${report.summary.errors} errors`);
    
    if (report.summary.errors > 0) {
      console.log('\n🚨 Critical Issues:');
      report.testResults
        .filter(r => r.severity === 'error')
        .forEach(r => console.log(`  ❌ ${r.testName}: ${r.message}`));
    }
    
    if (report.summary.warnings > 0) {
      console.log('\n⚠️ Warnings:');
      report.testResults
        .filter(r => r.severity === 'warning')
        .forEach(r => console.log(`  ⚠️ ${r.testName}: ${r.message}`));
    }
    
    console.log('\n💡 Recommendations:');
    report.recommendations.forEach(rec => console.log(`  - ${rec}`));
    
    console.log('\n✨ All Test Results:');
    report.testResults.forEach(result => {
      const icon = result.success ? '✅' : '❌';
      console.log(`  ${icon} ${result.testName}: ${result.message}`);
    });
  }
}

// Export singleton instance
export const platformCompatibilityTester = new PlatformCompatibilityTester();
