/**
 * Ocean Soul Sparkles Mobile App - Product Card Component
 * Displays product/service information with add to cart functionality
 */

import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
} from 'react-native';
import { Product, Service } from '@/types/database';
import { usePOSStore } from '@/store/posStore';

interface ProductCardProps {
  item: Product | Service;
  type: 'product' | 'service';
}

const ProductCard: React.FC<ProductCardProps> = ({ item, type }) => {
  const { addToCart, getCartItemByItemId } = usePOSStore();
  
  const price = type === 'product' ? (item as Product).price : (item as Service).base_price;
  const cartItem = getCartItemByItemId(item.id, type);
  const isInCart = !!cartItem;
  const cartQuantity = cartItem?.quantity || 0;

  const handleAddToCart = () => {
    addToCart(item, type, 1);
  };

  const formatPrice = (price: number | undefined) => {
    if (price === undefined || price === null) {
      return '$0.00';
    }
    return `$${price.toFixed(2)}`;
  };

  const getItemIcon = () => {
    if (type === 'service') {
      return '✂️'; // Scissors for hair services
    }
    
    // Product icons based on category
    const category = item.category?.toLowerCase() || '';
    if (category.includes('hair')) return '💇‍♀️';
    if (category.includes('braid')) return '🎀';
    if (category.includes('care')) return '🧴';
    if (category.includes('tool')) return '🔧';
    if (category.includes('accessory')) return '💎';
    return '📦'; // Default product icon
  };

  return (
    <TouchableOpacity 
      style={[styles.card, isInCart && styles.cardInCart]} 
      onPress={handleAddToCart}
      activeOpacity={0.7}
    >
      {/* Product Image or Icon */}
      <View style={styles.imageContainer}>
        {item.image_url ? (
          <Image 
            source={{ uri: item.image_url }} 
            style={styles.image}
            resizeMode="cover"
          />
        ) : (
          <View style={styles.placeholderImage}>
            <Text style={styles.placeholderIcon}>{getItemIcon()}</Text>
          </View>
        )}
        
        {/* Cart Badge */}
        {isInCart && (
          <View style={styles.cartBadge}>
            <Text style={styles.cartBadgeText}>{cartQuantity}</Text>
          </View>
        )}
      </View>

      {/* Product Info */}
      <View style={styles.info}>
        <Text style={styles.name} numberOfLines={2}>
          {item.name}
        </Text>
        
        {item.description && (
          <Text style={styles.description} numberOfLines={2}>
            {item.description}
          </Text>
        )}
        
        <View style={styles.footer}>
          <Text style={styles.price}>{formatPrice(price)}</Text>
          
          {type === 'service' && (item as Service).duration_minutes && (
            <Text style={styles.duration}>
              {(item as Service).duration_minutes}min
            </Text>
          )}
          
          {type === 'product' && (item as Product).stock_quantity !== undefined && (
            <Text style={[
              styles.stock,
              (item as Product).stock_quantity! <= 5 && styles.lowStock
            ]}>
              Stock: {(item as Product).stock_quantity}
            </Text>
          )}
        </View>
        
        {item.category && (
          <View style={styles.categoryBadge}>
            <Text style={styles.categoryText}>{item.category}</Text>
          </View>
        )}
      </View>

      {/* Add Button */}
      <View style={styles.addButton}>
        <Text style={styles.addButtonText}>
          {isInCart ? '+' : 'Add'}
        </Text>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  card: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 12,
    margin: 6,
    boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
    elevation: 3,
    borderWidth: 1,
    borderColor: '#f0f0f0',
  },
  cardInCart: {
    borderColor: '#FF9A8B',
    borderWidth: 2,
  },
  imageContainer: {
    position: 'relative',
    marginBottom: 8,
  },
  image: {
    width: '100%',
    height: 120,
    borderRadius: 8,
  },
  placeholderImage: {
    width: '100%',
    height: 120,
    borderRadius: 8,
    backgroundColor: '#f8f9fa',
    justifyContent: 'center',
    alignItems: 'center',
  },
  placeholderIcon: {
    fontSize: 40,
  },
  cartBadge: {
    position: 'absolute',
    top: 8,
    right: 8,
    backgroundColor: '#FF9A8B',
    borderRadius: 12,
    minWidth: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
  cartBadgeText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
  },
  info: {
    flex: 1,
  },
  name: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 4,
  },
  description: {
    fontSize: 14,
    color: '#666',
    marginBottom: 8,
    lineHeight: 18,
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  price: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FF9A8B',
  },
  duration: {
    fontSize: 12,
    color: '#666',
    backgroundColor: '#f0f0f0',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
  },
  stock: {
    fontSize: 12,
    color: '#666',
  },
  lowStock: {
    color: '#ff4444',
    fontWeight: '600',
  },
  categoryBadge: {
    alignSelf: 'flex-start',
    backgroundColor: '#e3f2fd',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    marginBottom: 8,
  },
  categoryText: {
    fontSize: 12,
    color: '#1976d2',
    fontWeight: '500',
  },
  addButton: {
    backgroundColor: '#FF9A8B',
    borderRadius: 8,
    paddingVertical: 8,
    paddingHorizontal: 16,
    alignItems: 'center',
    marginTop: 8,
  },
  addButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
  },
});

export default ProductCard;
