#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

console.log('🔧 Adding Vercel build scripts to package.json...\n');

try {
  // Read current package.json
  const packageJsonPath = path.join(process.cwd(), 'package.json');
  const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));

  // Add missing scripts
  if (!packageJson.scripts) {
    packageJson.scripts = {};
  }

  const scriptsToAdd = {
    'build:web': 'expo export --platform web',
    'build:vercel': 'expo export --platform web --output-dir dist',
    'deploy': 'npm run build:vercel && vercel --prod'
  };

  let scriptsAdded = 0;
  Object.entries(scriptsToAdd).forEach(([scriptName, scriptCommand]) => {
    if (!packageJson.scripts[scriptName]) {
      packageJson.scripts[scriptName] = scriptCommand;
      console.log(`✅ Added script: ${scriptName}`);
      scriptsAdded++;
    } else {
      console.log(`⚠️  Script already exists: ${scriptName}`);
    }
  });

  if (scriptsAdded > 0) {
    // Write updated package.json
    fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2) + '\n');
    console.log(`\n✅ Successfully added ${scriptsAdded} scripts to package.json`);
  } else {
    console.log('\n✅ All scripts already exist');
  }

  console.log('\n📋 Available scripts:');
  Object.keys(packageJson.scripts).forEach(script => {
    console.log(`   npm run ${script}`);
  });

} catch (error) {
  console.error('❌ Error updating package.json:', error.message);
  process.exit(1);
}