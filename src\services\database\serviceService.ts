/**
 * Ocean Soul Sparkles Mobile App - Service Service
 * Handles service management and queries
 */

import { supabase } from './supabase';
import { Service, DatabaseResponse, DatabaseListResponse, QueryFilters } from '@/types/database';

export class ServiceService {

  /**
   * Get all services with optional filtering
   */
  async getServices(filters?: QueryFilters): Promise<DatabaseListResponse<Service>> {
    try {
      console.log('💅 Loading services...');
      
      let query = supabase
        .from('services')
        .select('*');

      // Apply search filter
      if (filters?.search) {
        query = query.or(`name.ilike.%${filters.search}%,description.ilike.%${filters.search}%,category.ilike.%${filters.search}%`);
      }

      // Apply category filter
      if (filters?.filters?.category) {
        query = query.eq('category', filters.filters.category);
      }

      // Apply active status filter
      if (filters?.filters?.is_active !== undefined) {
        query = query.eq('is_active', filters.filters.is_active);
      }

      // Apply ordering
      const orderBy = filters?.order_by || 'name';
      const orderDirection = filters?.order_direction || 'asc';
      query = query.order(orderBy, { ascending: orderDirection === 'asc' });

      // Apply pagination
      if (filters?.limit) {
        query = query.limit(filters.limit);
      }
      if (filters?.offset) {
        query = query.range(filters.offset, filters.offset + (filters.limit || 20) - 1);
      }

      const { data, error, count } = await query;

      if (error) {
        console.error('❌ Get services error:', error);
        return { data: null, error, count: 0 };
      }

      console.log(`✅ Loaded ${data?.length || 0} services`);
      return { data: data || [], error: null, count: count || data?.length || 0 };
    } catch (error) {
      console.error('❌ Service service error:', error);
      return { data: null, error: error as Error, count: 0 };
    }
  }

  /**
   * Get service by ID
   */
  async getServiceById(id: string): Promise<DatabaseResponse<Service>> {
    try {
      const { data, error } = await supabase
        .from('services')
        .select('*')
        .eq('id', id)
        .single();

      if (error) {
        console.error('❌ Get service error:', error);
        return { data: null, error };
      }

      return { data, error: null };
    } catch (error) {
      console.error('❌ Get service service error:', error);
      return { data: null, error: error as Error };
    }
  }

  /**
   * Get active services only
   */
  async getActiveServices(): Promise<DatabaseListResponse<Service>> {
    return this.getServices({
      filters: { is_active: true },
      order_by: 'name',
      order_direction: 'asc',
    });
  }

  /**
   * Get services by category
   */
  async getServicesByCategory(category: string): Promise<DatabaseListResponse<Service>> {
    return this.getServices({
      filters: { 
        category,
        is_active: true 
      },
      order_by: 'name',
      order_direction: 'asc',
    });
  }

  /**
   * Create a new service
   */
  async createService(serviceData: Partial<Service>): Promise<DatabaseResponse<Service>> {
    try {
      console.log('📝 Creating new service...');

      const { data, error } = await supabase
        .from('services')
        .insert([{
          name: serviceData.name,
          description: serviceData.description,
          base_price: serviceData.base_price,
          duration_minutes: serviceData.duration_minutes,
          category: serviceData.category,
          is_active: serviceData.is_active ?? true,
          image_url: serviceData.image_url,
        }])
        .select('*')
        .single();

      if (error) {
        console.error('❌ Create service error:', error);
        return { data: null, error };
      }

      console.log('✅ Service created successfully');
      return { data, error: null };
    } catch (error) {
      console.error('❌ Create service service error:', error);
      return { data: null, error: error as Error };
    }
  }

  /**
   * Update an existing service
   */
  async updateService(id: string, updateData: Partial<Service>): Promise<DatabaseResponse<Service>> {
    try {
      console.log('📝 Updating service:', id);

      const { data, error } = await supabase
        .from('services')
        .update({
          name: updateData.name,
          description: updateData.description,
          base_price: updateData.base_price,
          duration_minutes: updateData.duration_minutes,
          category: updateData.category,
          is_active: updateData.is_active,
          image_url: updateData.image_url,
          updated_at: new Date().toISOString(),
        })
        .eq('id', id)
        .select('*')
        .single();

      if (error) {
        console.error('❌ Update service error:', error);
        return { data: null, error };
      }

      console.log('✅ Service updated successfully');
      return { data, error: null };
    } catch (error) {
      console.error('❌ Update service service error:', error);
      return { data: null, error: error as Error };
    }
  }

  /**
   * Delete a service
   */
  async deleteService(id: string): Promise<DatabaseResponse<boolean>> {
    try {
      console.log('🗑️ Deleting service:', id);

      const { error } = await supabase
        .from('services')
        .delete()
        .eq('id', id);

      if (error) {
        console.error('❌ Delete service error:', error);
        return { data: null, error };
      }

      console.log('✅ Service deleted successfully');
      return { data: true, error: null };
    } catch (error) {
      console.error('❌ Delete service service error:', error);
      return { data: null, error: error as Error };
    }
  }

  /**
   * Search services by name, description, or category
   */
  async searchServices(searchTerm: string, limit: number = 20): Promise<DatabaseListResponse<Service>> {
    return this.getServices({
      search: searchTerm,
      limit,
      order_by: 'name',
      order_direction: 'asc',
    });
  }

  /**
   * Get service categories
   */
  async getServiceCategories(): Promise<DatabaseResponse<string[]>> {
    try {
      const { data, error } = await supabase
        .from('services')
        .select('category')
        .eq('is_active', true);

      if (error) {
        console.error('❌ Get service categories error:', error);
        return { data: null, error };
      }

      // Extract unique categories
      const categories = [...new Set(data?.map(item => item.category).filter(Boolean))];
      return { data: categories, error: null };
    } catch (error) {
      console.error('❌ Get service categories service error:', error);
      return { data: null, error: error as Error };
    }
  }
}

// Export singleton instance
export const serviceService = new ServiceService();
