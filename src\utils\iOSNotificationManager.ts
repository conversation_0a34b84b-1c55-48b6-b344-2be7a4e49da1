/**
 * Ocean Soul Sparkles Mobile App - iOS Notification Manager
 * Manages iOS-specific notification features and permissions
 */

import * as Notifications from 'expo-notifications';
import { Platform } from 'react-native';

export interface iOSNotificationSettings {
  allowAlert: boolean;
  allowBadge: boolean;
  allowSound: boolean;
  allowCriticalAlerts: boolean;
  allowProvisional: boolean;
  providesAppNotificationSettings: boolean;
  allowAnnouncements: boolean;
}

export interface iOSNotificationOptions {
  categoryId?: string;
  threadId?: string;
  summaryArgument?: string;
  summaryArgumentCount?: number;
  targetContentId?: string;
  interruptionLevel?: 'passive' | 'active' | 'timeSensitive' | 'critical';
  relevanceScore?: number;
}

class iOSNotificationManager {
  private isInitialized = false;
  private notificationCategories: Map<string, any> = new Map();

  /**
   * Initialize iOS notification features
   */
  async initialize(): Promise<void> {
    if (Platform.OS !== 'ios') {
      console.log('📱 Skipping iOS notification setup on non-iOS platform');
      return;
    }

    if (this.isInitialized) {
      console.log('📱 iOS notification manager already initialized');
      return;
    }

    try {
      console.log('📱 Initializing iOS notification features...');

      // Set up notification categories
      await this.setupNotificationCategories();

      // Configure notification presentation options
      await this.configureNotificationPresentation();

      this.isInitialized = true;
      console.log('✅ iOS notification manager initialized');

    } catch (error) {
      console.error('❌ Failed to initialize iOS notification manager:', error);
      throw error;
    }
  }

  /**
   * Request iOS-specific notification permissions
   */
  async requestPermissions(options: {
    allowAlert?: boolean;
    allowBadge?: boolean;
    allowSound?: boolean;
    allowCriticalAlerts?: boolean;
    allowProvisional?: boolean;
    allowAnnouncements?: boolean;
  } = {}): Promise<iOSNotificationSettings> {
    if (Platform.OS !== 'ios') {
      throw new Error('iOS permissions can only be requested on iOS platform');
    }

    try {
      const permissionRequest = {
        ios: {
          allowAlert: options.allowAlert ?? true,
          allowBadge: options.allowBadge ?? true,
          allowSound: options.allowSound ?? true,
          allowCriticalAlerts: options.allowCriticalAlerts ?? false,
          allowProvisional: options.allowProvisional ?? false,
          allowAnnouncements: options.allowAnnouncements ?? false,
          providesAppNotificationSettings: true,
        },
      };

      const { status, ios } = await Notifications.requestPermissionsAsync(permissionRequest);

      if (status !== 'granted') {
        console.warn('⚠️ iOS notification permissions not fully granted:', status);
      }

      const settings: iOSNotificationSettings = {
        allowAlert: ios?.allowsAlert ?? false,
        allowBadge: ios?.allowsBadge ?? false,
        allowSound: ios?.allowsSound ?? false,
        allowCriticalAlerts: ios?.allowsCriticalAlerts ?? false,
        allowProvisional: ios?.allowsProvisional ?? false,
        providesAppNotificationSettings: ios?.providesAppNotificationSettings ?? false,
        allowAnnouncements: ios?.allowsAnnouncements ?? false,
      };

      console.log('📱 iOS notification permissions:', settings);
      return settings;

    } catch (error) {
      console.error('❌ Failed to request iOS notification permissions:', error);
      throw error;
    }
  }

  /**
   * Set up iOS notification categories with actions
   */
  private async setupNotificationCategories(): Promise<void> {
    try {
      const categories = [
        // Booking notification category
        {
          identifier: 'BOOKING_NOTIFICATION',
          actions: [
            {
              identifier: 'ACCEPT_BOOKING',
              title: 'Accept',
              options: {
                opensAppToForeground: true,
              },
            },
            {
              identifier: 'VIEW_DETAILS',
              title: 'View Details',
              options: {
                opensAppToForeground: true,
              },
            },
            {
              identifier: 'QUICK_REPLY',
              title: 'Reply',
              options: {
                opensAppToForeground: false,
              },
              textInput: {
                submitButtonTitle: 'Send',
                placeholder: 'Type your response...',
              },
            },
          ],
          options: {
            customDismissAction: true,
            allowInCarPlay: false,
            showTitle: true,
            showSubtitle: true,
          },
        },

        // Staff message category
        {
          identifier: 'STAFF_MESSAGE',
          actions: [
            {
              identifier: 'REPLY_MESSAGE',
              title: 'Reply',
              options: {
                opensAppToForeground: false,
              },
              textInput: {
                submitButtonTitle: 'Send',
                placeholder: 'Type your reply...',
              },
            },
            {
              identifier: 'MARK_READ',
              title: 'Mark as Read',
              options: {
                opensAppToForeground: false,
              },
            },
            {
              identifier: 'VIEW_THREAD',
              title: 'View Thread',
              options: {
                opensAppToForeground: true,
              },
            },
          ],
          options: {
            customDismissAction: true,
            allowInCarPlay: true,
            showTitle: true,
            showSubtitle: true,
          },
        },

        // Urgent notification category
        {
          identifier: 'URGENT_NOTIFICATION',
          actions: [
            {
              identifier: 'RESPOND_NOW',
              title: 'Respond Now',
              options: {
                opensAppToForeground: true,
                authenticationRequired: false,
              },
            },
            {
              identifier: 'CALL_CUSTOMER',
              title: 'Call Customer',
              options: {
                opensAppToForeground: false,
              },
            },
          ],
          options: {
            customDismissAction: false,
            allowInCarPlay: false,
            showTitle: true,
            showSubtitle: true,
          },
        },
      ];

      await Notifications.setNotificationCategoryAsync(
        'BOOKING_NOTIFICATION',
        categories[0].actions,
        categories[0].options
      );

      await Notifications.setNotificationCategoryAsync(
        'STAFF_MESSAGE',
        categories[1].actions,
        categories[1].options
      );

      await Notifications.setNotificationCategoryAsync(
        'URGENT_NOTIFICATION',
        categories[2].actions,
        categories[2].options
      );

      // Store categories for reference
      categories.forEach(category => {
        this.notificationCategories.set(category.identifier, category);
      });

      console.log('📱 iOS notification categories configured');

    } catch (error) {
      console.error('❌ Failed to setup iOS notification categories:', error);
      throw error;
    }
  }

  /**
   * Configure notification presentation options
   */
  private async configureNotificationPresentation(): Promise<void> {
    try {
      // Set notification handler for iOS-specific behavior
      Notifications.setNotificationHandler({
        handleNotification: async (notification) => {
          const content = notification.request.content;
          const categoryId = content.categoryIdentifier;

          // Determine presentation based on category and app state
          let shouldShowAlert = true;
          let shouldPlaySound = true;
          let shouldSetBadge = true;

          // Handle critical alerts
          if (categoryId === 'URGENT_NOTIFICATION') {
            shouldShowAlert = true;
            shouldPlaySound = true;
            shouldSetBadge = true;
          }

          return {
            shouldShowAlert,
            shouldPlaySound,
            shouldSetBadge,
          };
        },
      });

      console.log('📱 iOS notification presentation configured');

    } catch (error) {
      console.error('❌ Failed to configure iOS notification presentation:', error);
      throw error;
    }
  }

  /**
   * Get iOS category for notification type
   */
  getCategoryForNotificationType(notificationType: string): string {
    switch (notificationType) {
      case 'urgent_booking':
      case 'emergency':
        return 'URGENT_NOTIFICATION';
      
      case 'staff_message':
      case 'chat_message':
        return 'STAFF_MESSAGE';
      
      case 'new_booking':
      case 'booking_update':
      case 'availability_request':
        return 'BOOKING_NOTIFICATION';
      
      default:
        return 'BOOKING_NOTIFICATION';
    }
  }

  /**
   * Create iOS-specific notification content
   */
  createiOSNotificationContent(
    title: string,
    body: string,
    data: any = {},
    options: iOSNotificationOptions = {}
  ): any {
    const content: any = {
      title,
      body,
      data,
      sound: 'default',
      badge: 1,
    };

    // Add iOS-specific options
    if (options.categoryId) {
      content.categoryIdentifier = options.categoryId;
    }

    if (options.threadId) {
      content.threadIdentifier = options.threadId;
    }

    if (options.summaryArgument) {
      content.summaryArgument = options.summaryArgument;
      content.summaryArgumentCount = options.summaryArgumentCount || 1;
    }

    if (options.targetContentId) {
      content.targetContentIdentifier = options.targetContentId;
    }

    if (options.interruptionLevel) {
      content.interruptionLevel = options.interruptionLevel;
    }

    if (options.relevanceScore !== undefined) {
      content.relevanceScore = options.relevanceScore;
    }

    return content;
  }

  /**
   * Handle notification action responses
   */
  setupNotificationActionHandlers(): void {
    if (Platform.OS !== 'ios') {
      return;
    }

    // Handle notification responses
    Notifications.addNotificationResponseReceivedListener((response) => {
      const { actionIdentifier, userText, notification } = response;
      const notificationData = notification.request.content.data;

      console.log('📱 iOS notification action received:', actionIdentifier);

      switch (actionIdentifier) {
        case 'ACCEPT_BOOKING':
          this.handleAcceptBooking(notificationData);
          break;
        
        case 'QUICK_REPLY':
        case 'REPLY_MESSAGE':
          if (userText) {
            this.handleQuickReply(notificationData, userText);
          }
          break;
        
        case 'MARK_READ':
          this.handleMarkAsRead(notificationData);
          break;
        
        case 'RESPOND_NOW':
          this.handleRespondNow(notificationData);
          break;
        
        case 'CALL_CUSTOMER':
          this.handleCallCustomer(notificationData);
          break;
        
        default:
          console.log('📱 Unhandled notification action:', actionIdentifier);
      }
    });
  }

  /**
   * Handle accept booking action
   */
  private handleAcceptBooking(data: any): void {
    console.log('📱 Handling accept booking action:', data);
    // Implementation would navigate to booking screen or update booking status
  }

  /**
   * Handle quick reply action
   */
  private handleQuickReply(data: any, text: string): void {
    console.log('📱 Handling quick reply:', text, data);
    // Implementation would send the reply message
  }

  /**
   * Handle mark as read action
   */
  private handleMarkAsRead(data: any): void {
    console.log('📱 Handling mark as read:', data);
    // Implementation would mark message as read
  }

  /**
   * Handle respond now action
   */
  private handleRespondNow(data: any): void {
    console.log('📱 Handling respond now:', data);
    // Implementation would open app to specific screen
  }

  /**
   * Handle call customer action
   */
  private handleCallCustomer(data: any): void {
    console.log('📱 Handling call customer:', data);
    // Implementation would initiate phone call
  }

  /**
   * Get notification categories
   */
  getNotificationCategories(): Map<string, any> {
    return this.notificationCategories;
  }

  /**
   * Check if iOS features are available
   */
  isInitialized(): boolean {
    return this.isInitialized && Platform.OS === 'ios';
  }
}

// Export singleton instance
export const iOSNotificationManager = new iOSNotificationManager();
