import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TextInput,
  TouchableOpacity,
  SafeAreaView,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { staffCommunicationService, ChatMessage, AvailabilityResponse } from '@/services/communication/staffCommunicationService';

interface ChatThreadScreenProps {
  route: {
    params: {
      threadId: string;
      bookingTitle: string;
    };
  };
}

/**
 * Get user-friendly error message from error object
 */
const getErrorMessage = (error: unknown): string => {
  if (error instanceof Error) {
    // Return specific user-friendly messages for common errors
    if (error.message.includes('Failed to fetch')) {
      return 'Unable to connect to server. Please check your internet connection.';
    }
    if (error.message.includes('timeout')) {
      return 'Request timed out. Please try again.';
    }
    if (error.message.includes('not found')) {
      return 'Chat thread not found. It may have been deleted.';
    }
    if (error.message.includes('permission') || error.message.includes('unauthorized')) {
      return 'You do not have permission to access this chat.';
    }
    // Return the original error message if it's user-friendly
    return error.message.length < 100 ? error.message : 'An unexpected error occurred.';
  }
  return 'An unexpected error occurred. Please try again.';
};

export default function ChatThreadScreen({ route }: ChatThreadScreenProps) {
  const { threadId, bookingTitle } = route.params;
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [availabilityResponses, setAvailabilityResponses] = useState<AvailabilityResponse[]>([]);
  const [newMessage, setNewMessage] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [sendingMessage, setSendingMessage] = useState(false);
  const [authError, setAuthError] = useState<string | null>(null);
  const flatListRef = useRef<FlatList>(null);
  const isMountedRef = useRef(true);
  const subscriptionRef = useRef<(() => void) | null>(null);

  useEffect(() => {
    // Set mounted flag
    isMountedRef.current = true;

    // Load initial data
    loadThreadData();

    // Subscribe to real-time updates with mounted check
    const unsubscribe = staffCommunicationService.subscribeToThread(
      threadId,
      (message) => {
        // Only update state if component is still mounted
        if (isMountedRef.current) {
          setMessages(prev => [...prev, message]);
          scrollToBottom();
        }
      }
    );

    // Store subscription reference for cleanup
    subscriptionRef.current = unsubscribe;

    return () => {
      // Mark component as unmounted
      isMountedRef.current = false;

      // Clean up subscription
      if (subscriptionRef.current) {
        subscriptionRef.current();
        subscriptionRef.current = null;
      }
    };
  }, [threadId]);

  const loadThreadData = async () => {
    try {
      if (!isMountedRef.current) return;

      setIsLoading(true);
      setError(null);
      setAuthError(null);

      const [messagesData, responsesData] = await Promise.all([
        staffCommunicationService.getThreadMessages(threadId),
        staffCommunicationService.getAvailabilityResponses(threadId),
      ]);

      // Only update state if component is still mounted
      if (isMountedRef.current) {
        setMessages(messagesData);
        setAvailabilityResponses(responsesData);
      }
    } catch (error) {
      console.error('Failed to load thread data:', error);

      // Only update state if component is still mounted
      if (isMountedRef.current) {
        const errorMessage = getErrorMessage(error);

        // Check if it's an auth error
        if (error instanceof Error && (
          error.message.includes('auth') ||
          error.message.includes('unauthorized') ||
          error.message.includes('permission')
        )) {
          setAuthError('Authentication required. Please sign in again.');
        } else if (error instanceof Error && error.message.includes('network')) {
          setError('Network connection issue. Please check your internet connection and try again.');
        } else if (error instanceof Error && error.message.includes('timeout')) {
          setError('Request timed out. Please try again.');
        } else if (error instanceof Error && error.message.includes('not found')) {
          setError('Chat thread not found. It may have been deleted.');
        } else {
          setError(errorMessage);
        }
      }
    } finally {
      if (isMountedRef.current) {
        setIsLoading(false);
      }
    }
  };

  const sendMessage = async () => {
    if (!newMessage.trim() || sendingMessage || !isMountedRef.current) return;

    const message = newMessage.trim();
    setNewMessage('');
    setSendingMessage(true);
    setError(null);
    setAuthError(null);

    try {
      const result = await staffCommunicationService.sendMessage(threadId, message);

      if (!isMountedRef.current) return;

      if (!result) {
        setError('Failed to send message. Please try again.');
        setNewMessage(message); // Restore message if failed
      }
    } catch (error) {
      console.error('Failed to send message:', error);

      if (!isMountedRef.current) return;

      // Provide specific error messages based on error type
      if (error instanceof Error && (
        error.message.includes('auth') ||
        error.message.includes('unauthorized') ||
        error.message.includes('permission')
      )) {
        setAuthError('Authentication required. Please sign in again.');
      } else if (error instanceof Error && error.message.includes('network')) {
        setError('Network error. Please check your connection and try again.');
      } else if (error instanceof Error && error.message.includes('timeout')) {
        setError('Message send timed out. Please try again.');
      } else if (error instanceof Error && error.message.includes('rate limit')) {
        setError('Too many messages sent. Please wait a moment and try again.');
      } else {
        const errorMessage = getErrorMessage(error);
        setError(`Failed to send message: ${errorMessage}`);
      }

      setNewMessage(message); // Restore message if failed
    } finally {
      if (isMountedRef.current) {
        setSendingMessage(false);
      }
    }
  };

  const sendAvailabilityResponse = async (status: 'available' | 'unavailable' | 'maybe') => {
    await staffCommunicationService.sendAvailabilityResponse(threadId, status);
    loadThreadData(); // Refresh to show updated responses
  };

  const scrollToBottom = () => {
    setTimeout(() => {
      flatListRef.current?.scrollToEnd({ animated: true });
    }, 100);
  };

  const renderMessage = ({ item }: { item: ChatMessage }) => (
    <View style={[
      styles.messageContainer,
      item.message_type === 'system' ? styles.systemMessage : styles.userMessage
    ]}>
      <Text style={styles.senderName}>{item.sender_name}</Text>
      <Text style={styles.messageText}>{item.message}</Text>
      <Text style={styles.timestamp}>
        {new Date(item.created_at).toLocaleTimeString()}
      </Text>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>{bookingTitle}</Text>
      </View>

      {/* Error Banner */}
      {error && (
        <View style={styles.errorBanner}>
          <Text style={styles.errorText}>⚠️ {error}</Text>
          <TouchableOpacity
            style={styles.retryButton}
            onPress={() => {
              setError(null);
              loadThreadData();
            }}
          >
            <Text style={styles.retryButtonText}>Retry</Text>
          </TouchableOpacity>
        </View>
      )}

      {/* Auth Error Banner */}
      {authError && (
        <View style={styles.authErrorBanner}>
          <Text style={styles.authErrorText}>🔐 {authError}</Text>
          <TouchableOpacity
            style={styles.authRetryButton}
            onPress={() => {
              setAuthError(null);
              // Navigate to login screen or trigger re-auth
              // This would typically navigate back to login
            }}
          >
            <Text style={styles.authRetryButtonText}>Sign In</Text>
          </TouchableOpacity>
        </View>
      )}

      {/* Loading State */}
      {isLoading && (
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>Loading chat messages...</Text>
        </View>
      )}

      {/* Empty State */}
      {!isLoading && !error && !authError && messages.length === 0 && (
        <View style={styles.emptyContainer}>
          <Text style={styles.emptyTitle}>No messages yet</Text>
          <Text style={styles.emptySubtitle}>Start the conversation by sending a message below</Text>
        </View>
      )}

      {/* Availability Responses */}
      {!isLoading && !error && !authError && (
        <View style={styles.availabilitySection}>
        <Text style={styles.sectionTitle}>Staff Availability</Text>
        <View style={styles.availabilityButtons}>
          <TouchableOpacity
            style={[styles.availabilityButton, styles.availableButton]}
            onPress={() => sendAvailabilityResponse('available')}
          >
            <Text style={styles.buttonText}>✅ Available</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.availabilityButton, styles.maybeButton]}
            onPress={() => sendAvailabilityResponse('maybe')}
          >
            <Text style={styles.buttonText}>❓ Maybe</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.availabilityButton, styles.unavailableButton]}
            onPress={() => sendAvailabilityResponse('unavailable')}
          >
            <Text style={styles.buttonText}>❌ Unavailable</Text>
          </TouchableOpacity>
        </View>
      </View>
      )}

      {/* Messages */}
      {!isLoading && !error && !authError && (
      <FlatList
        ref={flatListRef}
        data={messages}
        renderItem={renderMessage}
        keyExtractor={(item) => item.id}
        style={styles.messagesList}
        onContentSizeChange={scrollToBottom}
      />
      )}

      {/* Message Input */}
      {!isLoading && !error && !authError && (
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.inputContainer}
      >
        <TextInput
          style={styles.textInput}
          value={newMessage}
          onChangeText={setNewMessage}
          placeholder="Type a message..."
          multiline
          maxLength={500}
        />
        <TouchableOpacity
          style={[styles.sendButton, (sendingMessage || !newMessage.trim()) && styles.sendButtonDisabled]}
          onPress={sendMessage}
          disabled={sendingMessage || !newMessage.trim()}
        >
          <Text style={styles.sendButtonText}>
            {sendingMessage ? 'Sending...' : 'Send'}
          </Text>
        </TouchableOpacity>
      </KeyboardAvoidingView>
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    backgroundColor: '#2E86AB',
    padding: 16,
  },
  headerTitle: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
  },
  availabilitySection: {
    backgroundColor: 'white',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 12,
  },
  availabilityButtons: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  availabilityButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    minWidth: 100,
    alignItems: 'center',
  },
  availableButton: {
    backgroundColor: '#4CAF50',
  },
  maybeButton: {
    backgroundColor: '#FF9800',
  },
  unavailableButton: {
    backgroundColor: '#F44336',
  },
  buttonText: {
    color: 'white',
    fontWeight: 'bold',
  },
  messagesList: {
    flex: 1,
    padding: 16,
  },
  messageContainer: {
    marginBottom: 12,
    padding: 12,
    borderRadius: 8,
  },
  systemMessage: {
    backgroundColor: '#e3f2fd',
    alignSelf: 'center',
  },
  userMessage: {
    backgroundColor: 'white',
    alignSelf: 'flex-start',
  },
  senderName: {
    fontWeight: 'bold',
    marginBottom: 4,
    color: '#2E86AB',
  },
  messageText: {
    fontSize: 16,
    marginBottom: 4,
  },
  timestamp: {
    fontSize: 12,
    color: '#666',
  },
  inputContainer: {
    flexDirection: 'row',
    padding: 16,
    backgroundColor: 'white',
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
  },
  textInput: {
    flex: 1,
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginRight: 8,
    maxHeight: 100,
  },
  sendButton: {
    backgroundColor: '#2E86AB',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 20,
    justifyContent: 'center',
  },
  sendButtonText: {
    color: 'white',
    fontWeight: 'bold',
  },
  sendButtonDisabled: {
    backgroundColor: '#9ca3af',
  },
  errorBanner: {
    backgroundColor: '#fee2e2',
    borderLeftWidth: 4,
    borderLeftColor: '#ef4444',
    padding: 12,
    marginHorizontal: 16,
    marginVertical: 8,
    borderRadius: 8,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  errorText: {
    color: '#dc2626',
    fontSize: 14,
    flex: 1,
    marginRight: 8,
  },
  retryButton: {
    backgroundColor: '#ef4444',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
  },
  retryButtonText: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
  },
  authErrorBanner: {
    backgroundColor: '#fef3c7',
    borderLeftWidth: 4,
    borderLeftColor: '#f59e0b',
    padding: 12,
    marginHorizontal: 16,
    marginVertical: 8,
    borderRadius: 8,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  authErrorText: {
    color: '#92400e',
    fontSize: 14,
    flex: 1,
    marginRight: 8,
  },
  authRetryButton: {
    backgroundColor: '#f59e0b',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
  },
  authRetryButtonText: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  loadingText: {
    color: '#666',
    fontSize: 16,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  emptySubtitle: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
  },
});