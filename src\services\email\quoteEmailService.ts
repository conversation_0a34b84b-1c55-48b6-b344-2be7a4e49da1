/**
 * Ocean Soul Sparkles Mobile App - Quote Email Service
 * Handles automated email notifications for quote creation and management
 */

import { emailService } from './emailService';
import { Quote, Booking, Customer, Service, AdminUser } from '@/types/database';
import { bookingService } from '@/services/database/bookingService';
import { customerService } from '@/services/database/customerService';
import { serviceService } from '@/services/database/serviceService';
import { staffService } from '@/services/database/staffService';

export interface QuoteEmailData {
  quote: Quote;
  customer: Customer;
  booking?: Booking;
  service?: Service;
  staff?: AdminUser;
}

export interface QuoteEmailResult {
  success: boolean;
  emailId?: string;
  error?: string;
}

export class QuoteEmailService {
  private static instance: QuoteEmailService;

  private constructor() {}

  public static getInstance(): QuoteEmailService {
    if (!QuoteEmailService.instance) {
      QuoteEmailService.instance = new QuoteEmailService();
    }
    return QuoteEmailService.instance;
  }

  /**
   * Load all related data for quote email
   */
  private async loadQuoteEmailData(quote: Quote): Promise<QuoteEmailData | null> {
    try {
      console.log('📧 Loading quote email data...');

      // Load customer data
      const customerResult = await customerService.getCustomerById(quote.customer_id);
      if (customerResult.error || !customerResult.data) {
        console.error('❌ Failed to load customer for quote email:', customerResult.error);
        return null;
      }

      const emailData: QuoteEmailData = {
        quote,
        customer: customerResult.data,
      };

      // Load booking data if available
      if (quote.booking_id) {
        const bookingResult = await bookingService.getBookingById(quote.booking_id);
        if (bookingResult.data) {
          emailData.booking = bookingResult.data;

          // Load service data from booking
          if (bookingResult.data.service_id) {
            const serviceResult = await serviceService.getServiceById(bookingResult.data.service_id);
            if (serviceResult.data) {
              emailData.service = serviceResult.data;
            }
          }
        }
      }

      // Load staff data if available
      if (quote.staff_id) {
        const staffResult = await staffService.getStaffById(quote.staff_id);
        if (staffResult.data) {
          emailData.staff = staffResult.data;
        }
      }

      console.log('✅ Quote email data loaded successfully');
      return emailData;

    } catch (error) {
      console.error('❌ Failed to load quote email data:', error);
      return null;
    }
  }

  /**
   * Generate quote number for email
   */
  private generateQuoteNumber(quote: Quote): string {
    // Use quote ID or create a formatted quote number
    const date = new Date(quote.created_at);
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    
    // Extract last 6 characters of quote ID for uniqueness
    const quoteId = quote.id.slice(-6).toUpperCase();
    
    return `OSS-Q${year}${month}${day}-${quoteId}`;
  }

  /**
   * Send quote email notification
   */
  async sendQuoteEmail(quote: Quote): Promise<QuoteEmailResult> {
    try {
      console.log('📧 Preparing to send quote email...');

      // Validate customer email
      if (!quote.customer_id) {
        return {
          success: false,
          error: 'Quote has no customer associated',
        };
      }

      // Load all related data
      const emailData = await this.loadQuoteEmailData(quote);
      if (!emailData) {
        return {
          success: false,
          error: 'Failed to load quote data for email',
        };
      }

      // Validate customer email address
      if (!emailData.customer.email) {
        return {
          success: false,
          error: 'Customer has no email address',
        };
      }

      // Prepare quote email data
      const quoteNumber = this.generateQuoteNumber(quote);
      const customerName = emailData.customer.full_name || 'Valued Customer';
      const staffName = emailData.staff 
        ? `${emailData.staff.first_name} ${emailData.staff.last_name}`.trim()
        : 'Ocean Soul Sparkles Team';

      const quoteEmailData = {
        quote_number: quoteNumber,
        service_name: quote.service_name || 'Service Quote',
        service_description: quote.service_description || 'Quote for services',
        estimated_total: quote.estimated_total || 0,
        expires_at: quote.expires_at || new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
        event_date: quote.event_date,
        staff_name: staffName,
      };

      // Send email via email service
      const emailResult = await emailService.sendQuoteEmail(
        emailData.customer.email,
        customerName,
        quoteEmailData
      );

      if (emailResult.success) {
        console.log('✅ Quote email sent successfully');
        return {
          success: true,
          emailId: emailResult.email_id,
        };
      } else {
        console.error('❌ Quote email failed:', emailResult.error);
        return {
          success: false,
          error: emailResult.error || 'Failed to send quote email',
        };
      }

    } catch (error) {
      console.error('❌ Quote email service error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error sending quote email',
      };
    }
  }

  /**
   * Send quote reminder email
   */
  async sendQuoteReminderEmail(quote: Quote): Promise<QuoteEmailResult> {
    try {
      console.log('📧 Sending quote reminder email...');

      // Load quote data
      const emailData = await this.loadQuoteEmailData(quote);
      if (!emailData || !emailData.customer.email) {
        return {
          success: false,
          error: 'Cannot send reminder - customer email not available',
        };
      }

      // Check if quote is still valid
      const validUntil = new Date(quote.valid_until || Date.now());
      const now = new Date();
      
      if (validUntil <= now) {
        return {
          success: false,
          error: 'Quote has expired - cannot send reminder',
        };
      }

      // Calculate days until expiry
      const daysUntilExpiry = Math.ceil((validUntil.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));

      const quoteNumber = this.generateQuoteNumber(quote);
      const customerName = emailData.customer.full_name || 'Valued Customer';

      // Use reminder template with additional context
      const reminderEmailData = {
        quote_number: quoteNumber,
        service_name: quote.service_name || 'Service Quote',
        estimated_total: quote.estimated_total || 0,
        expires_at: quote.expires_at || new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
        days_until_expiry: daysUntilExpiry.toString(),
        staff_name: emailData.staff
          ? `${emailData.staff.first_name} ${emailData.staff.last_name}`.trim()
          : 'Ocean Soul Sparkles Team',
      };

      // Send reminder email
      const emailResult = await emailService.sendEmail({
        to: emailData.customer.email,
        to_name: customerName,
        template_type: 'reminder',
        template_variables: {
          customer_name: customerName,
          ...reminderEmailData,
          business_name: 'Ocean Soul Sparkles',
          business_email: '<EMAIL>',
          business_phone: '+61 XXX XXX XXX',
          business_website: 'oceansoulsparkles.com.au',
        },
        priority: 'normal',
        send_immediately: true,
      });

      if (emailResult.success) {
        console.log('✅ Quote reminder email sent successfully');
        return {
          success: true,
          emailId: emailResult.email_id,
        };
      } else {
        return {
          success: false,
          error: emailResult.error || 'Failed to send quote reminder email',
        };
      }

    } catch (error) {
      console.error('❌ Quote reminder email error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error sending quote reminder',
      };
    }
  }

  /**
   * Send booking confirmation email after quote acceptance
   */
  async sendBookingConfirmationFromQuote(quote: Quote, booking: Booking): Promise<QuoteEmailResult> {
    try {
      console.log('📧 Sending booking confirmation from quote...');

      // Load email data
      const emailData = await this.loadQuoteEmailData(quote);
      if (!emailData || !emailData.customer.email) {
        return {
          success: false,
          error: 'Cannot send confirmation - customer email not available',
        };
      }

      const bookingNumber = `OSS-B${new Date().getFullYear()}${(new Date().getMonth() + 1).toString().padStart(2, '0')}${new Date().getDate().toString().padStart(2, '0')}-${booking.id.slice(-6).toUpperCase()}`;
      const customerName = emailData.customer.full_name || 'Valued Customer';

      const bookingEmailData = {
        booking_number: bookingNumber,
        service_name: emailData.service?.name || 'Service',
        booking_date: booking.booking_date,
        start_time: booking.start_time,
        end_time: booking.end_time,
        total_amount: booking.total_amount,
        staff_name: emailData.staff 
          ? `${emailData.staff.first_name} ${emailData.staff.last_name}`.trim()
          : 'Ocean Soul Sparkles Team',
        notes: booking.notes,
      };

      // Send booking confirmation email
      const emailResult = await emailService.sendBookingConfirmationEmail(
        emailData.customer.email,
        customerName,
        bookingEmailData
      );

      if (emailResult.success) {
        console.log('✅ Booking confirmation email sent successfully');
        return {
          success: true,
          emailId: emailResult.email_id,
        };
      } else {
        return {
          success: false,
          error: emailResult.error || 'Failed to send booking confirmation email',
        };
      }

    } catch (error) {
      console.error('❌ Booking confirmation email error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error sending booking confirmation',
      };
    }
  }
}

// Export singleton instance
export const quoteEmailService = QuoteEmailService.getInstance();
