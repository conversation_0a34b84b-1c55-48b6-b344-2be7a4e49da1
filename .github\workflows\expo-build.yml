name: Ocean Soul Sparkles - Expo Build & Deploy

on:
  push:
    branches: [main, production]
    paths: ['oceansoulapp/**']
  pull_request:
    branches: [main]
    paths: ['oceansoulapp/**']

jobs:
  build:
    name: Build and Deploy
    runs-on: ubuntu-latest
    
    # Only run on oceansoulapp changes
    if: contains(github.event.head_commit.modified, 'oceansoulapp/') || contains(github.event.head_commit.added, 'oceansoulapp/')
    
    steps:
      - name: 🏗 Setup repo
        uses: actions/checkout@v4

      - name: 🏗 Setup Node
        uses: actions/setup-node@v4
        with:
          node-version: 18.x
          cache: npm
          cache-dependency-path: oceansoulapp/package-lock.json

      - name: 🏗 Setup EAS
        uses: expo/expo-github-action@v8
        with:
          expo-version: latest
          eas-version: latest
          token: ${{ secrets.EXPO_TOKEN }}

      - name: 📦 Install dependencies
        working-directory: oceansoulapp
        run: npm ci

      - name: 🚀 Build app (Development)
        working-directory: oceansoulapp
        if: github.ref == 'refs/heads/main'
        run: eas build --platform all --profile development --non-interactive

      - name: 🚀 Build app (Production)
        working-directory: oceansoulapp
        if: github.ref == 'refs/heads/production'
        run: eas build --platform all --profile production --non-interactive

      - name: 📱 Submit to stores (Production only)
        working-directory: oceansoulapp
        if: github.ref == 'refs/heads/production'
        run: eas submit --platform all --non-interactive