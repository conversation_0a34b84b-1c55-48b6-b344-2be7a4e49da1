/**
 * Ocean Soul Sparkles Mobile App - Role-Based Navigation
 * Navigation structure based on user roles and permissions
 */

import React from 'react';
import { View, Text } from 'react-native';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
// Removed Ionicons to fix font loading error
import { useAuth } from '@/store/authStore.minimal';
import { AdminUser } from '@/types/database';

// Import screens (we'll create these next)
import DashboardScreen from '@/screens/dashboard/DashboardScreenSimple';
import POSScreen from '@/screens/pos/POSScreen';
import TransactionHistoryScreen from '@/screens/transactions/TransactionHistoryScreen';
import BookingsScreen from '@/screens/bookings/BookingsScreenReal';
import QuotesScreen from '@/screens/quotes/QuotesScreen';
import StaffScreen from '@/screens/staff/StaffScreen';
import ProductsScreen from '@/screens/products/ProductsScreen';
import CustomerManagementScreen from '@/screens/customers/CustomerManagementScreen';
import ProfileScreen from '@/screens/profile/ProfileScreen';

const Tab = createBottomTabNavigator();
const Stack = createNativeStackNavigator();

interface TabConfig {
  name: string;
  component: React.ComponentType<any>;
  icon: string;
  label: string;
  roles: string[];
}

// Define tab configurations based on roles
const TAB_CONFIGS: TabConfig[] = [
  {
    name: 'Dashboard',
    component: DashboardScreen,
    icon: '🏠',
    label: 'Dashboard',
    roles: ['DEV', 'Admin', 'Artist', 'Braider'],
  },
  {
    name: 'POS',
    component: POSScreen,
    icon: '🏪',
    label: 'POS',
    roles: ['DEV', 'Admin', 'Artist', 'Braider'],
  },
  {
    name: 'Transactions',
    component: TransactionHistoryScreen,
    icon: '📊',
    label: 'Transactions',
    roles: ['DEV', 'Admin', 'Artist', 'Braider'],
  },
  {
    name: 'Bookings',
    component: BookingsScreen,
    icon: '📅',
    label: 'Bookings',
    roles: ['DEV', 'Admin', 'Artist', 'Braider'],
  },
  {
    name: 'Quotes',
    component: QuotesScreen,
    icon: '📋',
    label: 'Quotes',
    roles: ['DEV', 'Admin', 'Artist'],
  },
  {
    name: 'Staff',
    component: StaffScreen,
    icon: '👥',
    label: 'Staff',
    roles: ['DEV', 'Admin'],
  },
  {
    name: 'Products',
    component: ProductsScreen,
    icon: '📦',
    label: 'Products',
    roles: ['DEV', 'Admin'],
  },
  {
    name: 'Customers',
    component: CustomerManagementScreen,
    icon: '👤',
    label: 'Customers',
    roles: ['DEV', 'Admin', 'Artist'],
  },
  {
    name: 'Profile',
    component: ProfileScreen,
    icon: '⚙️',
    label: 'Profile',
    roles: ['DEV', 'Admin', 'Artist', 'Braider'],
  },
];

const RoleBasedNavigation: React.FC = () => {
  const { user, hasRole } = useAuth();

  if (!user) {
    return null; // This should not happen as this component is only rendered when authenticated
  }

  // Filter tabs based on user role
  const allowedTabs = TAB_CONFIGS.filter(tab => 
    hasRole(tab.roles)
  );

  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        tabBarIcon: ({ focused, color, size }) => {
          const tabConfig = allowedTabs.find(tab => tab.name === route.name);
          const iconEmoji = tabConfig?.icon || '❓';

          return (
            <Text style={{
              fontSize: size,
              opacity: focused ? 1 : 0.6
            }}>
              {iconEmoji}
            </Text>
          );
        },
        tabBarActiveTintColor: '#FF9A8B',
        tabBarInactiveTintColor: '#666',
        tabBarStyle: {
          backgroundColor: '#fff',
          borderTopWidth: 1,
          borderTopColor: '#e0e0e0',
          paddingBottom: 5,
          paddingTop: 5,
          height: 60,
        },
        tabBarLabelStyle: {
          fontSize: 12,
          fontWeight: '500',
        },
        headerStyle: {
          backgroundColor: '#FF9A8B',
        },
        headerTintColor: '#fff',
        headerTitleStyle: {
          fontWeight: 'bold',
        },
        headerRight: () => (
          <RoleIndicator user={user} />
        ),
      })}
    >
      {allowedTabs.map((tab) => (
        <Tab.Screen
          key={tab.name}
          name={tab.name}
          component={tab.component}
          options={{
            tabBarLabel: tab.label,
            title: `${tab.label} - Ocean Soul Sparkles`,
          }}
        />
      ))}
    </Tab.Navigator>
  );
};

// Role indicator component for header
const RoleIndicator: React.FC<{ user: AdminUser }> = ({ user }) => {
  const getRoleColor = (role: string) => {
    switch (role) {
      case 'DEV':
        return '#ff4444';
      case 'Admin':
        return '#4444ff';
      case 'Artist':
        return '#44ff44';
      case 'Braider':
        return '#ffaa44';
      default:
        return '#666';
    }
  };

  const getRoleLabel = (role: string) => {
    switch (role) {
      case 'DEV':
        return 'DEV';
      case 'Admin':
        return 'ADMIN';
      case 'Artist':
        return 'ARTIST';
      case 'Braider':
        return 'BRAIDER';
      default:
        return role.toUpperCase();
    }
  };

  return (
    <View
      style={{
        backgroundColor: getRoleColor(user.role),
        paddingHorizontal: 8,
        paddingVertical: 4,
        borderRadius: 12,
        marginRight: 16,
      }}
    >
      <Text
        style={{
          color: '#fff',
          fontSize: 10,
          fontWeight: 'bold',
        }}
      >
        {getRoleLabel(user.role)}
      </Text>
    </View>
  );
};

// Stack navigator for modal screens and detailed views
export const AppStackNavigator: React.FC = () => {
  return (
    <Stack.Navigator
      screenOptions={{
        headerStyle: {
          backgroundColor: '#FF9A8B',
        },
        headerTintColor: '#fff',
        headerTitleStyle: {
          fontWeight: 'bold',
        },
      }}
    >
      <Stack.Screen 
        name="MainTabs" 
        component={RoleBasedNavigation}
        options={{ headerShown: false }}
      />
      {/* Add modal screens here as needed */}
    </Stack.Navigator>
  );
};

export default RoleBasedNavigation;
