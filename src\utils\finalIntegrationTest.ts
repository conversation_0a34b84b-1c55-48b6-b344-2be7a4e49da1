/**
 * Ocean Soul Sparkles Mobile App - Final Integration Test
 * Validates that all systems work together seamlessly
 */

import { integrationValidator } from './integrationValidation';
import { adminPortalIntegrationSuite } from '@/services/validation/adminPortalIntegrationSuite';
import { databaseIntegrationSuite } from '@/services/validation/databaseIntegrationSuite';
import { emailTesting } from './emailTesting';
import { workflowTesting } from './workflowTesting';

export interface FinalIntegrationResult {
  overall: 'pass' | 'fail' | 'warning';
  systems: {
    enhancedWorkflow: boolean;
    emailNotifications: boolean;
    adminPortalIntegration: boolean;
    databaseValidation: boolean;
  };
  summary: {
    totalSystems: number;
    workingSystems: number;
    successRate: number;
  };
  recommendations: string[];
}

export class FinalIntegrationTester {
  /**
   * Test all systems integration
   */
  static async testCompleteSystemIntegration(): Promise<FinalIntegrationResult> {
    console.log('🧪 Running final integration test for all systems...');

    try {
      // Test Enhanced Booking-to-Quote Workflow
      const workflowValidation = await integrationValidator.validateWorkflowIntegration();
      const enhancedWorkflow = workflowValidation.overall === 'pass';

      // Test Email Notification System
      const emailStatus = await emailTesting.getEmailSystemStatus();
      const emailNotifications = emailStatus.overall;

      // Test Admin Portal Integration
      const adminPortalReport = await adminPortalIntegrationSuite.runCompleteIntegrationValidation();
      const adminPortalIntegration = adminPortalReport.overall === 'pass';

      // Test Database Integration & SQL Functions
      const databaseReport = await databaseIntegrationSuite.runCompleteDatabaseValidation();
      const databaseValidation = databaseReport.overall === 'pass';

      const systems = {
        enhancedWorkflow,
        emailNotifications,
        adminPortalIntegration,
        databaseValidation,
      };

      const workingSystems = Object.values(systems).filter(Boolean).length;
      const totalSystems = Object.keys(systems).length;
      const successRate = (workingSystems / totalSystems) * 100;

      const overall: 'pass' | 'fail' | 'warning' = 
        successRate >= 100 ? 'pass' :
        successRate >= 75 ? 'warning' : 'fail';

      const recommendations = this.generateRecommendations(systems, overall);

      console.log(`✅ Final integration test completed: ${workingSystems}/${totalSystems} systems working (${successRate}%)`);

      return {
        overall,
        systems,
        summary: {
          totalSystems,
          workingSystems,
          successRate: Math.round(successRate * 100) / 100,
        },
        recommendations,
      };

    } catch (error) {
      console.error('❌ Final integration test failed:', error);
      return {
        overall: 'fail',
        systems: {
          enhancedWorkflow: false,
          emailNotifications: false,
          adminPortalIntegration: false,
          databaseValidation: false,
        },
        summary: {
          totalSystems: 4,
          workingSystems: 0,
          successRate: 0,
        },
        recommendations: ['Fix critical integration test error before proceeding'],
      };
    }
  }

  /**
   * Generate recommendations based on test results
   */
  private static generateRecommendations(
    systems: FinalIntegrationResult['systems'],
    overall: 'pass' | 'fail' | 'warning'
  ): string[] {
    const recommendations: string[] = [];

    if (!systems.enhancedWorkflow) {
      recommendations.push('Fix Enhanced Booking-to-Quote Workflow integration issues');
    }

    if (!systems.emailNotifications) {
      recommendations.push('Resolve email notification system problems');
    }

    if (!systems.adminPortalIntegration) {
      recommendations.push('Address admin portal integration failures');
    }

    if (!systems.databaseValidation) {
      recommendations.push('Implement and validate database integration');
    }

    if (overall === 'pass') {
      recommendations.push('✅ All systems integrated successfully - ready for production');
    } else if (overall === 'warning') {
      recommendations.push('⚠️ Most systems working - address remaining issues before production');
    } else {
      recommendations.push('❌ Critical integration issues - fix before production deployment');
    }

    return recommendations;
  }
}

// Export for easy access
export const finalIntegrationTester = FinalIntegrationTester;
