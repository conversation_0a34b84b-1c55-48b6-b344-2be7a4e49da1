/**
 * Ocean Soul Sparkles Mobile App - Database Fixes Verification
 * Verifies that all database schema fixes are working correctly
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

const SUPABASE_URL = process.env.EXPO_PUBLIC_SUPABASE_URL;
const SUPABASE_ANON_KEY = process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY;

console.log('🧪 Verifying Database Schema Fixes...');

if (!SUPABASE_URL || !SUPABASE_ANON_KEY) {
  console.error('❌ Missing Supabase configuration');
  process.exit(1);
}

const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

async function testQuotePropertyAccess() {
  console.log('\n💰 Testing Quote Property Access...');
  
  try {
    // Test accessing quotes with the correct properties
    const { data: quotes, error } = await supabase
      .from('quotes')
      .select('id, estimated_total, finalized_total, service_name, customer_first_name, customer_last_name')
      .limit(1);

    if (error) {
      console.error('❌ Quote property access failed:', error.message);
      return false;
    }

    if (quotes && quotes.length > 0) {
      const quote = quotes[0];
      console.log('✅ Quote properties accessible:');
      console.log(`  - estimated_total: ${quote.estimated_total}`);
      console.log(`  - finalized_total: ${quote.finalized_total}`);
      console.log(`  - service_name: ${quote.service_name}`);
      console.log(`  - customer_first_name: ${quote.customer_first_name}`);
      console.log(`  - customer_last_name: ${quote.customer_last_name}`);
      
      // Test that estimated_total is accessible (this was causing the runtime error)
      if (typeof quote.estimated_total === 'number') {
        console.log('✅ estimated_total property is correctly accessible as number');
        return true;
      } else {
        console.log('❌ estimated_total property is not a number:', typeof quote.estimated_total);
        return false;
      }
    } else {
      console.log('ℹ️ No quotes in database to test with');
      return true; // Not an error, just no data
    }

  } catch (error) {
    console.error('❌ Quote property test failed:', error.message);
    return false;
  }
}

async function testCustomerPropertyAccess() {
  console.log('\n👥 Testing Customer Property Access...');
  
  try {
    // Test accessing customers with the correct properties
    const { data: customers, error } = await supabase
      .from('customers')
      .select('id, name, email, phone, address')
      .limit(1);

    if (error) {
      console.error('❌ Customer property access failed:', error.message);
      return false;
    }

    if (customers && customers.length > 0) {
      const customer = customers[0];
      console.log('✅ Customer properties accessible:');
      console.log(`  - name: ${customer.name}`);
      console.log(`  - email: ${customer.email}`);
      console.log(`  - phone: ${customer.phone}`);
      console.log(`  - address: ${customer.address}`);
      
      return true;
    } else {
      console.log('ℹ️ No customers in database to test with');
      return true; // Not an error, just no data
    }

  } catch (error) {
    console.error('❌ Customer property test failed:', error.message);
    return false;
  }
}

async function testEmailTemplateVariables() {
  console.log('\n📧 Testing Email Template Variables...');
  
  try {
    // Import the email templates to test
    const emailTemplates = require('../src/services/email/emailTemplates');
    
    // Test quote template
    const quoteTemplate = emailTemplates.getFallbackTemplate('quote');
    if (!quoteTemplate) {
      console.error('❌ Quote template not found');
      return false;
    }

    // Check that template uses estimated_total instead of total_amount
    if (quoteTemplate.html_content.includes('{{estimated_total}}')) {
      console.log('✅ Quote template uses estimated_total');
    } else {
      console.log('❌ Quote template does not use estimated_total');
      return false;
    }

    // Check that template doesn't use old property names
    if (quoteTemplate.html_content.includes('{{total_amount}}') || 
        quoteTemplate.html_content.includes('{{quote_amount}}')) {
      console.log('❌ Quote template still uses old property names');
      return false;
    }

    // Check variables array
    if (quoteTemplate.variables.includes('estimated_total')) {
      console.log('✅ Quote template variables include estimated_total');
    } else {
      console.log('❌ Quote template variables missing estimated_total');
      return false;
    }

    console.log('✅ Email template variables are correct');
    return true;

  } catch (error) {
    console.error('❌ Email template test failed:', error.message);
    return false;
  }
}

async function testAppRegistration() {
  console.log('\n📱 Testing App Registration Setup...');
  
  try {
    // Check if App component can be imported
    const App = require('../App');
    if (!App || !App.default) {
      console.error('❌ App component not properly exported');
      return false;
    }
    console.log('✅ App component properly exported');

    // Check if expo registerRootComponent is available
    try {
      const { registerRootComponent } = require('expo');
      if (typeof registerRootComponent === 'function') {
        console.log('✅ registerRootComponent available');
      } else {
        console.log('❌ registerRootComponent not a function');
        return false;
      }
    } catch (err) {
      console.log('❌ Expo registerRootComponent not available:', err.message);
      return false;
    }

    // Check index.js exists and has correct content
    const fs = require('fs');
    const path = require('path');
    
    const indexPath = path.join(process.cwd(), 'index.js');
    if (!fs.existsSync(indexPath)) {
      console.error('❌ index.js does not exist');
      return false;
    }

    const indexContent = fs.readFileSync(indexPath, 'utf8');
    if (indexContent.includes('registerRootComponent(App)')) {
      console.log('✅ index.js properly registers App component');
    } else {
      console.log('❌ index.js does not register App component');
      return false;
    }

    // Check app.json has main entry point
    const appJsonPath = path.join(process.cwd(), 'app.json');
    if (fs.existsSync(appJsonPath)) {
      const appJsonContent = fs.readFileSync(appJsonPath, 'utf8');
      const appConfig = JSON.parse(appJsonContent);
      
      if (appConfig.expo && appConfig.expo.main === 'index.js') {
        console.log('✅ app.json specifies correct main entry point');
      } else {
        console.log('❌ app.json does not specify index.js as main entry point');
        return false;
      }
    }

    console.log('✅ App registration setup is correct');
    return true;

  } catch (error) {
    console.error('❌ App registration test failed:', error.message);
    return false;
  }
}

async function runAllTests() {
  console.log('🚨 Ocean Soul Sparkles - Database Fixes Verification');
  console.log('='.repeat(60));
  
  const tests = [
    { name: 'Quote Property Access', test: testQuotePropertyAccess },
    { name: 'Customer Property Access', test: testCustomerPropertyAccess },
    { name: 'Email Template Variables', test: testEmailTemplateVariables },
    { name: 'App Registration Setup', test: testAppRegistration },
  ];

  const results = [];
  
  for (const { name, test } of tests) {
    try {
      const result = await test();
      results.push({ name, passed: result });
    } catch (error) {
      console.error(`❌ ${name} test crashed:`, error.message);
      results.push({ name, passed: false });
    }
  }

  console.log('\n📊 TEST RESULTS SUMMARY:');
  console.log('='.repeat(40));
  
  let allPassed = true;
  results.forEach(({ name, passed }) => {
    const status = passed ? '✅ PASS' : '❌ FAIL';
    console.log(`${status} - ${name}`);
    if (!passed) allPassed = false;
  });

  const passedCount = results.filter(r => r.passed).length;
  const totalCount = results.length;

  console.log(`\n📈 Overall: ${passedCount}/${totalCount} tests passed`);

  if (allPassed) {
    console.log('\n🎉 ALL TESTS PASSED!');
    console.log('✅ Database schema fixes are working correctly');
    console.log('✅ App registration is properly configured');
    console.log('✅ The app should now build and run without runtime errors');
  } else {
    console.log('\n❌ SOME TESTS FAILED!');
    console.log('🚨 Runtime errors may still occur');
    console.log('🔧 Please review and fix the failing tests before deployment');
  }

  return allPassed;
}

// Run all tests
runAllTests()
  .then(success => {
    process.exit(success ? 0 : 1);
  })
  .catch(error => {
    console.error('❌ Test suite failed:', error);
    process.exit(1);
  });
