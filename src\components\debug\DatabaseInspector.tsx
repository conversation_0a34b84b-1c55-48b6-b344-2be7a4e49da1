/**
 * Ocean Soul Sparkles - Database Inspector Component
 * Debug component to inspect database structure
 */

import React, { useState, useRef, useEffect } from 'react';
import { View, Text, TouchableOpacity, ScrollView, StyleSheet } from 'react-native';
import { DatabaseInspector } from '@/utils/databaseInspector';

const DatabaseInspectorComponent: React.FC = () => {
  const [inspection, setInspection] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [bestTable, setBestTable] = useState<string | null>(null);
  const isMountedRef = useRef(true);

  useEffect(() => {
    isMountedRef.current = true;
    return () => {
      isMountedRef.current = false;
    };
  }, []);

  const runInspection = async () => {
    if (!isMountedRef.current) return;

    setLoading(true);
    try {
      const result = await DatabaseInspector.inspectDatabase();

      if (isMountedRef.current) {
        setInspection(result);
      }

      const best = await DatabaseInspector.findBestTransactionTable();

      if (isMountedRef.current) {
        setBestTable(best);
      }
    } catch (error) {
      // Handle error silently for customer-facing app
    } finally {
      if (isMountedRef.current) {
        setLoading(false);
      }
    }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>🔍 Database Inspector</Text>
      
      <TouchableOpacity 
        style={styles.button} 
        onPress={runInspection}
        disabled={loading}
      >
        <Text style={styles.buttonText}>
          {loading ? 'Inspecting...' : 'Inspect Database'}
        </Text>
      </TouchableOpacity>

      {bestTable && (
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>🎯 Best Transaction Table:</Text>
          <Text style={styles.tableText}>{bestTable}</Text>
        </View>
      )}

      {inspection && (
        <ScrollView style={styles.results}>
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>📊 All Tables ({inspection.allTables?.length || 0}):</Text>
            {inspection.allTables?.map((table: string) => (
              <Text key={table} style={styles.tableText}>• {table}</Text>
            ))}
          </View>

          <View style={styles.section}>
            <Text style={styles.sectionTitle}>💳 Transaction-Related Tables:</Text>
            {inspection.transactionTables?.length > 0 ? (
              inspection.transactionTables.map((table: string) => (
                <Text key={table} style={styles.tableText}>• {table}</Text>
              ))
            ) : (
              <Text style={styles.noData}>No transaction tables found</Text>
            )}
          </View>

          <View style={styles.section}>
            <Text style={styles.sectionTitle}>✅ Accessible Tables:</Text>
            {inspection.accessibleTables?.map((table: string) => (
              <Text key={table} style={styles.tableText}>• {table}</Text>
            ))}
          </View>

          <View style={styles.section}>
            <Text style={styles.sectionTitle}>📋 Table Structures:</Text>
            {Object.entries(inspection.tableStructures || {}).map(([table, columns]) => (
              <View key={table} style={styles.tableStructure}>
                <Text style={styles.tableName}>{table}:</Text>
                {(columns as string[]).map((column: string) => (
                  <Text key={column} style={styles.columnText}>  - {column}</Text>
                ))}
              </View>
            ))}
          </View>
        </ScrollView>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    backgroundColor: '#f8f9fa',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 16,
    textAlign: 'center',
  },
  button: {
    backgroundColor: '#FF9A8B',
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
    marginBottom: 16,
  },
  buttonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  results: {
    flex: 1,
  },
  section: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
  },
  tableText: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
    fontFamily: 'monospace',
  },
  noData: {
    fontSize: 14,
    color: '#999',
    fontStyle: 'italic',
  },
  tableStructure: {
    marginBottom: 12,
  },
  tableName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FF9A8B',
    marginBottom: 4,
  },
  columnText: {
    fontSize: 12,
    color: '#666',
    fontFamily: 'monospace',
  },
});

export default DatabaseInspectorComponent;
