/**
 * Ocean Soul Sparkles Mobile App - Android Notification Channels
 * Manages Android-specific notification channels for proper notification display
 */

import * as Notifications from 'expo-notifications';
import { Platform } from 'react-native';

export interface NotificationChannel {
  id: string;
  name: string;
  description: string;
  importance: Notifications.AndroidImportance;
  sound?: string;
  vibrationPattern?: number[];
  enableLights?: boolean;
  lightColor?: string;
  enableVibration?: boolean;
  showBadge?: boolean;
}

export const NOTIFICATION_CHANNELS = {
  // High priority channels
  URGENT_BOOKINGS: {
    id: 'urgent_bookings',
    name: 'Urgent Bookings',
    description: 'Critical booking notifications that require immediate attention',
    importance: Notifications.AndroidImportance.HIGH,
    sound: 'default',
    vibrationPattern: [0, 250, 250, 250],
    enableLights: true,
    lightColor: '#FF0000',
    enableVibration: true,
    showBadge: true,
  },
  
  STAFF_MESSAGES: {
    id: 'staff_messages',
    name: 'Staff Messages',
    description: 'Messages from other staff members and coordination updates',
    importance: Notifications.AndroidImportance.HIGH,
    sound: 'default',
    vibrationPattern: [0, 200, 100, 200],
    enableLights: true,
    lightColor: '#FF9A8B',
    enableVibration: true,
    showBadge: true,
  },

  // Medium priority channels
  NEW_BOOKINGS: {
    id: 'new_bookings',
    name: 'New Bookings',
    description: 'Notifications for new customer bookings and appointments',
    importance: Notifications.AndroidImportance.DEFAULT,
    sound: 'default',
    vibrationPattern: [0, 150, 100, 150],
    enableLights: true,
    lightColor: '#4CAF50',
    enableVibration: true,
    showBadge: true,
  },

  BOOKING_UPDATES: {
    id: 'booking_updates',
    name: 'Booking Updates',
    description: 'Updates to existing bookings and appointment changes',
    importance: Notifications.AndroidImportance.DEFAULT,
    sound: 'default',
    vibrationPattern: [0, 100, 50, 100],
    enableLights: true,
    lightColor: '#2196F3',
    enableVibration: true,
    showBadge: true,
  },

  AVAILABILITY_REQUESTS: {
    id: 'availability_requests',
    name: 'Availability Requests',
    description: 'Requests for staff availability and scheduling updates',
    importance: Notifications.AndroidImportance.DEFAULT,
    sound: 'default',
    vibrationPattern: [0, 100],
    enableLights: true,
    lightColor: '#FF9800',
    enableVibration: true,
    showBadge: true,
  },

  // Low priority channels
  GENERAL_UPDATES: {
    id: 'general_updates',
    name: 'General Updates',
    description: 'General app updates and non-urgent notifications',
    importance: Notifications.AndroidImportance.LOW,
    sound: 'default',
    enableLights: false,
    enableVibration: false,
    showBadge: false,
  },

  SYSTEM_NOTIFICATIONS: {
    id: 'system_notifications',
    name: 'System Notifications',
    description: 'System maintenance and app status notifications',
    importance: Notifications.AndroidImportance.LOW,
    sound: 'default',
    enableLights: false,
    enableVibration: false,
    showBadge: false,
  },
} as const;

class AndroidNotificationChannelManager {
  private isInitialized = false;
  private createdChannels: Set<string> = new Set();

  /**
   * Initialize all notification channels for Android
   */
  async initializeChannels(): Promise<void> {
    if (Platform.OS !== 'android') {
      console.log('📱 Skipping Android notification channels on non-Android platform');
      return;
    }

    if (this.isInitialized) {
      console.log('📱 Android notification channels already initialized');
      return;
    }

    try {
      console.log('📱 Initializing Android notification channels...');

      // Create all defined channels
      const channelPromises = Object.values(NOTIFICATION_CHANNELS).map(channel =>
        this.createChannel(channel)
      );

      await Promise.all(channelPromises);

      this.isInitialized = true;
      console.log(`✅ Created ${this.createdChannels.size} Android notification channels`);

    } catch (error) {
      console.error('❌ Failed to initialize Android notification channels:', error);
      throw error;
    }
  }

  /**
   * Create a single notification channel
   */
  private async createChannel(channelConfig: NotificationChannel): Promise<void> {
    try {
      await Notifications.setNotificationChannelAsync(channelConfig.id, {
        name: channelConfig.name,
        description: channelConfig.description,
        importance: channelConfig.importance,
        sound: channelConfig.sound,
        vibrationPattern: channelConfig.vibrationPattern,
        enableLights: channelConfig.enableLights,
        lightColor: channelConfig.lightColor,
        enableVibration: channelConfig.enableVibration,
        showBadge: channelConfig.showBadge,
      });

      this.createdChannels.add(channelConfig.id);
      console.log(`📱 Created Android notification channel: ${channelConfig.id}`);

    } catch (error) {
      console.error(`❌ Failed to create notification channel ${channelConfig.id}:`, error);
      throw error;
    }
  }

  /**
   * Get the appropriate channel ID for a notification type
   */
  getChannelForNotificationType(notificationType: string): string {
    switch (notificationType) {
      case 'urgent_booking':
      case 'emergency':
        return NOTIFICATION_CHANNELS.URGENT_BOOKINGS.id;
      
      case 'staff_message':
      case 'chat_message':
        return NOTIFICATION_CHANNELS.STAFF_MESSAGES.id;
      
      case 'new_booking':
        return NOTIFICATION_CHANNELS.NEW_BOOKINGS.id;
      
      case 'booking_update':
      case 'booking_change':
        return NOTIFICATION_CHANNELS.BOOKING_UPDATES.id;
      
      case 'availability_request':
      case 'schedule_request':
        return NOTIFICATION_CHANNELS.AVAILABILITY_REQUESTS.id;
      
      case 'system_notification':
      case 'maintenance':
        return NOTIFICATION_CHANNELS.SYSTEM_NOTIFICATIONS.id;
      
      case 'general_update':
      default:
        return NOTIFICATION_CHANNELS.GENERAL_UPDATES.id;
    }
  }

  /**
   * Create a custom notification channel
   */
  async createCustomChannel(channelConfig: NotificationChannel): Promise<void> {
    if (Platform.OS !== 'android') {
      return;
    }

    await this.createChannel(channelConfig);
  }

  /**
   * Delete a notification channel
   */
  async deleteChannel(channelId: string): Promise<void> {
    if (Platform.OS !== 'android') {
      return;
    }

    try {
      await Notifications.deleteNotificationChannelAsync(channelId);
      this.createdChannels.delete(channelId);
      console.log(`📱 Deleted Android notification channel: ${channelId}`);
    } catch (error) {
      console.error(`❌ Failed to delete notification channel ${channelId}:`, error);
    }
  }

  /**
   * Get all created channels
   */
  getCreatedChannels(): string[] {
    return Array.from(this.createdChannels);
  }

  /**
   * Check if channels are initialized
   */
  isChannelsInitialized(): boolean {
    return this.isInitialized;
  }

  /**
   * Reset initialization state (for testing)
   */
  reset(): void {
    this.isInitialized = false;
    this.createdChannels.clear();
  }

  /**
   * Get channel statistics
   */
  getChannelStats(): {
    totalChannels: number;
    channelsByImportance: Record<string, number>;
    createdChannels: string[];
  } {
    const channelsByImportance: Record<string, number> = {};
    
    Object.values(NOTIFICATION_CHANNELS).forEach(channel => {
      const importance = channel.importance.toString();
      channelsByImportance[importance] = (channelsByImportance[importance] || 0) + 1;
    });

    return {
      totalChannels: Object.keys(NOTIFICATION_CHANNELS).length,
      channelsByImportance,
      createdChannels: this.getCreatedChannels(),
    };
  }
}

// Export singleton instance
export const androidNotificationChannelManager = new AndroidNotificationChannelManager();
