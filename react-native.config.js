/**
 * Ocean Soul Sparkles Mobile App - React Native CLI Configuration
 * Enables Metro bundler for Android Studio development
 */

module.exports = {
  // Project configuration
  project: {
    android: {
      // Enable Android Studio development
      sourceDir: 'android',
      appName: 'app',
      packageName: 'com.oceansoulsparkles.app',
    },
    // Skip iOS configuration for now (use Expo for iOS)
  },
  
  // Assets configuration
  assets: ['./src/assets/fonts/', './src/assets/images/'],
  
  // Dependencies configuration
  dependencies: {
    // Expo modules compatibility
    'expo': {
      platforms: {
        android: {
          sourceDir: '../node_modules/expo/android',
          packageImportPath: 'import expo.modules.ExpoModulesPackage;',
        },
      },
    },
    
    // React Native async storage
    '@react-native-async-storage/async-storage': {
      platforms: {
        android: {
          sourceDir: '../node_modules/@react-native-async-storage/async-storage/android',
          packageImportPath: 'import com.reactnativecommunity.asyncstorage.AsyncStoragePackage;',
        },
      },
    },
    
    // React Navigation dependencies
    'react-native-screens': {
      platforms: {
        android: {
          sourceDir: '../node_modules/react-native-screens/android',
          packageImportPath: 'import com.swmansion.rnscreens.RNScreensPackage;',
        },
      },
    },
    
    'react-native-safe-area-context': {
      platforms: {
        android: {
          sourceDir: '../node_modules/react-native-safe-area-context/android',
          packageImportPath: 'import com.th3rdwave.safeareacontext.SafeAreaContextPackage;',
        },
      },
    },
    
    'react-native-gesture-handler': {
      platforms: {
        android: {
          sourceDir: '../node_modules/react-native-gesture-handler/android',
          packageImportPath: 'import com.swmansion.gesturehandler.react.RNGestureHandlerPackage;',
        },
      },
    },
    
    'react-native-reanimated': {
      platforms: {
        android: {
          sourceDir: '../node_modules/react-native-reanimated/android',
          packageImportPath: 'import com.swmansion.reanimated.ReanimatedPackage;',
        },
      },
    },
  },
  
  // Commands configuration
  commands: [
    {
      name: 'metro-start',
      description: 'Start Metro bundler for Android Studio development',
      func: () => {
        require('child_process').spawn('npx', ['react-native', 'start'], {
          stdio: 'inherit',
        });
      },
    },
  ],
};
