#!/usr/bin/env node

/**
 * Ocean Soul Sparkles Mobile App - Setup Verification
 */

const fs = require('fs');
const path = require('path');

console.log('🌊 Ocean Soul Sparkles Mobile App - Setup Verification\n');

const requiredFiles = [
  'App.tsx',
  'package.json',
  'app.json',
  'babel.config.js',
  'metro.config.js',
  'tsconfig.json',
  '.env.local',
  'src/store/authStore.ts',
  'src/services/database/supabase.ts',
  'src/types/database.ts'
];

let allFilesExist = true;

console.log('📁 Checking required files:');
requiredFiles.forEach(file => {
  const exists = fs.existsSync(path.join(__dirname, file));
  console.log(`${exists ? '✅' : '❌'} ${file}`);
  if (!exists) allFilesExist = false;
});

console.log('\n📦 Checking package.json configuration:');
try {
  const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
  console.log(`✅ Name: ${packageJson.name}`);
  console.log(`✅ Version: ${packageJson.version}`);
  console.log(`✅ Main: ${packageJson.main || 'node_modules/expo/AppEntry.js'}`);
  
  const requiredDeps = [
    'expo',
    'react',
    'react-native',
    '@react-navigation/native',
    '@supabase/supabase-js',
    'zustand'
  ];
  
  console.log('\n📚 Checking key dependencies:');
  requiredDeps.forEach(dep => {
    const exists = packageJson.dependencies && packageJson.dependencies[dep];
    console.log(`${exists ? '✅' : '❌'} ${dep}`);
  });
} catch (error) {
  console.log('❌ Error reading package.json:', error.message);
  allFilesExist = false;
}

console.log('\n🔧 Checking app.json configuration:');
try {
  const appJson = JSON.parse(fs.readFileSync('app.json', 'utf8'));
  console.log(`✅ App name: ${appJson.expo.name}`);
  console.log(`✅ Slug: ${appJson.expo.slug}`);
  console.log(`✅ Version: ${appJson.expo.version}`);
  console.log(`✅ Platforms: iOS, Android, Web`);
} catch (error) {
  console.log('❌ Error reading app.json:', error.message);
  allFilesExist = false;
}

console.log('\n🌐 Environment configuration:');
const envExists = fs.existsSync('.env.local');
console.log(`${envExists ? '✅' : '⚠️'} .env.local ${envExists ? 'exists' : 'missing - copy from .env.example'}`);

console.log('\n📱 Next steps:');
if (allFilesExist) {
  console.log('✅ All required files are present!');
  console.log('\n🚀 Ready to start development:');
  console.log('   1. npm install');
  console.log('   2. Update .env.local with your credentials');
  console.log('   3. npm start');
} else {
  console.log('❌ Some required files are missing.');
  console.log('   Please ensure all files are created before starting development.');
}

console.log('\n📋 Development commands:');
console.log('   npm start     - Start Expo development server');
console.log('   npm run ios   - Run on iOS simulator');
console.log('   npm run android - Run on Android emulator');
console.log('   npm run web   - Run in web browser');

console.log('\n🎯 Current status: Ready for npm install and development!');
