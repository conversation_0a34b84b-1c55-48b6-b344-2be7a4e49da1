/**
 * Ocean Soul Sparkles Mobile App - Email Status Indicator
 * Shows the current status of the email system
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { emailTesting, EmailSystemStatus } from '@/utils/emailTesting';

interface EmailStatusIndicatorProps {
  showDetails?: boolean;
  onStatusChange?: (status: EmailSystemStatus) => void;
}

const EmailStatusIndicator: React.FC<EmailStatusIndicatorProps> = ({
  showDetails = false,
  onStatusChange,
}) => {
  const [status, setStatus] = useState<EmailSystemStatus | null>(null);
  const [loading, setLoading] = useState(false);
  const [lastChecked, setLastChecked] = useState<Date | null>(null);

  useEffect(() => {
    checkEmailStatus();
  }, []);

  const checkEmailStatus = async () => {
    try {
      setLoading(true);
      const emailStatus = await emailTesting.getEmailSystemStatus();
      setStatus(emailStatus);
      setLastChecked(new Date());
      
      if (onStatusChange) {
        onStatusChange(emailStatus);
      }
    } catch (error) {
      console.error('❌ Failed to check email status:', error);
      setStatus({
        emailService: false,
        authentication: false,
        templates: false,
        adminPortalConnection: false,
        overall: false,
      });
    } finally {
      setLoading(false);
    }
  };

  const runEmailTest = async () => {
    try {
      setLoading(true);
      const testResults = await emailTesting.runComprehensiveEmailTest();
      
      Alert.alert(
        'Email System Test Results',
        `Tests Completed: ${testResults.summary.totalTests}\n` +
        `Passed: ${testResults.summary.passedTests}\n` +
        `Failed: ${testResults.summary.failedTests}\n` +
        `Success Rate: ${testResults.summary.successRate}%\n\n` +
        `Overall Status: ${testResults.status.overall ? '✅ Working' : '❌ Issues Detected'}`,
        [
          {
            text: 'View Details',
            onPress: () => showDetailedResults(testResults),
          },
          { text: 'OK' },
        ]
      );
      
      setStatus(testResults.status);
      setLastChecked(new Date());
      
      if (onStatusChange) {
        onStatusChange(testResults.status);
      }
    } catch (error) {
      console.error('❌ Email test failed:', error);
      Alert.alert('Error', 'Failed to run email system test');
    } finally {
      setLoading(false);
    }
  };

  const showDetailedResults = (testResults: any) => {
    const allTests = [...testResults.connectivity, ...testResults.functionality];
    const failedTests = allTests.filter(t => !t.success);
    
    let message = `Email System Status:\n\n`;
    message += `Service: ${testResults.status.emailService ? '✅' : '❌'}\n`;
    message += `Authentication: ${testResults.status.authentication ? '✅' : '❌'}\n`;
    message += `Templates: ${testResults.status.templates ? '✅' : '❌'}\n`;
    message += `Admin Portal: ${testResults.status.adminPortalConnection ? '✅' : '❌'}\n\n`;
    
    if (failedTests.length > 0) {
      message += `Failed Tests:\n`;
      failedTests.forEach(test => {
        message += `• ${test.test}: ${test.message}\n`;
      });
    }
    
    Alert.alert('Detailed Test Results', message);
  };

  const getStatusColor = () => {
    if (!status) return '#6b7280';
    if (status.overall) return '#10b981';
    if (status.emailService && status.templates) return '#f59e0b';
    return '#ef4444';
  };

  const getStatusText = () => {
    if (loading) return 'Checking...';
    if (!status) return 'Unknown';
    if (status.overall) return 'Email System Ready';
    if (status.emailService && status.templates) return 'Email System Partial';
    return 'Email System Issues';
  };

  const getStatusIcon = () => {
    if (loading) return '⏳';
    if (!status) return '❓';
    if (status.overall) return '✅';
    if (status.emailService && status.templates) return '⚠️';
    return '❌';
  };

  if (!showDetails) {
    return (
      <View style={[styles.simpleIndicator, { backgroundColor: getStatusColor() }]}>
        <Text style={styles.simpleText}>
          {getStatusIcon()} Email
        </Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.statusRow}>
        <View style={[styles.statusDot, { backgroundColor: getStatusColor() }]} />
        <Text style={styles.statusText}>{getStatusText()}</Text>
        {loading && <ActivityIndicator size="small" color={getStatusColor()} />}
      </View>

      {status && (
        <View style={styles.detailsContainer}>
          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Service:</Text>
            <Text style={[styles.detailValue, { color: status.emailService ? '#10b981' : '#ef4444' }]}>
              {status.emailService ? '✅ Ready' : '❌ Not Ready'}
            </Text>
          </View>
          
          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Authentication:</Text>
            <Text style={[styles.detailValue, { color: status.authentication ? '#10b981' : '#ef4444' }]}>
              {status.authentication ? '✅ Authenticated' : '❌ Not Authenticated'}
            </Text>
          </View>
          
          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Templates:</Text>
            <Text style={[styles.detailValue, { color: status.templates ? '#10b981' : '#ef4444' }]}>
              {status.templates ? '✅ Available' : '❌ Not Available'}
            </Text>
          </View>
          
          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Admin Portal:</Text>
            <Text style={[styles.detailValue, { color: status.adminPortalConnection ? '#10b981' : '#ef4444' }]}>
              {status.adminPortalConnection ? '✅ Connected' : '❌ Not Connected'}
            </Text>
          </View>
        </View>
      )}

      <View style={styles.buttonRow}>
        <TouchableOpacity
          style={[styles.button, styles.refreshButton]}
          onPress={checkEmailStatus}
          disabled={loading}
        >
          <Text style={styles.buttonText}>Refresh</Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[styles.button, styles.testButton]}
          onPress={runEmailTest}
          disabled={loading}
        >
          <Text style={styles.buttonText}>Test System</Text>
        </TouchableOpacity>
      </View>

      {lastChecked && (
        <Text style={styles.lastChecked}>
          Last checked: {lastChecked.toLocaleTimeString()}
        </Text>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 16,
    margin: 8,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  simpleIndicator: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    alignSelf: 'flex-start',
  },
  simpleText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '600',
  },
  statusRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  statusDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 8,
  },
  statusText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    flex: 1,
  },
  detailsContainer: {
    marginBottom: 16,
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 4,
  },
  detailLabel: {
    fontSize: 14,
    color: '#666',
    fontWeight: '500',
  },
  detailValue: {
    fontSize: 14,
    fontWeight: '600',
  },
  buttonRow: {
    flexDirection: 'row',
    gap: 8,
  },
  button: {
    flex: 1,
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 6,
    alignItems: 'center',
  },
  refreshButton: {
    backgroundColor: '#6b7280',
  },
  testButton: {
    backgroundColor: '#3b82f6',
  },
  buttonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
  },
  lastChecked: {
    fontSize: 12,
    color: '#9ca3af',
    textAlign: 'center',
    marginTop: 8,
  },
});

export default EmailStatusIndicator;
