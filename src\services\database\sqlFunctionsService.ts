/**
 * Ocean Soul Sparkles Mobile App - SQL Functions Integration Service
 * Integrates with existing Supabase SQL functions for business workflows
 */

import { supabase } from './supabase';
import { DatabaseResponse } from '@/types/database';

export interface InvoiceGenerationResult {
  success: boolean;
  invoice_id?: string;
  invoice_number?: string;
  total_amount?: number;
  error?: string;
}

export interface AcknowledgmentResult {
  success: boolean;
  acknowledgment_id?: string;
  status?: string;
  error?: string;
}

export interface TransactionNumberResult {
  success: boolean;
  transaction_number?: string;
  error?: string;
}

export interface SQLFunctionResult<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  duration: number;
}

export class SQLFunctionsService {
  private static instance: SQLFunctionsService;

  private constructor() {}

  public static getInstance(): SQLFunctionsService {
    if (!SQLFunctionsService.instance) {
      SQLFunctionsService.instance = new SQLFunctionsService();
    }
    return SQLFunctionsService.instance;
  }

  /**
   * Generate unique transaction number
   */
  async generateTransactionNumber(): Promise<TransactionNumberResult> {
    try {
      console.log('🔢 Generating transaction number...');
      const startTime = Date.now();

      const { data, error } = await supabase.rpc('generate_transaction_number');

      if (error) {
        console.error('❌ Transaction number generation failed:', error);
        return {
          success: false,
          error: error.message,
        };
      }

      console.log(`✅ Transaction number generated: ${data} (${Date.now() - startTime}ms)`);
      return {
        success: true,
        transaction_number: data,
      };

    } catch (error) {
      console.error('❌ Transaction number generation error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Generate unique invoice number
   */
  async generateInvoiceNumber(): Promise<SQLFunctionResult<string>> {
    try {
      console.log('🧾 Generating invoice number...');
      const startTime = Date.now();

      const { data, error } = await supabase.rpc('generate_invoice_number');

      if (error) {
        console.error('❌ Invoice number generation failed:', error);
        return {
          success: false,
          error: error.message,
          duration: Date.now() - startTime,
        };
      }

      console.log(`✅ Invoice number generated: ${data} (${Date.now() - startTime}ms)`);
      return {
        success: true,
        data,
        duration: Date.now() - startTime,
      };

    } catch (error) {
      console.error('❌ Invoice number generation error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        duration: 0,
      };
    }
  }

  /**
   * Calculate invoice total using SQL function
   */
  async calculateInvoiceTotal(invoiceId: string): Promise<SQLFunctionResult<number>> {
    try {
      console.log('💰 Calculating invoice total for:', invoiceId);
      const startTime = Date.now();

      const { data, error } = await supabase.rpc('calculate_invoice_total', {
        invoice_id: invoiceId,
      });

      if (error) {
        console.error('❌ Invoice total calculation failed:', error);
        return {
          success: false,
          error: error.message,
          duration: Date.now() - startTime,
        };
      }

      console.log(`✅ Invoice total calculated: $${data} (${Date.now() - startTime}ms)`);
      return {
        success: true,
        data: parseFloat(data) || 0,
        duration: Date.now() - startTime,
      };

    } catch (error) {
      console.error('❌ Invoice total calculation error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        duration: 0,
      };
    }
  }

  /**
   * Create invoice from quote using SQL function
   */
  async createInvoiceFromQuote(quoteId: string): Promise<InvoiceGenerationResult> {
    try {
      console.log('📄 Creating invoice from quote:', quoteId);
      const startTime = Date.now();

      const { data, error } = await supabase.rpc('create_invoice_from_quote', {
        quote_id: quoteId,
      });

      if (error) {
        console.error('❌ Invoice creation from quote failed:', error);
        return {
          success: false,
          error: error.message,
        };
      }

      console.log(`✅ Invoice created from quote (${Date.now() - startTime}ms):`, data);
      return {
        success: true,
        invoice_id: data?.invoice_id,
        invoice_number: data?.invoice_number,
        total_amount: data?.total_amount,
      };

    } catch (error) {
      console.error('❌ Invoice creation from quote error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Update invoice status using SQL function
   */
  async updateInvoiceStatus(
    invoiceId: string, 
    newStatus: 'draft' | 'sent' | 'paid' | 'overdue' | 'cancelled'
  ): Promise<SQLFunctionResult<boolean>> {
    try {
      console.log(`📝 Updating invoice ${invoiceId} status to:`, newStatus);
      const startTime = Date.now();

      const { data, error } = await supabase.rpc('update_invoice_status', {
        invoice_id: invoiceId,
        new_status: newStatus,
      });

      if (error) {
        console.error('❌ Invoice status update failed:', error);
        return {
          success: false,
          error: error.message,
          duration: Date.now() - startTime,
        };
      }

      console.log(`✅ Invoice status updated (${Date.now() - startTime}ms)`);
      return {
        success: true,
        data: true,
        duration: Date.now() - startTime,
      };

    } catch (error) {
      console.error('❌ Invoice status update error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        duration: 0,
      };
    }
  }

  /**
   * Create booking acknowledgment using SQL function
   */
  async createBookingAcknowledgment(bookingId: string): Promise<AcknowledgmentResult> {
    try {
      console.log('📧 Creating booking acknowledgment for:', bookingId);
      const startTime = Date.now();

      const { data, error } = await supabase.rpc('create_booking_acknowledgment', {
        booking_id: bookingId,
      });

      if (error) {
        console.error('❌ Booking acknowledgment creation failed:', error);
        return {
          success: false,
          error: error.message,
        };
      }

      console.log(`✅ Booking acknowledgment created (${Date.now() - startTime}ms):`, data);
      return {
        success: true,
        acknowledgment_id: data?.acknowledgment_id,
        status: data?.status,
      };

    } catch (error) {
      console.error('❌ Booking acknowledgment creation error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Send quote acknowledgment using SQL function
   */
  async sendQuoteAcknowledgment(quoteId: string): Promise<AcknowledgmentResult> {
    try {
      console.log('📧 Sending quote acknowledgment for:', quoteId);
      const startTime = Date.now();

      const { data, error } = await supabase.rpc('send_quote_acknowledgment', {
        quote_id: quoteId,
      });

      if (error) {
        console.error('❌ Quote acknowledgment sending failed:', error);
        return {
          success: false,
          error: error.message,
        };
      }

      console.log(`✅ Quote acknowledgment sent (${Date.now() - startTime}ms):`, data);
      return {
        success: true,
        acknowledgment_id: data?.acknowledgment_id,
        status: data?.status,
      };

    } catch (error) {
      console.error('❌ Quote acknowledgment sending error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Get pending acknowledgments using SQL function
   */
  async getPendingAcknowledgments(): Promise<SQLFunctionResult<any[]>> {
    try {
      console.log('📋 Getting pending acknowledgments...');
      const startTime = Date.now();

      const { data, error } = await supabase.rpc('get_pending_acknowledgments');

      if (error) {
        console.error('❌ Get pending acknowledgments failed:', error);
        return {
          success: false,
          error: error.message,
          duration: Date.now() - startTime,
        };
      }

      console.log(`✅ Pending acknowledgments retrieved: ${data?.length || 0} items (${Date.now() - startTime}ms)`);
      return {
        success: true,
        data: data || [],
        duration: Date.now() - startTime,
      };

    } catch (error) {
      console.error('❌ Get pending acknowledgments error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        duration: 0,
      };
    }
  }

  /**
   * Calculate booking total using SQL function
   */
  async calculateBookingTotal(bookingId: string): Promise<SQLFunctionResult<number>> {
    try {
      console.log('💰 Calculating booking total for:', bookingId);
      const startTime = Date.now();

      const { data, error } = await supabase.rpc('calculate_booking_total', {
        booking_id: bookingId,
      });

      if (error) {
        console.error('❌ Booking total calculation failed:', error);
        return {
          success: false,
          error: error.message,
          duration: Date.now() - startTime,
        };
      }

      console.log(`✅ Booking total calculated: $${data} (${Date.now() - startTime}ms)`);
      return {
        success: true,
        data: parseFloat(data) || 0,
        duration: Date.now() - startTime,
      };

    } catch (error) {
      console.error('❌ Booking total calculation error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        duration: 0,
      };
    }
  }

  /**
   * Execute custom SQL function with parameters
   */
  async executeFunction<T = any>(
    functionName: string, 
    parameters: Record<string, any> = {}
  ): Promise<SQLFunctionResult<T>> {
    try {
      console.log(`⚙️ Executing SQL function: ${functionName}`, parameters);
      const startTime = Date.now();

      const { data, error } = await supabase.rpc(functionName, parameters);

      if (error) {
        console.error(`❌ SQL function ${functionName} failed:`, error);
        return {
          success: false,
          error: error.message,
          duration: Date.now() - startTime,
        };
      }

      console.log(`✅ SQL function ${functionName} executed (${Date.now() - startTime}ms)`);
      return {
        success: true,
        data,
        duration: Date.now() - startTime,
      };

    } catch (error) {
      console.error(`❌ SQL function ${functionName} error:`, error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        duration: 0,
      };
    }
  }

  /**
   * Test SQL function availability
   */
  async testFunctionAvailability(functionName: string): Promise<boolean> {
    try {
      // Try to call the function with minimal parameters
      const { error } = await supabase.rpc(functionName, {});
      
      // If we get a parameter error, the function exists
      // If we get a function not found error, it doesn't exist
      if (error) {
        return !error.message.includes('function') || 
               !error.message.includes('does not exist');
      }
      
      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * Get available SQL functions
   */
  async getAvailableFunctions(): Promise<string[]> {
    const expectedFunctions = [
      'generate_transaction_number',
      'generate_invoice_number',
      'calculate_invoice_total',
      'create_invoice_from_quote',
      'update_invoice_status',
      'create_booking_acknowledgment',
      'send_quote_acknowledgment',
      'get_pending_acknowledgments',
      'calculate_booking_total',
      'update_updated_at_column',
    ];

    const availableFunctions: string[] = [];

    for (const functionName of expectedFunctions) {
      const isAvailable = await this.testFunctionAvailability(functionName);
      if (isAvailable) {
        availableFunctions.push(functionName);
      }
    }

    return availableFunctions;
  }
}

// Export singleton instance
export const sqlFunctionsService = SQLFunctionsService.getInstance();
