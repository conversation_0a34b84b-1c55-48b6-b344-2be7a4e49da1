/**
 * Ocean Soul Sparkles Mobile App - Customer Service
 * Handles customer management and queries
 */

import { supabase } from './supabase';
import { Customer, DatabaseResponse, DatabaseListResponse, QueryFilters } from '@/types/database';

export class CustomerService {

  /**
   * Transform customer data to ensure backward compatibility
   */
  private transformCustomerData(customer: any): Customer {
    return {
      ...customer,
      full_name: customer.name || customer.full_name, // Provide full_name for backward compatibility
    };
  }

  /**
   * Transform array of customer data
   */
  private transformCustomersData(customers: any[]): Customer[] {
    return customers.map(customer => this.transformCustomerData(customer));
  }

  /**
   * Get all customers with optional filtering
   */
  async getCustomers(filters?: QueryFilters): Promise<DatabaseListResponse<Customer>> {
    try {
      console.log('👥 Loading customers...');
      
      let query = supabase
        .from('customers')
        .select('*');

      // Apply search filter with performance optimization
      if (filters?.search) {
        const searchTerm = filters.search.trim();
        if (searchTerm.length >= 2) { // Minimum search length for performance
          // Prioritize exact matches and common fields
          query = query.or(`full_name.ilike.%${searchTerm}%,email.ilike.%${searchTerm}%,phone.ilike.%${searchTerm}%`);
        }
      }

      // Apply ordering with performance-conscious defaults
      const orderBy = filters?.order_by || 'full_name';
      const orderDirection = filters?.order_direction || 'asc';
      query = query.order(orderBy, { ascending: orderDirection === 'asc' });

      // Apply pagination with default limits
      const limit = filters?.limit || 100; // Default limit for customers
      const offset = filters?.offset || 0;

      query = query.limit(limit);
      if (offset > 0) {
        query = query.range(offset, offset + limit - 1);
      }
      if (filters?.offset) {
        query = query.range(filters.offset, filters.offset + (filters.limit || 20) - 1);
      }

      const { data, error, count } = await query;

      if (error) {
        console.error('❌ Get customers error:', error);
        return { data: null, error, count: 0 };
      }

      console.log(`✅ Loaded ${data?.length || 0} customers`);
      const transformedData = data ? this.transformCustomersData(data) : [];
      return { data: transformedData, error: null, count: count || data?.length || 0 };
    } catch (error) {
      console.error('❌ Customer service error:', error);
      return { data: null, error: error as Error, count: 0 };
    }
  }

  /**
   * Get customer by ID
   */
  async getCustomerById(id: string): Promise<DatabaseResponse<Customer>> {
    try {
      const { data, error } = await supabase
        .from('customers')
        .select('*')
        .eq('id', id)
        .single();

      if (error) {
        console.error('❌ Get customer error:', error);
        return { data: null, error };
      }

      const transformedData = data ? this.transformCustomerData(data) : null;
      return { data: transformedData, error: null };
    } catch (error) {
      console.error('❌ Get customer service error:', error);
      return { data: null, error: error as Error };
    }
  }

  /**
   * Create a new customer
   */
  async createCustomer(customerData: Partial<Customer>): Promise<DatabaseResponse<Customer>> {
    try {
      console.log('📝 Creating new customer...');

      const { data, error } = await supabase
        .from('customers')
        .insert([{
          email: customerData.email,
          full_name: customerData.full_name,
          phone: customerData.phone,
          address: customerData.address,
          city: customerData.city,
          state: customerData.state,
          postal_code: customerData.postal_code,
          notes: customerData.notes,
        }])
        .select('*')
        .single();

      if (error) {
        console.error('❌ Create customer error:', error);
        return { data: null, error };
      }

      console.log('✅ Customer created successfully');
      const transformedData = data ? this.transformCustomerData(data) : null;
      return { data: transformedData, error: null };
    } catch (error) {
      console.error('❌ Create customer service error:', error);
      return { data: null, error: error as Error };
    }
  }

  /**
   * Update an existing customer
   */
  async updateCustomer(id: string, updateData: Partial<Customer>): Promise<DatabaseResponse<Customer>> {
    try {
      console.log('📝 Updating customer:', id);

      const { data, error } = await supabase
        .from('customers')
        .update({
          email: updateData.email,
          full_name: updateData.full_name,
          phone: updateData.phone,
          address: updateData.address,
          city: updateData.city,
          state: updateData.state,
          postal_code: updateData.postal_code,
          notes: updateData.notes,
          updated_at: new Date().toISOString(),
        })
        .eq('id', id)
        .select('*')
        .single();

      if (error) {
        console.error('❌ Update customer error:', error);
        return { data: null, error };
      }

      console.log('✅ Customer updated successfully');
      const transformedData = data ? this.transformCustomerData(data) : null;
      return { data: transformedData, error: null };
    } catch (error) {
      console.error('❌ Update customer service error:', error);
      return { data: null, error: error as Error };
    }
  }

  /**
   * Delete a customer
   */
  async deleteCustomer(id: string): Promise<DatabaseResponse<boolean>> {
    try {
      console.log('🗑️ Deleting customer:', id);

      const { error } = await supabase
        .from('customers')
        .delete()
        .eq('id', id);

      if (error) {
        console.error('❌ Delete customer error:', error);
        return { data: null, error };
      }

      console.log('✅ Customer deleted successfully');
      return { data: true, error: null };
    } catch (error) {
      console.error('❌ Delete customer service error:', error);
      return { data: null, error: error as Error };
    }
  }

  /**
   * Search customers by name, email, or phone
   */
  async searchCustomers(searchTerm: string, limit: number = 20): Promise<DatabaseListResponse<Customer>> {
    return this.getCustomers({
      search: searchTerm,
      limit,
      order_by: 'full_name',
      order_direction: 'asc',
    });
  }
}

// Export singleton instance
export const customerService = new CustomerService();
