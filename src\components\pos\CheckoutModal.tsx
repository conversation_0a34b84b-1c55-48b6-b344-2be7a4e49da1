/**
 * Ocean Soul Sparkles Mobile App - Checkout Modal Component
 * Handles transaction completion and payment processing
 */

import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  TouchableOpacity,
  TextInput,
  Alert,
  ActivityIndicator,
  ScrollView,
} from 'react-native';
import { CartItem, Customer } from '@/types/database';
import { transactionService } from '@/services/database/transactionService';
import { useAuth } from '@/store/authStore.minimal';
import { usePOSStore } from '@/store/posStore';

interface CheckoutModalProps {
  visible: boolean;
  onClose: () => void;
  cartItems: CartItem[];
  cartTotal: number;
  onTransactionComplete: (transactionId: string) => void;
}

const CheckoutModal: React.FC<CheckoutModalProps> = ({
  visible,
  onClose,
  cartItems,
  cartTotal,
  onTransactionComplete,
}) => {
  const [paymentMethod, setPaymentMethod] = useState<'cash' | 'card' | 'digital_wallet'>('cash');
  const [customerName, setCustomerName] = useState('');
  const [customerEmail, setCustomerEmail] = useState('');
  const [customerPhone, setCustomerPhone] = useState('');
  const [notes, setNotes] = useState('');
  const [processing, setProcessing] = useState(false);
  
  const { user } = useAuth();
  const { clearCart } = usePOSStore();

  const subtotal = cartTotal || 0;
  const taxRate = 0.10; // 10% GST
  const taxAmount = subtotal * taxRate;
  const total = subtotal + taxAmount;

  const handleCompleteTransaction = async () => {
    if (!user) {
      Alert.alert('Error', 'User not authenticated');
      return;
    }

    setProcessing(true);

    try {
      console.log('🛒 Processing checkout...');

      // Create customer if details provided
      let customerId: string | undefined;
      if (customerName.trim() || customerEmail.trim() || customerPhone.trim()) {
        // For now, we'll pass customer info in notes
        // In Phase 3B, we'll implement proper customer management
        const customerInfo = [
          customerName.trim() && `Name: ${customerName.trim()}`,
          customerEmail.trim() && `Email: ${customerEmail.trim()}`,
          customerPhone.trim() && `Phone: ${customerPhone.trim()}`,
        ].filter(Boolean).join(', ');
        
        if (customerInfo) {
          setNotes(prev => prev ? `${prev}\nCustomer: ${customerInfo}` : `Customer: ${customerInfo}`);
        }
      }

      // Create transaction
      const result = await transactionService.createTransaction(
        cartItems,
        user.id,
        customerId,
        paymentMethod,
        notes.trim() || undefined
      );

      if (result.error || !result.data) {
        throw new Error(result.error?.message || 'Failed to create transaction');
      }

      // Transaction completed successfully

      // Clear cart and close modal
      clearCart();
      onTransactionComplete(result.data.id);
      onClose();

      // Show success message
      Alert.alert(
        '🎉 Transaction Complete!',
        `Transaction ${result.data.transaction_number} completed successfully.\nTotal: $${(total || 0).toFixed(2)}`,
        [{ text: 'OK' }]
      );

    } catch (error) {
      console.error('❌ Checkout error:', error);
      Alert.alert(
        'Transaction Failed',
        error instanceof Error ? error.message : 'An unexpected error occurred',
        [{ text: 'OK' }]
      );
    } finally {
      setProcessing(false);
    }
  };

  const testPOSCustomerIntegration = async () => {
    Alert.alert(
      'POS Customer Integration Test',
      'This will test customer selection and transaction association in the POS system. Continue?',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Run Test', onPress: performPOSTest },
      ]
    );
  };

  const performPOSTest = async () => {
    if (!user) {
      Alert.alert('Error', 'User not authenticated for testing');
      return;
    }

    let testResults: string[] = [];

    try {
      // Test 1: Check if customer is selected
      if (currentCustomer) {
        testResults.push(`✅ CUSTOMER SELECTION: Customer "${currentCustomer.full_name}" is selected`);
      } else {
        testResults.push('⚠️ CUSTOMER SELECTION: No customer selected (optional)');
      }

      // Test 2: Check cart items
      if (cartItems.length > 0) {
        testResults.push(`✅ CART: ${cartItems.length} items in cart`);
        testResults.push(`✅ TOTAL: $${total.toFixed(2)} calculated`);
      } else {
        testResults.push('❌ CART: No items in cart - cannot test transaction');
        Alert.alert('Test Error', 'Please add items to cart before testing POS integration');
        return;
      }

      // Test 3: Test transaction creation (dry run)
      console.log('🧪 Testing POS transaction creation...');

      // Create a test transaction to validate the integration
      const result = await transactionService.createTransaction(
        cartItems,
        user.id,
        currentCustomer?.id, // This tests customer association
        paymentMethod,
        'TEST TRANSACTION - POS Customer Integration Validation'
      );

      if (result.error) {
        testResults.push('❌ TRANSACTION: Failed - ' + result.error.message);
      } else if (result.data) {
        testResults.push('✅ TRANSACTION: Successfully created with customer association');
        testResults.push(`✅ TRANSACTION ID: ${result.data.transaction_number}`);

        if (currentCustomer) {
          testResults.push(`✅ CUSTOMER LINK: Transaction linked to customer "${currentCustomer.full_name}"`);
        } else {
          testResults.push('✅ NO CUSTOMER: Transaction created without customer (valid)');
        }
      }

    } catch (error) {
      testResults.push('❌ GENERAL: Unexpected error - ' + error);
    }

    // Show test results
    const resultsText = testResults.join('\n');
    Alert.alert(
      'POS Integration Test Results 📋',
      resultsText,
      [{ text: 'OK' }]
    );

    console.log('🧪 POS Customer Integration Tests completed:', testResults);
  };

  const resetForm = () => {
    setPaymentMethod('cash');
    setCustomerName('');
    setCustomerEmail('');
    setCustomerPhone('');
    setNotes('');
  };

  const handleClose = () => {
    resetForm();
    onClose();
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={handleClose}
    >
      <View style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity onPress={handleClose} style={styles.cancelButton}>
            <Text style={styles.cancelButtonText}>Cancel</Text>
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Checkout</Text>
          <View style={styles.placeholder} />
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {/* Order Summary */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Order Summary</Text>
            {cartItems.map((item) => (
              <View key={item.id} style={styles.orderItem}>
                <View style={styles.orderItemInfo}>
                  <Text style={styles.orderItemName}>{item.item.name}</Text>
                  <Text style={styles.orderItemDetails}>
                    {item.quantity} × ${(item.unit_price || 0).toFixed(2)}
                  </Text>
                </View>
                <Text style={styles.orderItemTotal}>${(item.total_price || 0).toFixed(2)}</Text>
              </View>
            ))}
            
            <View style={styles.totalsContainer}>
              <View style={styles.totalRow}>
                <Text style={styles.totalLabel}>Subtotal</Text>
                <Text style={styles.totalValue}>${(subtotal || 0).toFixed(2)}</Text>
              </View>
              <View style={styles.totalRow}>
                <Text style={styles.totalLabel}>GST (10%)</Text>
                <Text style={styles.totalValue}>${(taxAmount || 0).toFixed(2)}</Text>
              </View>
              <View style={[styles.totalRow, styles.grandTotalRow]}>
                <Text style={styles.grandTotalLabel}>Total</Text>
                <Text style={styles.grandTotalValue}>${(total || 0).toFixed(2)}</Text>
              </View>
            </View>
          </View>

          {/* Payment Method */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Payment Method</Text>
            <View style={styles.paymentMethods}>
              {[
                { key: 'cash', label: '💵 Cash', icon: '💵' },
                { key: 'card', label: '💳 Card', icon: '💳' },
                { key: 'digital_wallet', label: '📱 Digital Wallet', icon: '📱' },
              ].map((method) => (
                <TouchableOpacity
                  key={method.key}
                  style={[
                    styles.paymentMethod,
                    paymentMethod === method.key && styles.paymentMethodSelected,
                  ]}
                  onPress={() => setPaymentMethod(method.key as any)}
                >
                  <Text style={styles.paymentMethodIcon}>{method.icon}</Text>
                  <Text
                    style={[
                      styles.paymentMethodText,
                      paymentMethod === method.key && styles.paymentMethodTextSelected,
                    ]}
                  >
                    {method.label}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>

          {/* Customer Information (Optional) */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Customer Information (Optional)</Text>
            <TextInput
              style={styles.input}
              placeholder="Customer Name"
              value={customerName}
              onChangeText={setCustomerName}
            />
            <TextInput
              style={styles.input}
              placeholder="Email Address"
              value={customerEmail}
              onChangeText={setCustomerEmail}
              keyboardType="email-address"
              autoCapitalize="none"
            />
            <TextInput
              style={styles.input}
              placeholder="Phone Number"
              value={customerPhone}
              onChangeText={setCustomerPhone}
              keyboardType="phone-pad"
            />
          </View>

          {/* Notes */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Notes (Optional)</Text>
            <TextInput
              style={[styles.input, styles.notesInput]}
              placeholder="Add any notes about this transaction..."
              value={notes}
              onChangeText={setNotes}
              multiline
              numberOfLines={3}
            />
          </View>
        </ScrollView>

        {/* Test and Complete Transaction Buttons */}
        <View style={styles.footer}>
          <TouchableOpacity
            style={styles.testButton}
            onPress={testPOSCustomerIntegration}
            disabled={processing}
          >
            <Text style={styles.testButtonText}>🧪 Test POS Integration</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.completeButton, processing && styles.completeButtonDisabled]}
            onPress={handleCompleteTransaction}
            disabled={processing}
          >
            {processing ? (
              <View style={styles.processingContainer}>
                <ActivityIndicator size="small" color="#fff" />
                <Text style={styles.completeButtonText}>Processing...</Text>
              </View>
            ) : (
              <Text style={styles.completeButtonText}>
                Complete Transaction • ${(total || 0).toFixed(2)}
              </Text>
            )}
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  cancelButton: {
    padding: 8,
  },
  cancelButtonText: {
    fontSize: 16,
    color: '#FF9A8B',
    fontWeight: '600',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  placeholder: {
    width: 60,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  section: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginBottom: 16,
  },
  orderItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  orderItemInfo: {
    flex: 1,
  },
  orderItemName: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
    marginBottom: 2,
  },
  orderItemDetails: {
    fontSize: 14,
    color: '#666',
  },
  orderItemTotal: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FF9A8B',
  },
  totalsContainer: {
    marginTop: 16,
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
  },
  totalRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  grandTotalRow: {
    marginTop: 8,
    paddingTop: 8,
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
  },
  totalLabel: {
    fontSize: 16,
    color: '#666',
  },
  totalValue: {
    fontSize: 16,
    color: '#333',
  },
  grandTotalLabel: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
  },
  grandTotalValue: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#FF9A8B',
  },
  paymentMethods: {
    flexDirection: 'row',
    gap: 12,
  },
  paymentMethod: {
    flex: 1,
    padding: 16,
    borderRadius: 8,
    borderWidth: 2,
    borderColor: '#e0e0e0',
    alignItems: 'center',
    backgroundColor: '#f8f9fa',
  },
  paymentMethodSelected: {
    borderColor: '#FF9A8B',
    backgroundColor: '#fff',
  },
  paymentMethodIcon: {
    fontSize: 24,
    marginBottom: 8,
  },
  paymentMethodText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#666',
    textAlign: 'center',
  },
  paymentMethodTextSelected: {
    color: '#FF9A8B',
    fontWeight: '600',
  },
  input: {
    borderWidth: 1,
    borderColor: '#e0e0e0',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    backgroundColor: '#fff',
    marginBottom: 12,
  },
  notesInput: {
    height: 80,
    textAlignVertical: 'top',
  },
  footer: {
    padding: 16,
    backgroundColor: '#fff',
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
    gap: 12,
  },
  testButton: {
    backgroundColor: '#f0f8ff',
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#e6f3ff',
  },
  testButtonText: {
    color: '#0066cc',
    fontSize: 14,
    fontWeight: '600',
  },
  completeButton: {
    backgroundColor: '#FF9A8B',
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
  },
  completeButtonDisabled: {
    opacity: 0.6,
  },
  completeButtonText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: '600',
  },
  processingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
});

export default CheckoutModal;
