// Ocean Soul Sparkles Mobile App - Service Initialization
import { pushNotificationService } from './notifications/pushNotificationService';
import { staffCommunicationService } from './communication/staffCommunicationService';
import { realTimeDataSyncService } from './adminPortal/realTimeDataSyncService';

export const initializeServices = async () => {
  try {
    console.log('🚀 Initializing Ocean Soul Sparkles services...');

    // Initialize core services in order of dependency
    console.log('📡 Initializing real-time data sync service...');
    await realTimeDataSyncService.initialize();

    console.log('🔔 Initializing push notification service...');
    await pushNotificationService.initialize();

    console.log('💬 Initializing staff communication service...');
    await staffCommunicationService.initialize();

    console.log('✅ All services initialized successfully');
  } catch (error) {
    console.error('❌ Failed to initialize services:', error);
    throw error;
  }
};

/**
 * Cleanup all services
 */
export const cleanupServices = async () => {
  try {
    console.log('🧹 Cleaning up Ocean Soul Sparkles services...');

    // Cleanup services in reverse order
    await staffCommunicationService.cleanup();
    await realTimeDataSyncService.cleanup();

    console.log('✅ All services cleaned up successfully');
  } catch (error) {
    console.error('❌ Failed to cleanup services:', error);
  }
};