/**
 * Ocean Soul Sparkles Mobile App - Runtime Fixes Test
 * Tests the fixes for property access errors and app registration
 */

// Test 1: Verify Quote interface property access
function testQuotePropertyAccess() {
  console.log('🧪 Testing Quote property access...');
  
  try {
    // Import the Quote type
    const { Quote } = require('../src/types/database');
    
    // Create a test quote object with the correct properties
    const testQuote = {
      id: 'test-quote-id',
      customer_id: 'test-customer-id',
      customer_first_name: '<PERSON>',
      customer_last_name: '<PERSON><PERSON>',
      customer_email: '<EMAIL>',
      customer_phone: '+61123456789',
      service_name: 'Test Service',
      service_description: 'Test service description',
      estimated_total: 150.00,
      status: 'pending',
      source: 'mobile_app',
      number_of_people: 1,
      base_price: 100.00,
      travel_cost: 25.00,
      additional_costs: 25.00,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };
    
    // Test accessing estimated_total property
    const total = testQuote.estimated_total;
    if (typeof total === 'number') {
      console.log('✅ estimated_total property access works:', total);
    } else {
      console.log('❌ estimated_total property access failed');
      return false;
    }
    
    // Test accessing service_name property
    const serviceName = testQuote.service_name;
    if (typeof serviceName === 'string') {
      console.log('✅ service_name property access works:', serviceName);
    } else {
      console.log('❌ service_name property access failed');
      return false;
    }
    
    return true;
  } catch (error) {
    console.error('❌ Quote property access test failed:', error.message);
    return false;
  }
}

// Test 2: Verify email template variables
function testEmailTemplateVariables() {
  console.log('\n📧 Testing email template variables...');
  
  try {
    // Import email templates
    const { getFallbackTemplate } = require('../src/services/email/emailTemplates');
    
    // Test quote template
    const quoteTemplate = getFallbackTemplate('quote');
    if (!quoteTemplate) {
      console.log('❌ Quote template not found');
      return false;
    }
    
    // Check that template uses estimated_total
    if (quoteTemplate.html_content.includes('{{estimated_total}}')) {
      console.log('✅ Quote template uses estimated_total');
    } else {
      console.log('❌ Quote template does not use estimated_total');
      console.log('Template content preview:', quoteTemplate.html_content.substring(0, 200));
      return false;
    }
    
    // Check variables array
    if (quoteTemplate.variables.includes('estimated_total')) {
      console.log('✅ Quote template variables include estimated_total');
    } else {
      console.log('❌ Quote template variables missing estimated_total');
      console.log('Available variables:', quoteTemplate.variables);
      return false;
    }
    
    return true;
  } catch (error) {
    console.error('❌ Email template test failed:', error.message);
    return false;
  }
}

// Test 3: Verify app registration setup
function testAppRegistrationSetup() {
  console.log('\n📱 Testing app registration setup...');
  
  try {
    const fs = require('fs');
    const path = require('path');
    
    // Check if index.js exists
    const indexPath = path.join(__dirname, '..', 'index.js');
    if (fs.existsSync(indexPath)) {
      console.log('✅ index.js entry point exists');
      
      // Check content
      const indexContent = fs.readFileSync(indexPath, 'utf8');
      if (indexContent.includes('registerRootComponent')) {
        console.log('✅ index.js contains registerRootComponent');
      } else {
        console.log('❌ index.js missing registerRootComponent');
        return false;
      }
    } else {
      console.log('❌ index.js entry point missing');
      return false;
    }
    
    // Check app.json configuration
    const appJsonPath = path.join(__dirname, '..', 'app.json');
    if (fs.existsSync(appJsonPath)) {
      console.log('✅ app.json exists');
      
      const appConfig = JSON.parse(fs.readFileSync(appJsonPath, 'utf8'));
      if (appConfig.expo && appConfig.expo.main === 'index.js') {
        console.log('✅ app.json specifies correct main entry point');
      } else {
        console.log('❌ app.json missing or incorrect main entry point');
        console.log('Current main:', appConfig.expo?.main);
        return false;
      }
    } else {
      console.log('❌ app.json missing');
      return false;
    }
    
    return true;
  } catch (error) {
    console.error('❌ App registration test failed:', error.message);
    return false;
  }
}

// Test 4: Verify email service property usage
function testEmailServicePropertyUsage() {
  console.log('\n📧 Testing email service property usage...');
  
  try {
    // Create a mock quote with the correct properties
    const mockQuote = {
      id: 'test-quote-id',
      customer_id: 'test-customer-id',
      customer_first_name: 'John',
      customer_last_name: 'Doe',
      customer_email: '<EMAIL>',
      customer_phone: '+61123456789',
      service_name: 'Test Service',
      service_description: 'Test service description',
      estimated_total: 150.00,
      status: 'pending',
      source: 'mobile_app',
      number_of_people: 1,
      base_price: 100.00,
      travel_cost: 25.00,
      additional_costs: 25.00,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };
    
    // Test the property access that was causing the runtime error
    const estimatedTotal = mockQuote.estimated_total;
    const formattedTotal = estimatedTotal.toFixed(2);
    
    console.log('✅ estimated_total property access works:', formattedTotal);
    
    // Test service name access
    const serviceName = mockQuote.service_name;
    console.log('✅ service_name property access works:', serviceName);
    
    return true;
  } catch (error) {
    console.error('❌ Email service property test failed:', error.message);
    return false;
  }
}

// Main test runner
async function runRuntimeFixesTest() {
  console.log('🚨 Ocean Soul Sparkles - Runtime Fixes Test');
  console.log('='.repeat(50));
  
  const tests = [
    { name: 'Quote Property Access', test: testQuotePropertyAccess },
    { name: 'Email Template Variables', test: testEmailTemplateVariables },
    { name: 'App Registration Setup', test: testAppRegistrationSetup },
    { name: 'Email Service Property Usage', test: testEmailServicePropertyUsage },
  ];
  
  const results = [];
  
  for (const { name, test } of tests) {
    try {
      const result = test();
      results.push({ name, passed: result });
    } catch (error) {
      console.error(`❌ ${name} test crashed:`, error.message);
      results.push({ name, passed: false });
    }
  }
  
  // Summary
  console.log('\n📊 TEST RESULTS SUMMARY:');
  console.log('='.repeat(30));
  
  const passed = results.filter(r => r.passed).length;
  const total = results.length;
  
  results.forEach(({ name, passed }) => {
    console.log(`${passed ? '✅' : '❌'} ${name}`);
  });
  
  console.log(`\n🎯 Overall: ${passed}/${total} tests passed`);
  
  if (passed === total) {
    console.log('🎉 All runtime fixes are working correctly!');
    console.log('\n📱 The app should now:');
    console.log('  - Build without property access errors');
    console.log('  - Register properly with React Native');
    console.log('  - Handle quote email templates correctly');
    console.log('  - Access estimated_total property without errors');
  } else {
    console.log('⚠️ Some tests failed. Please review the issues above.');
  }
  
  return passed === total;
}

// Run if executed directly
if (require.main === module) {
  runRuntimeFixesTest().catch(console.error);
}

module.exports = { runRuntimeFixesTest };
