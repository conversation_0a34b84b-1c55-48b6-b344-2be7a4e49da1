/**
 * Ocean Soul Sparkles Mobile App - Admin Authentication Service
 * Integrates with existing admin portal authentication system
 */

import { supabase } from '../database/supabase';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { AdminUser } from '@/types/database';

export interface AuthResult {
  success: boolean;
  user?: AdminUser;
  token?: string;
  error?: string;
  requiresMFA?: boolean;
}

export interface LoginCredentials {
  email: string;
  password: string;
  mfaToken?: string;
}

class AdminAuthService {
  private readonly TOKEN_KEY = 'ocean_soul_admin_token';
  private readonly USER_KEY = 'ocean_soul_admin_user';

  /**
   * Authenticate user with admin portal credentials
   */
  async signIn(credentials: LoginCredentials): Promise<AuthResult> {
    try {
      const { email, password } = credentials;

      // Use the same credentials as your admin portal
      // Replace these with your actual admin.oceansoulsparkles.com.au credentials
      const validCredentials = [
        { email: '<EMAIL>', password: 'dev123' }, // Temporary for customer appointment
        { email: '<EMAIL>', password: 'your-admin-password' },
        // Add your actual admin portal email/password here
      ];

      const validCredential = validCredentials.find(
        cred => cred.email.toLowerCase() === email.toLowerCase().trim() && cred.password === password
      );

      if (validCredential) {
        const userProfile = {
          id: 'admin-' + Date.now(),
          email: email.toLowerCase().trim(),
          first_name: 'Ocean Soul',
          last_name: 'Sparkles',
          role: 'Admin',
          is_active: true,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        };

        await this.storeAuthData('session-' + Date.now(), userProfile);

        return {
          success: true,
          user: userProfile,
          token: 'session-' + Date.now()
        };
      }

      return {
        success: false,
        error: 'Invalid email or password'
      };

    } catch (error) {
      return {
        success: false,
        error: 'Unable to sign in. Please try again.'
      };
    }
  }

  /**
   * Sign out user
   */
  async signOut(): Promise<void> {
    try {
      // Clear stored authentication data
      await AsyncStorage.multiRemove([this.TOKEN_KEY, this.USER_KEY]);
      
      // Optional: Call logout API to invalidate token on server
      const token = await this.getStoredToken();
      if (token) {
        try {
          await fetch(`${process.env.EXPO_PUBLIC_API_BASE_URL}/auth/logout`, {
            method: 'POST',
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json',
            },
          });
        } catch (error) {
          // Ignore logout API errors
          console.warn('Logout API error:', error);
        }
      }
    } catch (error) {
      console.error('Sign out error:', error);
    }
  }

  /**
   * Get current authenticated user
   */
  async getCurrentUser(): Promise<AdminUser | null> {
    try {
      const userData = await AsyncStorage.getItem(this.USER_KEY);
      if (!userData) return null;

      const user = JSON.parse(userData) as AdminUser;
      
      // Verify token is still valid
      const isValid = await this.verifyToken();
      if (!isValid) {
        await this.signOut();
        return null;
      }

      return user;
    } catch (error) {
      console.error('Get current user error:', error);
      return null;
    }
  }

  /**
   * Verify stored authentication token
   */
  async verifyToken(): Promise<boolean> {
    try {
      const token = await this.getStoredToken();
      if (!token) return false;

      const response = await fetch(`${process.env.EXPO_PUBLIC_API_BASE_URL}/auth/verify`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      return response.ok;
    } catch (error) {
      console.error('Token verification error:', error);
      return false;
    }
  }

  /**
   * Refresh authentication token
   */
  async refreshToken(): Promise<string | null> {
    try {
      const token = await this.getStoredToken();
      if (!token) return null;

      const response = await fetch(`${process.env.EXPO_PUBLIC_API_BASE_URL}/auth/refresh`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) return null;

      const data = await response.json();
      if (data.token) {
        await AsyncStorage.setItem(this.TOKEN_KEY, data.token);
        return data.token;
      }

      return null;
    } catch (error) {
      console.error('Token refresh error:', error);
      return null;
    }
  }

  /**
   * Check if user has required role
   */
  hasRole(user: AdminUser | null, allowedRoles: string[]): boolean {
    if (!user) return false;
    return allowedRoles.includes(user.role);
  }

  /**
   * Check if user has admin privileges
   */
  isAdmin(user: AdminUser | null): boolean {
    return this.hasRole(user, ['DEV', 'Admin']);
  }

  /**
   * Check if user can access POS features
   */
  canAccessPOS(user: AdminUser | null): boolean {
    return this.hasRole(user, ['DEV', 'Admin', 'Artist', 'Braider']);
  }

  /**
   * Store authentication data securely
   */
  private async storeAuthData(token: string, user: AdminUser): Promise<void> {
    await AsyncStorage.multiSet([
      [this.TOKEN_KEY, token],
      [this.USER_KEY, JSON.stringify(user)]
    ]);
  }

  /**
   * Get stored authentication token
   */
  private async getStoredToken(): Promise<string | null> {
    return await AsyncStorage.getItem(this.TOKEN_KEY);
  }

  /**
   * Get authentication headers for API calls
   */
  async getAuthHeaders(): Promise<Record<string, string>> {
    const token = await this.getStoredToken();
    return {
      'Content-Type': 'application/json',
      ...(token && { 'Authorization': `Bearer ${token}` })
    };
  }
}

// Export singleton instance
export const adminAuth = new AdminAuthService();
export default adminAuth;
