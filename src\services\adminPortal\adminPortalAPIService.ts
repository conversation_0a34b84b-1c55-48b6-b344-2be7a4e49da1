/**
 * Ocean Soul Sparkles Mobile App - Admin Portal API Integration Service
 * Enhanced integration with admin dashboard API for complete CRUD operation compatibility
 */

import { useAuth } from '@/store/authStore.minimal';
import Constants from 'expo-constants';

const API_BASE_URL = Constants.expoConfig?.extra?.EXPO_PUBLIC_API_BASE_URL || 
                    process.env.EXPO_PUBLIC_API_BASE_URL || 
                    'https://admin.oceansoulsparkles.com.au/api';

export interface APIResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
  status?: number;
}

export interface AdminPortalHealthCheck {
  status: 'healthy' | 'degraded' | 'down';
  version: string;
  timestamp: string;
  services: {
    database: boolean;
    email: boolean;
    authentication: boolean;
    storage: boolean;
  };
  responseTime: number;
}

export interface DataSyncStatus {
  table: string;
  lastSync: string;
  recordCount: number;
  status: 'synced' | 'pending' | 'error';
  conflicts?: number;
}

export interface AdminPortalEndpoint {
  path: string;
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  description: string;
  critical: boolean;
  authenticated: boolean;
}

export class AdminPortalAPIService {
  private static instance: AdminPortalAPIService;
  private baseURL: string;
  private timeout: number = 10000; // 10 seconds

  private constructor() {
    this.baseURL = API_BASE_URL;
  }

  public static getInstance(): AdminPortalAPIService {
    if (!AdminPortalAPIService.instance) {
      AdminPortalAPIService.instance = new AdminPortalAPIService();
    }
    return AdminPortalAPIService.instance;
  }

  /**
   * Get authentication headers
   */
  private getAuthHeaders(): Record<string, string> {
    const authStore = useAuth.getState();
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    };

    if (authStore.token) {
      headers['Authorization'] = `Bearer ${authStore.token}`;
    }

    return headers;
  }

  /**
   * Make authenticated API request
   */
  private async makeRequest<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<APIResponse<T>> {
    try {
      const url = `${this.baseURL}${endpoint}`;
      console.log(`🌐 Admin Portal API: ${options.method || 'GET'} ${endpoint}`);

      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), this.timeout);

      const response = await fetch(url, {
        ...options,
        headers: {
          ...this.getAuthHeaders(),
          ...options.headers,
        },
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      let responseData: any;
      const contentType = response.headers.get('content-type');
      
      if (contentType && contentType.includes('application/json')) {
        responseData = await response.json();
      } else {
        responseData = await response.text();
      }

      if (!response.ok) {
        console.error(`❌ Admin Portal API error: ${response.status}`, responseData);
        return {
          success: false,
          error: responseData?.error || responseData?.message || `HTTP ${response.status}`,
          status: response.status,
        };
      }

      console.log(`✅ Admin Portal API success: ${endpoint}`);
      return {
        success: true,
        data: responseData,
        status: response.status,
      };

    } catch (error) {
      if (error instanceof Error && error.name === 'AbortError') {
        console.error(`⏰ Admin Portal API timeout: ${endpoint}`);
        return {
          success: false,
          error: 'Request timeout',
          status: 408,
        };
      }

      console.error(`❌ Admin Portal API request failed: ${endpoint}`, error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Network error',
        status: 0,
      };
    }
  }

  /**
   * Test admin portal health
   */
  async testHealth(): Promise<APIResponse<AdminPortalHealthCheck>> {
    const startTime = Date.now();
    
    try {
      const result = await this.makeRequest<AdminPortalHealthCheck>('/health', {
        method: 'GET',
      });

      if (result.success && result.data) {
        result.data.responseTime = Date.now() - startTime;
      }

      return result;

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Health check failed',
        status: 0,
      };
    }
  }

  /**
   * Validate authentication token
   */
  async validateToken(token?: string): Promise<APIResponse<{ valid: boolean; user?: any }>> {
    const authStore = useAuth.getState();
    const tokenToValidate = token || authStore.token;

    if (!tokenToValidate) {
      return {
        success: false,
        error: 'No token provided',
      };
    }

    return this.makeRequest('/auth/validate', {
      method: 'POST',
      body: JSON.stringify({ token: tokenToValidate }),
    });
  }

  /**
   * Refresh authentication token
   */
  async refreshToken(): Promise<APIResponse<{ token: string; user: any }>> {
    const authStore = useAuth.getState();

    if (!authStore.token) {
      return {
        success: false,
        error: 'No token to refresh',
      };
    }

    return this.makeRequest('/auth/refresh', {
      method: 'POST',
      body: JSON.stringify({ token: authStore.token }),
    });
  }

  /**
   * Send email via admin portal
   */
  async sendEmail(emailData: {
    to: string;
    subject: string;
    template: string;
    variables: Record<string, any>;
  }): Promise<APIResponse<{ email_id: string; status: string }>> {
    return this.makeRequest('/email/send', {
      method: 'POST',
      body: JSON.stringify(emailData),
    });
  }

  /**
   * Get email status
   */
  async getEmailStatus(emailId: string): Promise<APIResponse<{ status: string; delivered_at?: string }>> {
    return this.makeRequest(`/email/status/${emailId}`, {
      method: 'GET',
    });
  }

  /**
   * Get data synchronization status
   */
  async getDataSyncStatus(): Promise<APIResponse<DataSyncStatus[]>> {
    return this.makeRequest('/sync/status', {
      method: 'GET',
    });
  }

  /**
   * Trigger data synchronization
   */
  async triggerDataSync(tables?: string[]): Promise<APIResponse<{ sync_id: string; status: string }>> {
    return this.makeRequest('/sync/trigger', {
      method: 'POST',
      body: JSON.stringify({ tables }),
    });
  }

  /**
   * Get admin portal configuration
   */
  async getConfiguration(): Promise<APIResponse<{ version: string; features: string[]; settings: any }>> {
    return this.makeRequest('/config', {
      method: 'GET',
    });
  }

  /**
   * Test CRUD operations for a specific entity
   */
  async testCRUDOperations(entity: 'customers' | 'bookings' | 'quotes' | 'services'): Promise<APIResponse<{
    create: boolean;
    read: boolean;
    update: boolean;
    delete: boolean;
  }>> {
    try {
      const results = {
        create: false,
        read: false,
        update: false,
        delete: false,
      };

      // Test READ operation
      const readResult = await this.makeRequest(`/${entity}?limit=1`, { method: 'GET' });
      results.read = readResult.success;

      // Test CREATE operation (dry run)
      const createResult = await this.makeRequest(`/${entity}/test-create`, { 
        method: 'POST',
        body: JSON.stringify({ test: true }),
      });
      results.create = createResult.success;

      // Test UPDATE operation (dry run)
      const updateResult = await this.makeRequest(`/${entity}/test-update`, { 
        method: 'PUT',
        body: JSON.stringify({ test: true }),
      });
      results.update = updateResult.success;

      // Test DELETE operation (dry run)
      const deleteResult = await this.makeRequest(`/${entity}/test-delete`, { 
        method: 'DELETE',
      });
      results.delete = deleteResult.success;

      return {
        success: true,
        data: results,
      };

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'CRUD test failed',
      };
    }
  }

  /**
   * Get available API endpoints
   */
  getAvailableEndpoints(): AdminPortalEndpoint[] {
    return [
      { path: '/health', method: 'GET', description: 'Health check', critical: true, authenticated: false },
      { path: '/auth/validate', method: 'POST', description: 'Token validation', critical: true, authenticated: true },
      { path: '/auth/refresh', method: 'POST', description: 'Token refresh', critical: false, authenticated: true },
      { path: '/email/send', method: 'POST', description: 'Send email', critical: true, authenticated: true },
      { path: '/email/status/:id', method: 'GET', description: 'Email status', critical: false, authenticated: true },
      { path: '/sync/status', method: 'GET', description: 'Sync status', critical: false, authenticated: true },
      { path: '/sync/trigger', method: 'POST', description: 'Trigger sync', critical: false, authenticated: true },
      { path: '/config', method: 'GET', description: 'Configuration', critical: false, authenticated: true },
      { path: '/customers', method: 'GET', description: 'List customers', critical: true, authenticated: true },
      { path: '/customers', method: 'POST', description: 'Create customer', critical: true, authenticated: true },
      { path: '/customers/:id', method: 'PUT', description: 'Update customer', critical: true, authenticated: true },
      { path: '/customers/:id', method: 'DELETE', description: 'Delete customer', critical: false, authenticated: true },
      { path: '/bookings', method: 'GET', description: 'List bookings', critical: true, authenticated: true },
      { path: '/bookings', method: 'POST', description: 'Create booking', critical: true, authenticated: true },
      { path: '/bookings/:id', method: 'PUT', description: 'Update booking', critical: true, authenticated: true },
      { path: '/bookings/:id', method: 'DELETE', description: 'Delete booking', critical: false, authenticated: true },
      { path: '/quotes', method: 'GET', description: 'List quotes', critical: true, authenticated: true },
      { path: '/quotes', method: 'POST', description: 'Create quote', critical: true, authenticated: true },
      { path: '/quotes/:id', method: 'PUT', description: 'Update quote', critical: true, authenticated: true },
      { path: '/quotes/:id', method: 'DELETE', description: 'Delete quote', critical: false, authenticated: true },
      { path: '/services', method: 'GET', description: 'List services', critical: true, authenticated: true },
      { path: '/services', method: 'POST', description: 'Create service', critical: false, authenticated: true },
      { path: '/services/:id', method: 'PUT', description: 'Update service', critical: false, authenticated: true },
    ];
  }

  /**
   * Test endpoint availability
   */
  async testEndpoint(endpoint: AdminPortalEndpoint): Promise<{
    success: boolean;
    responseTime: number;
    status?: number;
    error?: string;
  }> {
    const startTime = Date.now();

    try {
      // For endpoints with parameters, use test versions
      let testPath = endpoint.path;
      if (testPath.includes(':id')) {
        testPath = testPath.replace(':id', 'test');
      }

      const result = await this.makeRequest(testPath, {
        method: endpoint.method,
        ...(endpoint.method !== 'GET' && { body: JSON.stringify({ test: true }) }),
      });

      return {
        success: result.success,
        responseTime: Date.now() - startTime,
        status: result.status,
        error: result.error,
      };

    } catch (error) {
      return {
        success: false,
        responseTime: Date.now() - startTime,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Get API base URL
   */
  getBaseURL(): string {
    return this.baseURL;
  }

  /**
   * Set API timeout
   */
  setTimeout(timeout: number): void {
    this.timeout = timeout;
  }

  /**
   * Test real-time connectivity
   */
  async testRealTimeConnectivity(): Promise<APIResponse<{ connected: boolean; latency: number }>> {
    const startTime = Date.now();
    
    try {
      const result = await this.makeRequest('/realtime/ping', {
        method: 'GET',
      });

      if (result.success) {
        return {
          success: true,
          data: {
            connected: true,
            latency: Date.now() - startTime,
          },
        };
      }

      return result;

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Real-time test failed',
      };
    }
  }
}

// Export singleton instance
export const adminPortalAPIService = AdminPortalAPIService.getInstance();
