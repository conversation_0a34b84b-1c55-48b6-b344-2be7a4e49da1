module.exports = function(api) {
  api.cache(true);
  return {
    presets: ['babel-preset-expo'],
    plugins: [
      // Module resolver for path aliases
      [
        'module-resolver',
        {
          root: ['./src'],
          alias: {
            '@': './src',
            '@/components': './src/components',
            '@/screens': './src/screens',
            '@/services': './src/services',
            '@/hooks': './src/hooks',
            '@/store': './src/store',
            '@/utils': './src/utils',
            '@/types': './src/types',
            '@/assets': './src/assets',
          },
        },
      ],
      // Required for React Native Reanimated
      'react-native-reanimated/plugin',
    ],
  };
};
