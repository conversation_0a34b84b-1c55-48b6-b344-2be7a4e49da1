/**
 * Ocean Soul Sparkles Mobile App - Admin Portal Integration Validator
 * Validates connectivity and synchronization with admin.oceansoulsparkles.com.au
 */

import { supabase } from '@/services/database/supabase';
import { bookingService } from '@/services/database/bookingService';
import { customerService } from '@/services/database/customerService';
import { serviceService } from '@/services/database/serviceService';
import { quoteService } from '@/services/database/quoteService';
import { useAuth } from '@/store/authStore.minimal';
import Constants from 'expo-constants';

const API_BASE_URL = Constants.expoConfig?.extra?.EXPO_PUBLIC_API_BASE_URL || process.env.EXPO_PUBLIC_API_BASE_URL;

export interface AdminPortalValidationResult {
  test: string;
  status: 'pass' | 'fail' | 'warning';
  message: string;
  duration: number;
  details?: any;
  error?: string;
}

export interface AdminPortalSyncStatus {
  connectivity: boolean;
  authentication: boolean;
  dataSync: boolean;
  apiEndpoints: boolean;
  overall: boolean;
}

export class AdminPortalValidator {
  private static instance: AdminPortalValidator;

  private constructor() {}

  public static getInstance(): AdminPortalValidator {
    if (!AdminPortalValidator.instance) {
      AdminPortalValidator.instance = new AdminPortalValidator();
    }
    return AdminPortalValidator.instance;
  }

  /**
   * Test admin portal API connectivity
   */
  async testAPIConnectivity(): Promise<AdminPortalValidationResult> {
    const startTime = Date.now();
    
    try {
      console.log('🔗 Testing admin portal API connectivity...');

      if (!API_BASE_URL) {
        return {
          test: 'Admin Portal API Connectivity',
          status: 'fail',
          message: 'Admin portal API URL not configured',
          duration: Date.now() - startTime,
          error: 'EXPO_PUBLIC_API_BASE_URL environment variable missing',
        };
      }

      // Test basic connectivity with health check endpoint
      const response = await fetch(`${API_BASE_URL}/health`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        timeout: 10000, // 10 second timeout
      });

      if (response.ok) {
        const data = await response.json();
        return {
          test: 'Admin Portal API Connectivity',
          status: 'pass',
          message: 'Admin portal API is accessible and responding',
          duration: Date.now() - startTime,
          details: {
            url: API_BASE_URL,
            status: response.status,
            responseTime: Date.now() - startTime,
            healthData: data,
          },
        };
      } else {
        return {
          test: 'Admin Portal API Connectivity',
          status: 'warning',
          message: `Admin portal API responded with status ${response.status}`,
          duration: Date.now() - startTime,
          details: {
            url: API_BASE_URL,
            status: response.status,
            statusText: response.statusText,
          },
        };
      }

    } catch (error) {
      // If health endpoint doesn't exist, try a fallback test
      try {
        const fallbackResponse = await fetch(`${API_BASE_URL}/auth/status`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
          timeout: 5000,
        });

        return {
          test: 'Admin Portal API Connectivity',
          status: 'warning',
          message: 'Admin portal API accessible but health endpoint unavailable',
          duration: Date.now() - startTime,
          details: {
            url: API_BASE_URL,
            fallbackStatus: fallbackResponse.status,
            note: 'Consider adding /health endpoint to admin portal',
          },
        };

      } catch (fallbackError) {
        return {
          test: 'Admin Portal API Connectivity',
          status: 'fail',
          message: 'Cannot connect to admin portal API',
          duration: Date.now() - startTime,
          error: error instanceof Error ? error.message : 'Network error',
          details: {
            url: API_BASE_URL,
            originalError: error instanceof Error ? error.message : 'Unknown error',
            fallbackError: fallbackError instanceof Error ? fallbackError.message : 'Unknown error',
          },
        };
      }
    }
  }

  /**
   * Test authentication integration with admin portal
   */
  async testAuthenticationIntegration(): Promise<AdminPortalValidationResult> {
    const startTime = Date.now();
    
    try {
      console.log('🔐 Testing admin portal authentication integration...');

      const authStore = useAuth.getState();
      
      if (!authStore.isAuthenticated || !authStore.token) {
        return {
          test: 'Admin Portal Authentication',
          status: 'warning',
          message: 'No active authentication session to test',
          duration: Date.now() - startTime,
          details: {
            isAuthenticated: authStore.isAuthenticated,
            hasToken: !!authStore.token,
            hasUser: !!authStore.user,
          },
        };
      }

      // Test token validation with admin portal
      const response = await fetch(`${API_BASE_URL}/auth/validate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authStore.token}`,
        },
        body: JSON.stringify({
          token: authStore.token,
        }),
      });

      if (response.ok) {
        const data = await response.json();
        return {
          test: 'Admin Portal Authentication',
          status: 'pass',
          message: 'Authentication token valid with admin portal',
          duration: Date.now() - startTime,
          details: {
            tokenValid: true,
            userEmail: authStore.user?.email,
            userRole: authStore.user?.role,
            validationResponse: data,
          },
        };
      } else {
        return {
          test: 'Admin Portal Authentication',
          status: 'fail',
          message: `Authentication validation failed: ${response.status}`,
          duration: Date.now() - startTime,
          details: {
            status: response.status,
            statusText: response.statusText,
            userEmail: authStore.user?.email,
          },
        };
      }

    } catch (error) {
      return {
        test: 'Admin Portal Authentication',
        status: 'fail',
        message: 'Authentication integration test failed',
        duration: Date.now() - startTime,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Test Supabase database synchronization
   */
  async testDatabaseSynchronization(): Promise<AdminPortalValidationResult> {
    const startTime = Date.now();
    
    try {
      console.log('🗄️ Testing Supabase database synchronization...');

      // Test basic Supabase connectivity
      const { data: supabaseHealth, error: supabaseError } = await supabase
        .from('admin_users')
        .select('count')
        .limit(1);

      if (supabaseError) {
        return {
          test: 'Database Synchronization',
          status: 'fail',
          message: 'Supabase database connection failed',
          duration: Date.now() - startTime,
          error: supabaseError.message,
          details: {
            supabaseError: supabaseError,
          },
        };
      }

      // Test data consistency across key tables
      const consistencyTests = await Promise.allSettled([
        bookingService.getBookings({ limit: 5 }),
        customerService.getCustomers({ limit: 5 }),
        serviceService.getServices({ limit: 5 }),
        quoteService.getQuotes({ limit: 5 }),
      ]);

      const results = consistencyTests.map((result, index) => {
        const tableNames = ['bookings', 'customers', 'services', 'quotes'];
        return {
          table: tableNames[index],
          status: result.status,
          success: result.status === 'fulfilled' && !result.value.error,
          error: result.status === 'rejected' ? result.reason : 
                 result.status === 'fulfilled' && result.value.error ? result.value.error.message : null,
          recordCount: result.status === 'fulfilled' && result.value.data ? result.value.data.length : 0,
        };
      });

      const successfulTables = results.filter(r => r.success).length;
      const totalTables = results.length;

      return {
        test: 'Database Synchronization',
        status: successfulTables === totalTables ? 'pass' : 
                successfulTables > 0 ? 'warning' : 'fail',
        message: `Database sync: ${successfulTables}/${totalTables} tables accessible`,
        duration: Date.now() - startTime,
        details: {
          tableResults: results,
          successRate: (successfulTables / totalTables) * 100,
          supabaseConnected: !supabaseError,
        },
      };

    } catch (error) {
      return {
        test: 'Database Synchronization',
        status: 'fail',
        message: 'Database synchronization test failed',
        duration: Date.now() - startTime,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Test admin portal API endpoints
   */
  async testAPIEndpoints(): Promise<AdminPortalValidationResult> {
    const startTime = Date.now();
    
    try {
      console.log('🔌 Testing admin portal API endpoints...');

      const authStore = useAuth.getState();
      const headers = {
        'Content-Type': 'application/json',
        ...(authStore.token && { 'Authorization': `Bearer ${authStore.token}` }),
      };

      // Test key API endpoints that mobile app uses
      const endpointTests = [
        { name: 'Email Send', path: '/email/send', method: 'POST', critical: true },
        { name: 'Email Status', path: '/email/status/test', method: 'GET', critical: false },
        { name: 'Auth Validate', path: '/auth/validate', method: 'POST', critical: true },
        { name: 'Auth Refresh', path: '/auth/refresh', method: 'POST', critical: false },
      ];

      const results = await Promise.allSettled(
        endpointTests.map(async (endpoint) => {
          try {
            const response = await fetch(`${API_BASE_URL}${endpoint.path}`, {
              method: endpoint.method,
              headers,
              ...(endpoint.method === 'POST' && { 
                body: JSON.stringify({ test: true }) 
              }),
            });

            return {
              ...endpoint,
              status: response.status,
              available: response.status < 500, // 4xx is expected for test calls, 5xx indicates server issues
              responseTime: Date.now() - startTime,
            };
          } catch (error) {
            return {
              ...endpoint,
              status: 0,
              available: false,
              error: error instanceof Error ? error.message : 'Unknown error',
            };
          }
        })
      );

      const endpointResults = results.map((result, index) => {
        if (result.status === 'fulfilled') {
          return result.value;
        } else {
          return {
            ...endpointTests[index],
            status: 0,
            available: false,
            error: result.reason instanceof Error ? result.reason.message : 'Test failed',
          };
        }
      });

      const availableEndpoints = endpointResults.filter(r => r.available).length;
      const criticalEndpoints = endpointResults.filter(r => r.critical && r.available).length;
      const totalCritical = endpointResults.filter(r => r.critical).length;

      const status = criticalEndpoints === totalCritical ? 'pass' :
                    criticalEndpoints > 0 ? 'warning' : 'fail';

      return {
        test: 'Admin Portal API Endpoints',
        status,
        message: `API endpoints: ${availableEndpoints}/${endpointResults.length} available, ${criticalEndpoints}/${totalCritical} critical endpoints working`,
        duration: Date.now() - startTime,
        details: {
          endpointResults,
          availableCount: availableEndpoints,
          criticalCount: criticalEndpoints,
          totalEndpoints: endpointResults.length,
          totalCritical,
        },
      };

    } catch (error) {
      return {
        test: 'Admin Portal API Endpoints',
        status: 'fail',
        message: 'API endpoints test failed',
        duration: Date.now() - startTime,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Get overall admin portal sync status
   */
  async getAdminPortalSyncStatus(): Promise<AdminPortalSyncStatus> {
    try {
      const [connectivity, authentication, dataSync, apiEndpoints] = await Promise.all([
        this.testAPIConnectivity(),
        this.testAuthenticationIntegration(),
        this.testDatabaseSynchronization(),
        this.testAPIEndpoints(),
      ]);

      const status = {
        connectivity: connectivity.status === 'pass',
        authentication: authentication.status === 'pass',
        dataSync: dataSync.status === 'pass',
        apiEndpoints: apiEndpoints.status === 'pass',
        overall: false,
      };

      // Overall status is true if all critical components are working
      status.overall = status.connectivity && status.dataSync && 
                      (status.authentication || status.apiEndpoints);

      return status;

    } catch (error) {
      console.error('❌ Failed to get admin portal sync status:', error);
      return {
        connectivity: false,
        authentication: false,
        dataSync: false,
        apiEndpoints: false,
        overall: false,
      };
    }
  }

  /**
   * Run comprehensive admin portal integration validation
   */
  async runComprehensiveValidation(): Promise<AdminPortalValidationResult[]> {
    console.log('🧪 Running comprehensive admin portal integration validation...');

    const results = await Promise.all([
      this.testAPIConnectivity(),
      this.testAuthenticationIntegration(),
      this.testDatabaseSynchronization(),
      this.testAPIEndpoints(),
    ]);

    const passedTests = results.filter(r => r.status === 'pass').length;
    const totalTests = results.length;

    console.log(`✅ Admin portal validation completed: ${passedTests}/${totalTests} tests passed`);

    return results;
  }
}

// Export singleton instance
export const adminPortalValidator = AdminPortalValidator.getInstance();
