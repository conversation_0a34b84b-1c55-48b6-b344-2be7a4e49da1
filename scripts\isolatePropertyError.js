/**
 * Ocean Soul Sparkles Mobile App - Property Error Isolation
 * Systematically tests each service to isolate the estimated_total property error
 */

console.log('🔍 Isolating estimated_total Property Error...');

// Test 1: Check if the error occurs during module imports
console.log('\n📦 Test 1: Testing module imports...');

try {
  console.log('Testing database types import...');
  const databaseTypes = require('../src/types/database');
  console.log('✅ Database types imported successfully');
  
  // Check Quote interface
  if (databaseTypes.Quote) {
    console.log('✅ Quote interface available');
  } else {
    console.log('❌ Quote interface not available');
  }
} catch (error) {
  console.log('❌ Database types import failed:', error.message);
}

try {
  console.log('Testing email templates import...');
  const emailTemplates = require('../src/services/email/emailTemplates');
  console.log('✅ Email templates imported successfully');
  
  // Test template access
  const quoteTemplate = emailTemplates.getFallbackTemplate('quote');
  if (quoteTemplate) {
    console.log('✅ Quote template accessible');
    console.log('Template variables:', quoteTemplate.variables);
  } else {
    console.log('❌ Quote template not accessible');
  }
} catch (error) {
  console.log('❌ Email templates import failed:', error.message);
}

// Test 2: Check if the error occurs when creating Quote objects
console.log('\n🏗️ Test 2: Testing Quote object creation...');

try {
  // Create a Quote object with the new interface structure
  const testQuote = {
    id: 'test-quote-id',
    customer_id: 'test-customer-id',
    customer_first_name: 'John',
    customer_last_name: 'Doe',
    customer_email: '<EMAIL>',
    customer_phone: '+61123456789',
    service_name: 'Test Service',
    service_description: 'Test service description',
    estimated_total: 150.00,
    status: 'pending',
    source: 'mobile_app',
    number_of_people: 1,
    base_price: 100.00,
    travel_cost: 25.00,
    additional_costs: 25.00,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  };
  
  console.log('✅ Quote object created successfully');
  
  // Test property access
  const estimatedTotal = testQuote.estimated_total;
  console.log('✅ estimated_total property access works:', estimatedTotal);
  
  const serviceName = testQuote.service_name;
  console.log('✅ service_name property access works:', serviceName);
  
} catch (error) {
  console.log('❌ Quote object creation/access failed:', error.message);
}

// Test 3: Check if the error occurs in email service imports
console.log('\n📧 Test 3: Testing email service imports...');

try {
  console.log('Testing email service import...');
  // This might be where the error occurs
  const emailService = require('../src/services/email/emailService');
  console.log('✅ Email service imported successfully');
} catch (error) {
  console.log('❌ Email service import failed:', error.message);
  console.log('Error stack:', error.stack);
}

try {
  console.log('Testing quote email service import...');
  // This might also be where the error occurs
  const quoteEmailService = require('../src/services/email/quoteEmailService');
  console.log('✅ Quote email service imported successfully');
} catch (error) {
  console.log('❌ Quote email service import failed:', error.message);
  console.log('Error stack:', error.stack);
}

// Test 4: Check if the error occurs in quote service imports
console.log('\n📋 Test 4: Testing quote service imports...');

try {
  console.log('Testing quote service import...');
  const quoteService = require('../src/services/database/quoteService');
  console.log('✅ Quote service imported successfully');
} catch (error) {
  console.log('❌ Quote service import failed:', error.message);
  console.log('Error stack:', error.stack);
}

// Test 5: Check if the error occurs in screen imports
console.log('\n📱 Test 5: Testing screen imports...');

try {
  console.log('Testing QuotesScreen import...');
  // This is likely where the error occurs since it imports quote services
  const QuotesScreen = require('../src/screens/quotes/QuotesScreen');
  console.log('✅ QuotesScreen imported successfully');
} catch (error) {
  console.log('❌ QuotesScreen import failed:', error.message);
  console.log('Error stack:', error.stack);
}

try {
  console.log('Testing QuoteDetailScreen import...');
  const QuoteDetailScreen = require('../src/screens/quotes/QuoteDetailScreen');
  console.log('✅ QuoteDetailScreen imported successfully');
} catch (error) {
  console.log('❌ QuoteDetailScreen import failed:', error.message);
  console.log('Error stack:', error.stack);
}

// Test 6: Check if the error occurs in navigation imports
console.log('\n🧭 Test 6: Testing navigation imports...');

try {
  console.log('Testing RoleBasedNavigation import...');
  const RoleBasedNavigation = require('../src/components/navigation/RoleBasedNavigation');
  console.log('✅ RoleBasedNavigation imported successfully');
} catch (error) {
  console.log('❌ RoleBasedNavigation import failed:', error.message);
  console.log('Error stack:', error.stack);
}

// Test 7: Check if the error occurs in App import
console.log('\n📱 Test 7: Testing App import...');

try {
  console.log('Testing App component import...');
  const App = require('../App');
  console.log('✅ App component imported successfully');
} catch (error) {
  console.log('❌ App component import failed:', error.message);
  console.log('Error stack:', error.stack);
}

console.log('\n🎯 Property Error Isolation Complete');
console.log('='.repeat(50));
console.log('If any of the above tests failed, that\'s likely where the estimated_total property error is occurring.');
console.log('The error happens during module loading, not during function execution.');
