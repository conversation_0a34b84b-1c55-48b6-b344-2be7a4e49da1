/**
 * Ocean Soul Sparkles Mobile App - Query Performance Monitor
 * Monitors database query performance and identifies bottlenecks
 */

interface QueryMetrics {
  queryName: string;
  duration: number;
  recordCount: number;
  timestamp: Date;
  table: string;
  hasFilters: boolean;
  hasSearch: boolean;
  limit?: number;
}

interface PerformanceThresholds {
  warning: number; // milliseconds
  critical: number; // milliseconds
}

class QueryPerformanceMonitor {
  private metrics: QueryMetrics[] = [];
  private readonly MAX_METRICS = 100; // Keep last 100 queries
  private readonly thresholds: PerformanceThresholds = {
    warning: 1000, // 1 second
    critical: 3000, // 3 seconds
  };

  /**
   * Monitor a database query execution
   */
  async monitorQuery<T>(
    queryName: string,
    table: string,
    queryFunction: () => Promise<T>,
    options: {
      hasFilters?: boolean;
      hasSearch?: boolean;
      limit?: number;
    } = {}
  ): Promise<T> {
    const startTime = Date.now();
    
    try {
      const result = await queryFunction();
      const duration = Date.now() - startTime;
      
      // Extract record count from result
      let recordCount = 0;
      if (result && typeof result === 'object') {
        if ('data' in result && Array.isArray((result as any).data)) {
          recordCount = (result as any).data.length;
        } else if (Array.isArray(result)) {
          recordCount = result.length;
        }
      }
      
      // Record metrics
      this.recordMetrics({
        queryName,
        duration,
        recordCount,
        timestamp: new Date(),
        table,
        hasFilters: options.hasFilters || false,
        hasSearch: options.hasSearch || false,
        limit: options.limit,
      });
      
      // Log performance warnings
      this.checkPerformance(queryName, duration, recordCount);
      
      return result;
    } catch (error) {
      const duration = Date.now() - startTime;
      console.error(`❌ Query ${queryName} failed after ${duration}ms:`, error);
      throw error;
    }
  }

  /**
   * Record query metrics
   */
  private recordMetrics(metrics: QueryMetrics): void {
    this.metrics.push(metrics);
    
    // Keep only the most recent metrics
    if (this.metrics.length > this.MAX_METRICS) {
      this.metrics.shift();
    }
  }

  /**
   * Check query performance and log warnings
   */
  private checkPerformance(queryName: string, duration: number, recordCount: number): void {
    if (duration >= this.thresholds.critical) {
      console.warn(`🚨 CRITICAL: Query ${queryName} took ${duration}ms (${recordCount} records)`);
    } else if (duration >= this.thresholds.warning) {
      console.warn(`⚠️ SLOW: Query ${queryName} took ${duration}ms (${recordCount} records)`);
    } else {
      console.log(`✅ Query ${queryName} completed in ${duration}ms (${recordCount} records)`);
    }
  }

  /**
   * Get performance statistics
   */
  getPerformanceStats(): {
    totalQueries: number;
    averageDuration: number;
    slowQueries: QueryMetrics[];
    topSlowTables: { table: string; avgDuration: number; queryCount: number }[];
  } {
    if (this.metrics.length === 0) {
      return {
        totalQueries: 0,
        averageDuration: 0,
        slowQueries: [],
        topSlowTables: [],
      };
    }

    const totalDuration = this.metrics.reduce((sum, m) => sum + m.duration, 0);
    const averageDuration = totalDuration / this.metrics.length;
    
    const slowQueries = this.metrics
      .filter(m => m.duration >= this.thresholds.warning)
      .sort((a, b) => b.duration - a.duration)
      .slice(0, 10);

    // Group by table and calculate averages
    const tableStats = new Map<string, { totalDuration: number; count: number }>();
    this.metrics.forEach(m => {
      const existing = tableStats.get(m.table) || { totalDuration: 0, count: 0 };
      tableStats.set(m.table, {
        totalDuration: existing.totalDuration + m.duration,
        count: existing.count + 1,
      });
    });

    const topSlowTables = Array.from(tableStats.entries())
      .map(([table, stats]) => ({
        table,
        avgDuration: stats.totalDuration / stats.count,
        queryCount: stats.count,
      }))
      .sort((a, b) => b.avgDuration - a.avgDuration)
      .slice(0, 5);

    return {
      totalQueries: this.metrics.length,
      averageDuration,
      slowQueries,
      topSlowTables,
    };
  }

  /**
   * Get optimization recommendations
   */
  getOptimizationRecommendations(): string[] {
    const stats = this.getPerformanceStats();
    const recommendations: string[] = [];

    if (stats.averageDuration > this.thresholds.warning) {
      recommendations.push('Overall query performance is slow. Consider adding database indexes.');
    }

    stats.slowQueries.forEach(query => {
      if (!query.limit || query.limit > 100) {
        recommendations.push(`Query ${query.queryName} should use pagination (limit: ${query.limit || 'none'})`);
      }
      
      if (query.hasSearch && query.duration > 500) {
        recommendations.push(`Search query ${query.queryName} is slow. Consider full-text search indexes.`);
      }
      
      if (query.recordCount > 1000) {
        recommendations.push(`Query ${query.queryName} returns too many records (${query.recordCount}). Use pagination.`);
      }
    });

    stats.topSlowTables.forEach(table => {
      if (table.avgDuration > this.thresholds.warning) {
        recommendations.push(`Table ${table.table} has slow average query time (${Math.round(table.avgDuration)}ms). Check indexes.`);
      }
    });

    return recommendations;
  }

  /**
   * Clear all metrics
   */
  clearMetrics(): void {
    this.metrics = [];
    console.log('🧹 Query performance metrics cleared');
  }

  /**
   * Export metrics for analysis
   */
  exportMetrics(): QueryMetrics[] {
    return [...this.metrics];
  }
}

// Export singleton instance
export const queryPerformanceMonitor = new QueryPerformanceMonitor();
