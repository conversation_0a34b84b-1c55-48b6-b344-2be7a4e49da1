/**
 * Ocean Soul Sparkles Mobile App - Database Schema Validator
 * Validates database schema integrity, table structure, and constraints
 */

import { supabase } from '@/services/database/supabase';

export interface SchemaValidationResult {
  test: string;
  status: 'pass' | 'fail' | 'warning';
  message: string;
  duration: number;
  details?: any;
  error?: string;
}

export interface TableSchema {
  table_name: string;
  column_name: string;
  data_type: string;
  is_nullable: string;
  column_default: string | null;
  constraint_type?: string;
}

export interface DatabaseSchemaStatus {
  coreTablesExist: boolean;
  transactionTablesExist: boolean;
  constraintsValid: boolean;
  indexesOptimal: boolean;
  rlsPoliciesActive: boolean;
  overall: boolean;
}

export class DatabaseSchemaValidator {
  private static instance: DatabaseSchemaValidator;

  // Expected core tables for Ocean Soul Sparkles
  private readonly CORE_TABLES = [
    'admin_users',
    'customers', 
    'services',
    'bookings',
    'quotes',
    'email_templates',
  ];

  // Expected transaction tables
  private readonly TRANSACTION_TABLES = [
    'transactions',
    'transaction_items',
  ];

  // Expected SQL functions
  private readonly EXPECTED_FUNCTIONS = [
    'update_updated_at_column',
  ];

  private constructor() {}

  public static getInstance(): DatabaseSchemaValidator {
    if (!DatabaseSchemaValidator.instance) {
      DatabaseSchemaValidator.instance = new DatabaseSchemaValidator();
    }
    return DatabaseSchemaValidator.instance;
  }

  /**
   * Validate core table existence and structure
   */
  async validateCoreTablesExistence(): Promise<SchemaValidationResult> {
    const startTime = Date.now();
    
    try {
      console.log('🗄️ Validating core database tables...');

      // Query to check table existence
      const { data: tables, error } = await supabase
        .rpc('get_table_info', {})
        .catch(async () => {
          // Fallback query if RPC doesn't exist
          return await supabase
            .from('information_schema.tables')
            .select('table_name')
            .eq('table_schema', 'public')
            .in('table_name', this.CORE_TABLES);
        });

      if (error) {
        // Try alternative approach using raw SQL
        const { data: alternativeTables, error: altError } = await supabase
          .rpc('exec_sql', { 
            sql: `
              SELECT table_name 
              FROM information_schema.tables 
              WHERE table_schema = 'public' 
              AND table_name = ANY($1)
            `,
            params: [this.CORE_TABLES]
          })
          .catch(() => ({ data: null, error: 'RPC not available' }));

        if (altError || !alternativeTables) {
          // Manual check by attempting to query each table
          const tableChecks = await Promise.allSettled(
            this.CORE_TABLES.map(async (tableName) => {
              const { error } = await supabase
                .from(tableName)
                .select('*')
                .limit(1);
              return { table_name: tableName, exists: !error };
            })
          );

          const existingTables = tableChecks
            .filter(result => result.status === 'fulfilled')
            .map(result => (result as PromiseFulfilledResult<any>).value)
            .filter(table => table.exists);

          const missingTables = this.CORE_TABLES.filter(
            table => !existingTables.some(existing => existing.table_name === table)
          );

          return {
            test: 'Core Tables Existence',
            status: missingTables.length === 0 ? 'pass' : 'fail',
            message: missingTables.length === 0 
              ? `All ${this.CORE_TABLES.length} core tables exist`
              : `Missing core tables: ${missingTables.join(', ')}`,
            duration: Date.now() - startTime,
            details: {
              expectedTables: this.CORE_TABLES,
              existingTables: existingTables.map(t => t.table_name),
              missingTables,
              totalExpected: this.CORE_TABLES.length,
              totalExisting: existingTables.length,
            },
          };
        }
      }

      const existingTableNames = tables?.map((t: any) => t.table_name) || [];
      const missingTables = this.CORE_TABLES.filter(
        table => !existingTableNames.includes(table)
      );

      return {
        test: 'Core Tables Existence',
        status: missingTables.length === 0 ? 'pass' : 'fail',
        message: missingTables.length === 0 
          ? `All ${this.CORE_TABLES.length} core tables exist`
          : `Missing core tables: ${missingTables.join(', ')}`,
        duration: Date.now() - startTime,
        details: {
          expectedTables: this.CORE_TABLES,
          existingTables: existingTableNames,
          missingTables,
          totalExpected: this.CORE_TABLES.length,
          totalExisting: existingTableNames.length,
        },
      };

    } catch (error) {
      return {
        test: 'Core Tables Existence',
        status: 'fail',
        message: 'Failed to validate core tables',
        duration: Date.now() - startTime,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Validate transaction tables existence
   */
  async validateTransactionTables(): Promise<SchemaValidationResult> {
    const startTime = Date.now();
    
    try {
      console.log('💳 Validating transaction tables...');

      // Check transaction tables existence
      const tableChecks = await Promise.allSettled(
        this.TRANSACTION_TABLES.map(async (tableName) => {
          const { data, error } = await supabase
            .from(tableName)
            .select('*')
            .limit(1);
          return { table_name: tableName, exists: !error, error: error?.message };
        })
      );

      const results = tableChecks.map((result, index) => {
        if (result.status === 'fulfilled') {
          return result.value;
        } else {
          return { 
            table_name: this.TRANSACTION_TABLES[index], 
            exists: false, 
            error: result.reason 
          };
        }
      });

      const existingTables = results.filter(table => table.exists);
      const missingTables = results.filter(table => !table.exists);

      return {
        test: 'Transaction Tables Existence',
        status: missingTables.length === 0 ? 'pass' : 'warning',
        message: missingTables.length === 0 
          ? `All ${this.TRANSACTION_TABLES.length} transaction tables exist`
          : `Missing transaction tables: ${missingTables.map(t => t.table_name).join(', ')}`,
        duration: Date.now() - startTime,
        details: {
          expectedTables: this.TRANSACTION_TABLES,
          existingTables: existingTables.map(t => t.table_name),
          missingTables: missingTables.map(t => t.table_name),
          totalExpected: this.TRANSACTION_TABLES.length,
          totalExisting: existingTables.length,
          errors: missingTables.filter(t => t.error).map(t => ({ table: t.table_name, error: t.error })),
        },
      };

    } catch (error) {
      return {
        test: 'Transaction Tables Existence',
        status: 'fail',
        message: 'Failed to validate transaction tables',
        duration: Date.now() - startTime,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Validate table constraints and relationships
   */
  async validateTableConstraints(): Promise<SchemaValidationResult> {
    const startTime = Date.now();
    
    try {
      console.log('🔗 Validating table constraints and relationships...');

      // Test foreign key relationships by attempting to query with joins
      const relationshipTests = [
        {
          name: 'Bookings-Customers Relationship',
          test: async () => {
            const { data, error } = await supabase
              .from('bookings')
              .select('id, customer_id, customers(id, full_name)')
              .limit(1);
            return { success: !error, error: error?.message };
          }
        },
        {
          name: 'Bookings-Services Relationship',
          test: async () => {
            const { data, error } = await supabase
              .from('bookings')
              .select('id, service_id, services(id, name)')
              .limit(1);
            return { success: !error, error: error?.message };
          }
        },
        {
          name: 'Quotes-Customers Relationship',
          test: async () => {
            const { data, error } = await supabase
              .from('quotes')
              .select('id, customer_id, customers(id, full_name)')
              .limit(1);
            return { success: !error, error: error?.message };
          }
        },
        {
          name: 'Bookings-Staff Relationship',
          test: async () => {
            const { data, error } = await supabase
              .from('bookings')
              .select('id, staff_id, admin_users(id, email)')
              .limit(1);
            return { success: !error, error: error?.message };
          }
        },
      ];

      const results = await Promise.allSettled(
        relationshipTests.map(async (test) => {
          try {
            const result = await test.test();
            return { name: test.name, ...result };
          } catch (error) {
            return { 
              name: test.name, 
              success: false, 
              error: error instanceof Error ? error.message : 'Unknown error' 
            };
          }
        })
      );

      const constraintResults = results.map((result, index) => {
        if (result.status === 'fulfilled') {
          return result.value;
        } else {
          return { 
            name: relationshipTests[index].name, 
            success: false, 
            error: result.reason 
          };
        }
      });

      const successfulConstraints = constraintResults.filter(r => r.success).length;
      const failedConstraints = constraintResults.filter(r => !r.success);

      return {
        test: 'Table Constraints Validation',
        status: failedConstraints.length === 0 ? 'pass' : 'warning',
        message: failedConstraints.length === 0 
          ? `All ${constraintResults.length} relationship constraints valid`
          : `${failedConstraints.length} constraint issues found`,
        duration: Date.now() - startTime,
        details: {
          totalConstraints: constraintResults.length,
          successfulConstraints,
          failedConstraints: failedConstraints.map(c => ({ name: c.name, error: c.error })),
          constraintResults,
        },
      };

    } catch (error) {
      return {
        test: 'Table Constraints Validation',
        status: 'fail',
        message: 'Failed to validate table constraints',
        duration: Date.now() - startTime,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Validate Row Level Security (RLS) policies
   */
  async validateRLSPolicies(): Promise<SchemaValidationResult> {
    const startTime = Date.now();
    
    try {
      console.log('🔒 Validating Row Level Security policies...');

      // Test RLS by checking if policies are enabled on key tables
      const rlsTests = await Promise.allSettled(
        this.CORE_TABLES.map(async (tableName) => {
          try {
            // Attempt to query the table - if RLS is properly configured, this should work
            const { data, error } = await supabase
              .from(tableName)
              .select('*')
              .limit(1);
            
            return { 
              table: tableName, 
              accessible: !error, 
              error: error?.message,
              hasRLS: true // If we can query, RLS is likely configured
            };
          } catch (error) {
            return { 
              table: tableName, 
              accessible: false, 
              error: error instanceof Error ? error.message : 'Unknown error',
              hasRLS: false
            };
          }
        })
      );

      const rlsResults = rlsTests.map((result, index) => {
        if (result.status === 'fulfilled') {
          return result.value;
        } else {
          return { 
            table: this.CORE_TABLES[index], 
            accessible: false, 
            hasRLS: false,
            error: result.reason 
          };
        }
      });

      const accessibleTables = rlsResults.filter(r => r.accessible).length;
      const inaccessibleTables = rlsResults.filter(r => !r.accessible);

      return {
        test: 'RLS Policies Validation',
        status: inaccessibleTables.length === 0 ? 'pass' : 'warning',
        message: inaccessibleTables.length === 0 
          ? `All ${this.CORE_TABLES.length} tables accessible with proper RLS`
          : `${inaccessibleTables.length} tables have RLS access issues`,
        duration: Date.now() - startTime,
        details: {
          totalTables: this.CORE_TABLES.length,
          accessibleTables,
          inaccessibleTables: inaccessibleTables.map(t => ({ table: t.table, error: t.error })),
          rlsResults,
        },
      };

    } catch (error) {
      return {
        test: 'RLS Policies Validation',
        status: 'fail',
        message: 'Failed to validate RLS policies',
        duration: Date.now() - startTime,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Get overall database schema status
   */
  async getDatabaseSchemaStatus(): Promise<DatabaseSchemaStatus> {
    try {
      const [coreTablesResult, transactionTablesResult, constraintsResult, rlsResult] = await Promise.all([
        this.validateCoreTablesExistence(),
        this.validateTransactionTables(),
        this.validateTableConstraints(),
        this.validateRLSPolicies(),
      ]);

      const status = {
        coreTablesExist: coreTablesResult.status === 'pass',
        transactionTablesExist: transactionTablesResult.status === 'pass',
        constraintsValid: constraintsResult.status === 'pass',
        indexesOptimal: true, // Placeholder - will be implemented in next component
        rlsPoliciesActive: rlsResult.status === 'pass',
        overall: false,
      };

      // Overall status is true if core functionality is working
      status.overall = status.coreTablesExist && status.constraintsValid && status.rlsPoliciesActive;

      return status;

    } catch (error) {
      console.error('❌ Failed to get database schema status:', error);
      return {
        coreTablesExist: false,
        transactionTablesExist: false,
        constraintsValid: false,
        indexesOptimal: false,
        rlsPoliciesActive: false,
        overall: false,
      };
    }
  }

  /**
   * Run comprehensive database schema validation
   */
  async runComprehensiveSchemaValidation(): Promise<SchemaValidationResult[]> {
    console.log('🧪 Running comprehensive database schema validation...');

    const results = await Promise.all([
      this.validateCoreTablesExistence(),
      this.validateTransactionTables(),
      this.validateTableConstraints(),
      this.validateRLSPolicies(),
    ]);

    const passedTests = results.filter(r => r.status === 'pass').length;
    const totalTests = results.length;

    console.log(`✅ Database schema validation completed: ${passedTests}/${totalTests} tests passed`);

    return results;
  }
}

// Export singleton instance
export const databaseSchemaValidator = DatabaseSchemaValidator.getInstance();
