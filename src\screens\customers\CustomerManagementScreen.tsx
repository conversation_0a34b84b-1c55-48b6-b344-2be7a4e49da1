/**
 * Ocean Soul Sparkles Mobile App - Customer Management Screen
 * Complete customer management with CRUD operations
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  FlatList,
  TouchableOpacity,
  TextInput,
  ActivityIndicator,
  Alert,
  RefreshControl,
} from 'react-native';
import { Customer } from '@/types/database';
import { customerService } from '@/services/database/customerService';
import AddCustomerModal from '@/components/customers/AddCustomerModal';
import EditCustomerModal from '@/components/customers/EditCustomerModal';
import CustomerDetailModal from '@/components/customers/CustomerDetailModal';

const CustomerManagementScreen: React.FC = () => {
  // Data state
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [filteredCustomers, setFilteredCustomers] = useState<Customer[]>([]);
  
  // UI state
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  
  // Modal state
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null);

  // Statistics
  const [stats, setStats] = useState({
    total: 0,
    withEmail: 0,
    withPhone: 0,
    recentlyAdded: 0,
  });

  useEffect(() => {
    loadCustomers();
  }, []);

  useEffect(() => {
    filterCustomers();
  }, [customers, searchQuery]);

  const loadCustomers = async (isRefresh = false) => {
    try {
      if (isRefresh) {
        setRefreshing(true);
      } else {
        setLoading(true);
      }

      const result = await customerService.getCustomers({
        order_by: 'name',
        order_direction: 'asc',
      });

      if (result.error) {
        console.error('Error loading customers:', result.error);
        Alert.alert('Error', 'Failed to load customers');
      } else {
        const customerData = result.data || [];
        setCustomers(customerData);
        calculateStats(customerData);
      }
    } catch (error) {
      console.error('Load customers error:', error);
      Alert.alert('Error', 'Failed to load customers');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const filterCustomers = () => {
    if (!searchQuery.trim()) {
      setFilteredCustomers(customers);
      return;
    }

    const query = searchQuery.toLowerCase().trim();
    const filtered = customers.filter(customer =>
      (customer.full_name || customer.name || '').toLowerCase().includes(query) ||
      customer.email?.toLowerCase().includes(query) ||
      customer.phone?.toLowerCase().includes(query)
    );
    setFilteredCustomers(filtered);
  };

  const calculateStats = (customerData: Customer[]) => {
    const now = new Date();
    const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);

    setStats({
      total: customerData.length,
      withEmail: customerData.filter(c => c.email).length,
      withPhone: customerData.filter(c => c.phone).length,
      recentlyAdded: customerData.filter(c => 
        new Date(c.created_at) > thirtyDaysAgo
      ).length,
    });
  };

  const handleCustomerPress = (customer: Customer) => {
    setSelectedCustomer(customer);
    setShowDetailModal(true);
  };

  const handleEditCustomer = (customer: Customer) => {
    setSelectedCustomer(customer);
    setShowEditModal(true);
  };

  const handleDeleteCustomer = (customer: Customer) => {
    Alert.alert(
      'Delete Customer',
      `Are you sure you want to delete ${customer.full_name || customer.name || 'this customer'}? This action cannot be undone.`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              const result = await customerService.deleteCustomer(customer.id);
              if (result.error) {
                throw new Error(result.error.message);
              }
              Alert.alert('Success', 'Customer deleted successfully');
              loadCustomers();
            } catch (error) {
              console.error('Delete customer error:', error);
              Alert.alert('Error', 'Failed to delete customer');
            }
          }
        }
      ]
    );
  };

  const runCRUDTests = async () => {
    Alert.alert(
      'Customer CRUD Tests',
      'This will test Create, Read, Update, and Delete operations for customer management. Continue?',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Run Tests', onPress: performCRUDTests },
      ]
    );
  };

  const performCRUDTests = async () => {
    let testResults: string[] = [];
    let testCustomerId: string | null = null;

    try {
      // Test 1: CREATE - Create a test customer
      console.log('🧪 Testing CREATE operation...');
      const createResult = await customerService.createCustomer({
        full_name: 'Test Customer CRUD',
        email: '<EMAIL>',
        phone: '+61 400 000 000',
        notes: 'Test customer created for CRUD validation',
      });

      if (createResult.error) {
        testResults.push('❌ CREATE: Failed - ' + createResult.error.message);
      } else if (createResult.data) {
        testCustomerId = createResult.data.id;
        testResults.push('✅ CREATE: Success - Customer created');
      }

      // Test 2: READ - Get the created customer
      if (testCustomerId) {
        console.log('🧪 Testing READ operation...');
        const readResult = await customerService.getCustomerById(testCustomerId);

        if (readResult.error) {
          testResults.push('❌ READ: Failed - ' + readResult.error.message);
        } else if (readResult.data) {
          testResults.push('✅ READ: Success - Customer retrieved');
        }
      }

      // Test 3: UPDATE - Update the test customer
      if (testCustomerId) {
        console.log('🧪 Testing UPDATE operation...');
        const updateResult = await customerService.updateCustomer(testCustomerId, {
          full_name: 'Test Customer CRUD Updated',
          notes: 'Test customer updated for CRUD validation',
        });

        if (updateResult.error) {
          testResults.push('❌ UPDATE: Failed - ' + updateResult.error.message);
        } else if (updateResult.data) {
          testResults.push('✅ UPDATE: Success - Customer updated');
        }
      }

      // Test 4: DELETE - Delete the test customer
      if (testCustomerId) {
        console.log('🧪 Testing DELETE operation...');
        const deleteResult = await customerService.deleteCustomer(testCustomerId);

        if (deleteResult.error) {
          testResults.push('❌ DELETE: Failed - ' + deleteResult.error.message);
        } else {
          testResults.push('✅ DELETE: Success - Customer deleted');
        }
      }

      // Test 5: LIST - Get all customers
      console.log('🧪 Testing LIST operation...');
      const listResult = await customerService.getCustomers({
        order_by: 'name',
        order_direction: 'asc',
        limit: 10,
      });

      if (listResult.error) {
        testResults.push('❌ LIST: Failed - ' + listResult.error.message);
      } else {
        testResults.push(`✅ LIST: Success - ${listResult.data?.length || 0} customers retrieved`);
      }

    } catch (error) {
      testResults.push('❌ GENERAL: Unexpected error - ' + error);
    }

    // Show test results
    const resultsText = testResults.join('\n');
    Alert.alert(
      'CRUD Test Results 📋',
      resultsText,
      [
        {
          text: 'OK',
          onPress: () => loadCustomers(), // Refresh the customer list
        },
      ]
    );

    console.log('🧪 Customer CRUD Tests completed:', testResults);
  };

  const renderCustomerCard = ({ item }: { item: Customer }) => (
    <TouchableOpacity
      style={styles.customerCard}
      onPress={() => handleCustomerPress(item)}
      activeOpacity={0.7}
    >
      <View style={styles.customerHeader}>
        <View style={styles.customerAvatar}>
          <Text style={styles.customerInitials}>
            {(item.full_name || item.name || 'Unknown').split(' ').map(n => n[0]).join('').toUpperCase()}
          </Text>
        </View>
        <View style={styles.customerInfo}>
          <Text style={styles.customerName}>{item.full_name || item.name || 'Unknown Customer'}</Text>
          {item.email && (
            <Text style={styles.customerDetail}>📧 {item.email}</Text>
          )}
          {item.phone && (
            <Text style={styles.customerDetail}>📞 {item.phone}</Text>
          )}
        </View>
        <View style={styles.customerActions}>
          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => handleEditCustomer(item)}
          >
            <Text style={styles.actionButtonText}>✏️</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.actionButton, styles.deleteButton]}
            onPress={() => handleDeleteCustomer(item)}
          >
            <Text style={styles.actionButtonText}>🗑️</Text>
          </TouchableOpacity>
        </View>
      </View>
      
      {item.address && (
        <Text style={styles.customerAddress}>
          📍 {item.address}
          {item.city && `, ${item.city}`}
          {item.state && `, ${item.state}`}
          {item.postal_code && ` ${item.postal_code}`}
        </Text>
      )}
      
      <View style={styles.customerFooter}>
        <Text style={styles.customerDate}>
          Added: {new Date(item.created_at).toLocaleDateString()}
        </Text>
        <Text style={styles.tapHint}>Tap for details</Text>
      </View>
    </TouchableOpacity>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyContainer}>
      <Text style={styles.emptyIcon}>👥</Text>
      <Text style={styles.emptyTitle}>No Customers Found</Text>
      <Text style={styles.emptySubtitle}>
        {searchQuery ? 'Try adjusting your search' : 'Add your first customer to get started'}
      </Text>
      {!searchQuery && (
        <TouchableOpacity
          style={styles.addFirstButton}
          onPress={() => setShowAddModal(true)}
        >
          <Text style={styles.addFirstButtonText}>Add First Customer</Text>
        </TouchableOpacity>
      )}
    </View>
  );

  const renderStats = () => (
    <View style={styles.statsContainer}>
      <View style={styles.statItem}>
        <Text style={styles.statNumber}>{stats.total}</Text>
        <Text style={styles.statLabel}>Total</Text>
      </View>
      <View style={styles.statItem}>
        <Text style={styles.statNumber}>{stats.withEmail}</Text>
        <Text style={styles.statLabel}>With Email</Text>
      </View>
      <View style={styles.statItem}>
        <Text style={styles.statNumber}>{stats.withPhone}</Text>
        <Text style={styles.statLabel}>With Phone</Text>
      </View>
      <View style={styles.statItem}>
        <Text style={styles.statNumber}>{stats.recentlyAdded}</Text>
        <Text style={styles.statLabel}>Recent</Text>
      </View>
    </View>
  );

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#FF9A8B" />
          <Text style={styles.loadingText}>Loading customers...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.headerContent}>
          <View>
            <Text style={styles.headerTitle}>👥 Customer Management</Text>
            <Text style={styles.headerSubtitle}>Manage your customer database</Text>
          </View>
          <View style={styles.headerButtons}>
            <TouchableOpacity
              style={styles.testButton}
              onPress={runCRUDTests}
            >
              <Text style={styles.testButtonText}>🧪 Test</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.addButton}
              onPress={() => setShowAddModal(true)}
            >
              <Text style={styles.addButtonText}>+ Add</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>

      {/* Search Bar */}
      <View style={styles.searchContainer}>
        <TextInput
          style={styles.searchInput}
          placeholder="Search customers by name, email, or phone..."
          value={searchQuery}
          onChangeText={setSearchQuery}
          clearButtonMode="while-editing"
        />
      </View>

      {/* Statistics */}
      {renderStats()}

      {/* Customer List */}
      <FlatList
        data={filteredCustomers}
        renderItem={renderCustomerCard}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.listContainer}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={() => loadCustomers(true)}
            colors={['#FF9A8B']}
          />
        }
        ListEmptyComponent={!loading ? renderEmptyState : null}
      />

      {/* Modals */}
      <AddCustomerModal
        visible={showAddModal}
        onClose={() => setShowAddModal(false)}
        onCustomerAdded={() => {
          loadCustomers();
          setShowAddModal(false);
        }}
      />

      <EditCustomerModal
        visible={showEditModal}
        customer={selectedCustomer}
        onClose={() => {
          setShowEditModal(false);
          setSelectedCustomer(null);
        }}
        onCustomerUpdated={() => {
          loadCustomers();
          setShowEditModal(false);
          setSelectedCustomer(null);
        }}
      />

      <CustomerDetailModal
        visible={showDetailModal}
        customer={selectedCustomer}
        onClose={() => {
          setShowDetailModal(false);
          setSelectedCustomer(null);
        }}
        onEdit={(customer) => {
          setShowDetailModal(false);
          handleEditCustomer(customer);
        }}
        onDelete={(customer) => {
          setShowDetailModal(false);
          handleDeleteCustomer(customer);
        }}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#666',
  },
  header: {
    backgroundColor: '#FF9A8B',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomLeftRadius: 20,
    borderBottomRightRadius: 20,
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#fff',
  },
  headerSubtitle: {
    fontSize: 16,
    color: '#fff',
    marginTop: 4,
    opacity: 0.9,
  },
  addButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.3)',
  },
  addButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  headerButtons: {
    flexDirection: 'row',
    gap: 8,
  },
  testButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.15)',
    borderRadius: 20,
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.25)',
  },
  testButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
  },
  searchContainer: {
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  searchInput: {
    backgroundColor: '#fff',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  statsContainer: {
    flexDirection: 'row',
    backgroundColor: '#fff',
    marginHorizontal: 16,
    marginBottom: 16,
    borderRadius: 12,
    padding: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  statItem: {
    flex: 1,
    alignItems: 'center',
  },
  statNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#FF9A8B',
  },
  statLabel: {
    fontSize: 12,
    color: '#666',
    marginTop: 4,
  },
  listContainer: {
    paddingHorizontal: 16,
    paddingBottom: 20,
  },
  customerCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  customerHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  customerAvatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#FF9A8B',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  customerInitials: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#fff',
  },
  customerInfo: {
    flex: 1,
  },
  customerName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  customerDetail: {
    fontSize: 14,
    color: '#666',
    marginBottom: 2,
  },
  customerActions: {
    flexDirection: 'row',
    gap: 8,
  },
  actionButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#f0f0f0',
    justifyContent: 'center',
    alignItems: 'center',
  },
  deleteButton: {
    backgroundColor: '#ffe6e6',
  },
  actionButtonText: {
    fontSize: 16,
  },
  customerAddress: {
    fontSize: 14,
    color: '#666',
    marginBottom: 8,
    lineHeight: 20,
  },
  customerFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingTop: 8,
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
  },
  customerDate: {
    fontSize: 12,
    color: '#999',
  },
  tapHint: {
    fontSize: 12,
    color: '#007AFF',
    fontWeight: '500',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
    paddingVertical: 64,
  },
  emptyIcon: {
    fontSize: 64,
    marginBottom: 16,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  emptySubtitle: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 24,
  },
  addFirstButton: {
    backgroundColor: '#FF9A8B',
    borderRadius: 12,
    paddingHorizontal: 24,
    paddingVertical: 12,
  },
  addFirstButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default CustomerManagementScreen;
