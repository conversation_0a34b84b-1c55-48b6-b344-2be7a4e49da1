/**
 * Ocean Soul Sparkles Mobile App - Auth Compatibility Test
 * Tests mobile app auth tokens work with admin portal RLS policies
 */

const { createClient } = require('@supabase/supabase-js');

// Supabase configuration (same as mobile app)
const supabaseUrl = 'https://axdfyhqqjgsqdgypmmkj.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImF4ZGZ5aHFxamdzc...'; // Replace with actual key

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function testAuthCompatibility() {
  console.log('🔐 Testing Ocean Soul Sparkles Mobile App Auth Compatibility...\n');

  try {
    // Test 1: Test admin portal API authentication
    console.log('📋 Test 1: Admin Portal API Authentication');
    const adminPortalResponse = await fetch('https://admin.oceansoulsparkles.com.au/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>', // Test credentials
        password: 'dev123',
      }),
    });

    const adminResult = await adminPortalResponse.json();

    if (adminResult.success) {
      console.log('✅ Admin portal authentication successful');
      console.log(`   User ID: ${adminResult.user.id}`);
      console.log(`   Email: ${adminResult.user.email}`);
      console.log(`   Role: ${adminResult.user.role}`);
    } else {
      console.log('❌ Admin portal authentication failed:', adminResult.error);
      return;
    }

    // Test 2: Test Supabase authentication with same credentials
    console.log('\n📋 Test 2: Supabase Authentication');
    const { data: supabaseAuth, error: supabaseError } = await supabase.auth.signInWithPassword({
      email: '<EMAIL>',
      password: 'dev123',
    });

    if (supabaseError) {
      console.log('❌ Supabase authentication failed:', supabaseError.message);
      console.log('⚠️  This means RLS policies will not work properly');
    } else {
      console.log('✅ Supabase authentication successful');
      console.log(`   User ID: ${supabaseAuth.user.id}`);
      console.log(`   Email: ${supabaseAuth.user.email}`);
    }

    // Test 3: Test RLS policy access
    console.log('\n📋 Test 3: RLS Policy Access Tests');

    // Test admin_users table access
    console.log('   Testing admin_users table access...');
    const { data: adminUsers, error: adminUsersError } = await supabase
      .from('admin_users')
      .select('id, email, role')
      .limit(1);

    if (adminUsersError) {
      console.log('   ❌ admin_users access failed:', adminUsersError.message);
    } else {
      console.log('   ✅ admin_users access successful');
    }

    // Test chat_threads table access
    console.log('   Testing chat_threads table access...');
    const { data: chatThreads, error: chatThreadsError } = await supabase
      .from('chat_threads')
      .select('id, title')
      .limit(1);

    if (chatThreadsError) {
      console.log('   ❌ chat_threads access failed:', chatThreadsError.message);
    } else {
      console.log('   ✅ chat_threads access successful');
    }

    // Test staff_notification_preferences table access
    console.log('   Testing staff_notification_preferences table access...');
    const { data: notifPrefs, error: notifPrefsError } = await supabase
      .from('staff_notification_preferences')
      .select('user_id, enable_booking_notifications')
      .limit(1);

    if (notifPrefsError) {
      console.log('   ❌ staff_notification_preferences access failed:', notifPrefsError.message);
    } else {
      console.log('   ✅ staff_notification_preferences access successful');
    }

    // Test 4: Test write operations
    console.log('\n📋 Test 4: Write Operations Test');

    if (supabaseAuth?.user) {
      // Test creating notification preferences
      console.log('   Testing notification preferences upsert...');
      const { error: upsertError } = await supabase
        .from('staff_notification_preferences')
        .upsert({
          user_id: supabaseAuth.user.id,
          enable_booking_notifications: true,
          enable_chat_notifications: true,
          push_token: 'test-token-' + Date.now(),
        });

      if (upsertError) {
        console.log('   ❌ Notification preferences upsert failed:', upsertError.message);
      } else {
        console.log('   ✅ Notification preferences upsert successful');
      }
    }

    console.log('\n🎯 Auth Compatibility Test Summary:');
    console.log('   - Admin Portal API: ✅ Working');
    console.log('   - Supabase Auth: ' + (supabaseError ? '❌ Failed' : '✅ Working'));
    console.log('   - RLS Policies: ' + (adminUsersError || chatThreadsError || notifPrefsError ? '⚠️ Partial' : '✅ Working'));

    if (supabaseError) {
      console.log('\n⚠️  RECOMMENDATION: Set up Supabase user accounts for mobile app staff');
      console.log('   This will enable full RLS policy compatibility');
    }

  } catch (error) {
    console.error('❌ Auth compatibility test failed:', error);
  } finally {
    // Clean up - sign out
    await supabase.auth.signOut();
  }
}

// Run the test
testAuthCompatibility();