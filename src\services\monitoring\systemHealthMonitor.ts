/**
 * Ocean Soul Sparkles Mobile App - System Health Monitor
 * Real-time monitoring of all system components and health metrics
 */

import { productionReadinessValidator, ProductionReadinessStatus } from '@/services/validation/productionReadinessValidator';
import { adminPortalValidator, AdminPortalSyncStatus } from '@/services/validation/adminPortalValidator';
import { databaseSchemaValidator, DatabaseSchemaStatus } from '@/services/validation/databaseSchemaValidator';
import { databasePerformanceValidator, DatabasePerformanceStatus } from '@/services/validation/databasePerformanceValidator';
import { emailTesting, EmailSystemStatus } from '@/utils/emailTesting';

export interface SystemHealthMetrics {
  timestamp: string;
  overall: 'healthy' | 'degraded' | 'critical' | 'unknown';
  healthScore: number; // 0-100
  systems: {
    productionReadiness: ProductionReadinessStatus;
    adminPortal: AdminPortalSyncStatus;
    database: DatabaseSchemaStatus & DatabasePerformanceStatus;
    emailSystem: EmailSystemStatus;
  };
  performance: {
    averageResponseTime: number;
    errorRate: number;
    uptime: number;
    lastHealthCheck: string;
  };
  alerts: SystemAlert[];
  trends: HealthTrend[];
}

export interface SystemAlert {
  id: string;
  severity: 'critical' | 'warning' | 'info';
  system: 'production' | 'admin_portal' | 'database' | 'email' | 'general';
  message: string;
  timestamp: string;
  resolved: boolean;
  details?: any;
}

export interface HealthTrend {
  system: string;
  metric: string;
  value: number;
  timestamp: string;
  trend: 'improving' | 'stable' | 'degrading';
}

export interface MonitoringConfig {
  enabled: boolean;
  checkInterval: number; // milliseconds
  alertThresholds: {
    healthScore: number;
    responseTime: number;
    errorRate: number;
  };
  retentionPeriod: number; // hours
}

export class SystemHealthMonitor {
  private static instance: SystemHealthMonitor;
  private isMonitoring = false;
  private monitoringInterval: NodeJS.Timeout | null = null;
  private healthHistory: SystemHealthMetrics[] = [];
  private alerts: SystemAlert[] = [];
  private startTime = Date.now();

  private readonly DEFAULT_CONFIG: MonitoringConfig = {
    enabled: true,
    checkInterval: 30000, // 30 seconds
    alertThresholds: {
      healthScore: 80,
      responseTime: 2000,
      errorRate: 5,
    },
    retentionPeriod: 24, // 24 hours
  };

  private constructor() {}

  public static getInstance(): SystemHealthMonitor {
    if (!SystemHealthMonitor.instance) {
      SystemHealthMonitor.instance = new SystemHealthMonitor();
    }
    return SystemHealthMonitor.instance;
  }

  /**
   * Start continuous health monitoring
   */
  async startMonitoring(config: Partial<MonitoringConfig> = {}): Promise<void> {
    if (this.isMonitoring) {
      console.log('📊 System health monitoring already running');
      return;
    }

    const monitoringConfig = { ...this.DEFAULT_CONFIG, ...config };
    
    if (!monitoringConfig.enabled) {
      console.log('📊 System health monitoring disabled');
      return;
    }

    console.log('📊 Starting system health monitoring...');
    this.isMonitoring = true;
    this.startTime = Date.now();

    // Perform initial health check
    await this.performHealthCheck();

    // Set up periodic monitoring
    this.monitoringInterval = setInterval(async () => {
      try {
        await this.performHealthCheck();
        this.cleanupOldData(monitoringConfig.retentionPeriod);
      } catch (error) {
        console.error('❌ Health check failed:', error);
        this.addAlert({
          severity: 'critical',
          system: 'general',
          message: 'Health monitoring system error',
          details: { error: error instanceof Error ? error.message : 'Unknown error' },
        });
      }
    }, monitoringConfig.checkInterval);

    console.log(`✅ System health monitoring started (interval: ${monitoringConfig.checkInterval}ms)`);
  }

  /**
   * Stop health monitoring
   */
  stopMonitoring(): void {
    if (!this.isMonitoring) {
      return;
    }

    console.log('📊 Stopping system health monitoring...');
    
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
    }

    this.isMonitoring = false;
    console.log('✅ System health monitoring stopped');
  }

  /**
   * Perform comprehensive health check
   */
  async performHealthCheck(): Promise<SystemHealthMetrics> {
    const startTime = Date.now();
    
    try {
      console.log('🔍 Performing system health check...');

      // Get status from all systems
      const [
        productionReadiness,
        adminPortal,
        databaseSchema,
        databasePerformance,
        emailSystem,
      ] = await Promise.allSettled([
        productionReadinessValidator.getProductionReadinessStatus(),
        adminPortalValidator.getAdminPortalSyncStatus(),
        databaseSchemaValidator.getDatabaseSchemaStatus(),
        databasePerformanceValidator.getDatabasePerformanceStatus(),
        emailTesting.getEmailSystemStatus(),
      ]);

      // Extract results
      const productionReadinessStatus = this.extractResult(productionReadiness, {
        enhancedWorkflow: false,
        emailSystem: false,
        adminPortal: false,
        database: false,
        overall: false,
        readinessPercentage: 0,
      });

      const adminPortalStatus = this.extractResult(adminPortal, {
        connectivity: false,
        authentication: false,
        dataSync: false,
        apiEndpoints: false,
        overall: false,
      });

      const databaseSchemaStatus = this.extractResult(databaseSchema, {
        coreTablesExist: false,
        transactionTablesExist: false,
        constraintsValid: false,
        indexesOptimal: false,
        rlsPoliciesActive: false,
        overall: false,
      });

      const databasePerformanceStatus = this.extractResult(databasePerformance, {
        queryPerformance: false,
        indexOptimization: false,
        connectionStability: false,
        memoryUsage: false,
        overall: false,
      });

      const emailSystemStatus = this.extractResult(emailSystem, {
        emailService: false,
        authentication: false,
        templates: false,
        adminPortalConnection: false,
        overall: false,
      });

      // Calculate health score
      const healthScore = this.calculateHealthScore({
        productionReadiness: productionReadinessStatus,
        adminPortal: adminPortalStatus,
        database: { ...databaseSchemaStatus, ...databasePerformanceStatus },
        emailSystem: emailSystemStatus,
      });

      // Determine overall health status
      const overall = this.determineOverallHealth(healthScore);

      // Calculate performance metrics
      const responseTime = Date.now() - startTime;
      const uptime = (Date.now() - this.startTime) / 1000; // seconds
      const errorRate = this.calculateErrorRate();

      // Generate alerts for any issues
      await this.checkForAlerts({
        healthScore,
        responseTime,
        systems: {
          productionReadiness: productionReadinessStatus,
          adminPortal: adminPortalStatus,
          database: { ...databaseSchemaStatus, ...databasePerformanceStatus },
          emailSystem: emailSystemStatus,
        },
      });

      // Calculate trends
      const trends = this.calculateTrends();

      const healthMetrics: SystemHealthMetrics = {
        timestamp: new Date().toISOString(),
        overall,
        healthScore,
        systems: {
          productionReadiness: productionReadinessStatus,
          adminPortal: adminPortalStatus,
          database: { ...databaseSchemaStatus, ...databasePerformanceStatus },
          emailSystem: emailSystemStatus,
        },
        performance: {
          averageResponseTime: responseTime,
          errorRate,
          uptime,
          lastHealthCheck: new Date().toISOString(),
        },
        alerts: this.getActiveAlerts(),
        trends,
      };

      // Store in history
      this.healthHistory.push(healthMetrics);

      console.log(`✅ Health check completed: ${healthScore}% (${overall}) in ${responseTime}ms`);

      return healthMetrics;

    } catch (error) {
      console.error('❌ Health check failed:', error);
      
      const errorMetrics: SystemHealthMetrics = {
        timestamp: new Date().toISOString(),
        overall: 'critical',
        healthScore: 0,
        systems: {
          productionReadiness: {
            enhancedWorkflow: false,
            emailSystem: false,
            adminPortal: false,
            database: false,
            overall: false,
            readinessPercentage: 0,
          },
          adminPortal: {
            connectivity: false,
            authentication: false,
            dataSync: false,
            apiEndpoints: false,
            overall: false,
          },
          database: {
            coreTablesExist: false,
            transactionTablesExist: false,
            constraintsValid: false,
            indexesOptimal: false,
            rlsPoliciesActive: false,
            queryPerformance: false,
            indexOptimization: false,
            connectionStability: false,
            memoryUsage: false,
            overall: false,
          },
          emailSystem: {
            emailService: false,
            authentication: false,
            templates: false,
            adminPortalConnection: false,
            overall: false,
          },
        },
        performance: {
          averageResponseTime: Date.now() - startTime,
          errorRate: 100,
          uptime: (Date.now() - this.startTime) / 1000,
          lastHealthCheck: new Date().toISOString(),
        },
        alerts: this.getActiveAlerts(),
        trends: [],
      };

      this.addAlert({
        severity: 'critical',
        system: 'general',
        message: 'System health check failed',
        details: { error: error instanceof Error ? error.message : 'Unknown error' },
      });

      return errorMetrics;
    }
  }

  /**
   * Extract result from settled promise
   */
  private extractResult<T>(settledResult: PromiseSettledResult<T>, fallback: T): T {
    if (settledResult.status === 'fulfilled') {
      return settledResult.value;
    } else {
      console.warn('Health check promise rejected:', settledResult.reason);
      return fallback;
    }
  }

  /**
   * Calculate overall health score
   */
  private calculateHealthScore(systems: {
    productionReadiness: ProductionReadinessStatus;
    adminPortal: AdminPortalSyncStatus;
    database: DatabaseSchemaStatus & DatabasePerformanceStatus;
    emailSystem: EmailSystemStatus;
  }): number {
    let score = 0;
    let maxScore = 0;

    // Production readiness (30% weight)
    maxScore += 30;
    if (systems.productionReadiness.overall) score += 30;
    else score += (systems.productionReadiness.readinessPercentage / 100) * 30;

    // Admin portal (25% weight)
    maxScore += 25;
    const adminPortalScore = [
      systems.adminPortal.connectivity,
      systems.adminPortal.authentication,
      systems.adminPortal.dataSync,
      systems.adminPortal.apiEndpoints,
    ].filter(Boolean).length / 4;
    score += adminPortalScore * 25;

    // Database (30% weight)
    maxScore += 30;
    const databaseScore = [
      systems.database.coreTablesExist,
      systems.database.constraintsValid,
      systems.database.rlsPoliciesActive,
      systems.database.queryPerformance,
      systems.database.connectionStability,
    ].filter(Boolean).length / 5;
    score += databaseScore * 30;

    // Email system (15% weight)
    maxScore += 15;
    if (systems.emailSystem.overall) score += 15;

    return Math.round((score / maxScore) * 100);
  }

  /**
   * Determine overall health status
   */
  private determineOverallHealth(healthScore: number): 'healthy' | 'degraded' | 'critical' | 'unknown' {
    if (healthScore >= 90) return 'healthy';
    if (healthScore >= 70) return 'degraded';
    if (healthScore > 0) return 'critical';
    return 'unknown';
  }

  /**
   * Calculate error rate from recent health checks
   */
  private calculateErrorRate(): number {
    const recentChecks = this.healthHistory.slice(-10); // Last 10 checks
    if (recentChecks.length === 0) return 0;
    
    const errors = recentChecks.filter(check => check.overall === 'critical').length;
    return (errors / recentChecks.length) * 100;
  }

  /**
   * Check for alerts based on current metrics
   */
  private async checkForAlerts(data: {
    healthScore: number;
    responseTime: number;
    systems: any;
  }): Promise<void> {
    // Health score alert
    if (data.healthScore < this.DEFAULT_CONFIG.alertThresholds.healthScore) {
      this.addAlert({
        severity: data.healthScore < 50 ? 'critical' : 'warning',
        system: 'general',
        message: `System health score below threshold: ${data.healthScore}%`,
        details: { healthScore: data.healthScore, threshold: this.DEFAULT_CONFIG.alertThresholds.healthScore },
      });
    }

    // Response time alert
    if (data.responseTime > this.DEFAULT_CONFIG.alertThresholds.responseTime) {
      this.addAlert({
        severity: 'warning',
        system: 'general',
        message: `Health check response time high: ${data.responseTime}ms`,
        details: { responseTime: data.responseTime, threshold: this.DEFAULT_CONFIG.alertThresholds.responseTime },
      });
    }

    // System-specific alerts
    if (!data.systems.adminPortal.connectivity) {
      this.addAlert({
        severity: 'critical',
        system: 'admin_portal',
        message: 'Admin portal connectivity lost',
        details: { adminPortalStatus: data.systems.adminPortal },
      });
    }

    if (!data.systems.database.overall) {
      this.addAlert({
        severity: 'critical',
        system: 'database',
        message: 'Database system issues detected',
        details: { databaseStatus: data.systems.database },
      });
    }

    if (!data.systems.emailSystem.overall) {
      this.addAlert({
        severity: 'warning',
        system: 'email',
        message: 'Email system not functioning properly',
        details: { emailStatus: data.systems.emailSystem },
      });
    }
  }

  /**
   * Add new alert
   */
  private addAlert(alert: Omit<SystemAlert, 'id' | 'timestamp' | 'resolved'>): void {
    const newAlert: SystemAlert = {
      id: `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date().toISOString(),
      resolved: false,
      ...alert,
    };

    this.alerts.push(newAlert);
    console.log(`🚨 Alert: [${alert.severity.toUpperCase()}] ${alert.message}`);
  }

  /**
   * Get active (unresolved) alerts
   */
  private getActiveAlerts(): SystemAlert[] {
    return this.alerts.filter(alert => !alert.resolved);
  }

  /**
   * Calculate health trends
   */
  private calculateTrends(): HealthTrend[] {
    if (this.healthHistory.length < 2) return [];

    const trends: HealthTrend[] = [];
    const recent = this.healthHistory.slice(-5); // Last 5 checks

    if (recent.length >= 2) {
      const latest = recent[recent.length - 1];
      const previous = recent[recent.length - 2];

      // Overall health trend
      const healthTrend = latest.healthScore > previous.healthScore ? 'improving' :
                         latest.healthScore < previous.healthScore ? 'degrading' : 'stable';

      trends.push({
        system: 'overall',
        metric: 'health_score',
        value: latest.healthScore,
        timestamp: latest.timestamp,
        trend: healthTrend,
      });

      // Response time trend
      const responseTrend = latest.performance.averageResponseTime < previous.performance.averageResponseTime ? 'improving' :
                           latest.performance.averageResponseTime > previous.performance.averageResponseTime ? 'degrading' : 'stable';

      trends.push({
        system: 'performance',
        metric: 'response_time',
        value: latest.performance.averageResponseTime,
        timestamp: latest.timestamp,
        trend: responseTrend,
      });
    }

    return trends;
  }

  /**
   * Clean up old data
   */
  private cleanupOldData(retentionHours: number): void {
    const cutoffTime = Date.now() - (retentionHours * 60 * 60 * 1000);
    
    // Clean up health history
    this.healthHistory = this.healthHistory.filter(
      metrics => new Date(metrics.timestamp).getTime() > cutoffTime
    );

    // Clean up resolved alerts
    this.alerts = this.alerts.filter(
      alert => !alert.resolved || new Date(alert.timestamp).getTime() > cutoffTime
    );
  }

  /**
   * Get current health metrics
   */
  getCurrentHealthMetrics(): SystemHealthMetrics | null {
    return this.healthHistory.length > 0 ? this.healthHistory[this.healthHistory.length - 1] : null;
  }

  /**
   * Get health history
   */
  getHealthHistory(limit?: number): SystemHealthMetrics[] {
    return limit ? this.healthHistory.slice(-limit) : this.healthHistory;
  }

  /**
   * Resolve alert
   */
  resolveAlert(alertId: string): boolean {
    const alert = this.alerts.find(a => a.id === alertId);
    if (alert) {
      alert.resolved = true;
      console.log(`✅ Alert resolved: ${alert.message}`);
      return true;
    }
    return false;
  }

  /**
   * Get monitoring status
   */
  getMonitoringStatus(): {
    isMonitoring: boolean;
    uptime: number;
    totalChecks: number;
    activeAlerts: number;
  } {
    return {
      isMonitoring: this.isMonitoring,
      uptime: (Date.now() - this.startTime) / 1000,
      totalChecks: this.healthHistory.length,
      activeAlerts: this.getActiveAlerts().length,
    };
  }
}

// Export singleton instance
export const systemHealthMonitor = SystemHealthMonitor.getInstance();
