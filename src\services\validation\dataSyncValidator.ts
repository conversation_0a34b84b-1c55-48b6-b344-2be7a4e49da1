/**
 * Ocean Soul Sparkles Mobile App - Data Synchronization Validator
 * Validates real-time data sync between mobile app and admin portal
 */

import { supabase } from '@/services/database/supabase';
import { bookingService } from '@/services/database/bookingService';
import { customerService } from '@/services/database/customerService';
import { quoteService } from '@/services/database/quoteService';
import { Booking, Customer, Quote } from '@/types/database';

export interface DataSyncValidationResult {
  test: string;
  status: 'pass' | 'fail' | 'warning';
  message: string;
  duration: number;
  details?: any;
  error?: string;
}

export interface SyncTestRecord {
  id: string;
  type: 'booking' | 'customer' | 'quote';
  created: boolean;
  updated: boolean;
  deleted: boolean;
  syncTime: number;
}

export class DataSyncValidator {
  private static instance: DataSyncValidator;
  private testRecords: SyncTestRecord[] = [];

  private constructor() {}

  public static getInstance(): DataSyncValidator {
    if (!DataSyncValidator.instance) {
      DataSyncValidator.instance = new DataSyncValidator();
    }
    return DataSyncValidator.instance;
  }

  /**
   * Test real-time booking synchronization
   */
  async testBookingSynchronization(): Promise<DataSyncValidationResult> {
    const startTime = Date.now();
    
    try {
      console.log('📅 Testing booking data synchronization...');

      // Create a test booking
      const testBookingData: Partial<Booking> = {
        customer_id: 'test-customer-sync-' + Date.now(),
        service_id: 'test-service-sync-' + Date.now(),
        booking_date: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        start_time: '10:00',
        end_time: '11:00',
        status: 'pending',
        notes: `Sync test booking created at ${new Date().toISOString()}`,
      };

      // Test CREATE operation
      const createResult = await bookingService.createBooking(testBookingData);
      if (createResult.error || !createResult.data) {
        return {
          test: 'Booking Synchronization',
          status: 'fail',
          message: 'Failed to create test booking',
          duration: Date.now() - startTime,
          error: createResult.error?.message || 'Create operation failed',
        };
      }

      const testBooking = createResult.data;
      const createTime = Date.now() - startTime;

      // Test READ operation (verify creation sync)
      const readResult = await bookingService.getBookingById(testBooking.id);
      if (readResult.error || !readResult.data) {
        return {
          test: 'Booking Synchronization',
          status: 'fail',
          message: 'Failed to read created booking',
          duration: Date.now() - startTime,
          error: readResult.error?.message || 'Read operation failed',
        };
      }

      const readTime = Date.now() - startTime;

      // Test UPDATE operation
      const updateResult = await bookingService.updateBooking(testBooking.id, {
        notes: `${testBooking.notes}\nUpdated during sync test at ${new Date().toISOString()}`,
        status: 'confirmed',
      });

      if (updateResult.error) {
        return {
          test: 'Booking Synchronization',
          status: 'warning',
          message: 'Booking created and read successfully, but update failed',
          duration: Date.now() - startTime,
          details: {
            createSuccess: true,
            readSuccess: true,
            updateSuccess: false,
            createTime,
            readTime,
            updateError: updateResult.error.message,
          },
        };
      }

      const updateTime = Date.now() - startTime;

      // Test DELETE operation (cleanup)
      const deleteResult = await bookingService.deleteBooking(testBooking.id);
      const deleteSuccess = !deleteResult.error;
      const deleteTime = Date.now() - startTime;

      // Record test results
      this.testRecords.push({
        id: testBooking.id,
        type: 'booking',
        created: true,
        updated: !updateResult.error,
        deleted: deleteSuccess,
        syncTime: deleteTime,
      });

      return {
        test: 'Booking Synchronization',
        status: deleteSuccess ? 'pass' : 'warning',
        message: deleteSuccess 
          ? 'Booking CRUD operations synchronized successfully'
          : 'Booking created and updated successfully, cleanup warning',
        duration: Date.now() - startTime,
        details: {
          bookingId: testBooking.id,
          createTime,
          readTime,
          updateTime,
          deleteTime,
          deleteSuccess,
          operations: {
            create: true,
            read: true,
            update: !updateResult.error,
            delete: deleteSuccess,
          },
        },
      };

    } catch (error) {
      return {
        test: 'Booking Synchronization',
        status: 'fail',
        message: 'Booking synchronization test failed',
        duration: Date.now() - startTime,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Test customer data synchronization
   */
  async testCustomerSynchronization(): Promise<DataSyncValidationResult> {
    const startTime = Date.now();
    
    try {
      console.log('👤 Testing customer data synchronization...');

      // Create a test customer
      const testCustomerData: Partial<Customer> = {
        email: `sync-test-${Date.now()}@oceansoulsparkles.com.au`,
        full_name: 'Sync Test Customer',
        phone: '+61 400 000 000',
        address: '123 Sync Test Street, Sydney, NSW 2000',
        notes: `Sync test customer created at ${new Date().toISOString()}`,
      };

      // Test CREATE operation
      const createResult = await customerService.createCustomer(testCustomerData);
      if (createResult.error || !createResult.data) {
        return {
          test: 'Customer Synchronization',
          status: 'fail',
          message: 'Failed to create test customer',
          duration: Date.now() - startTime,
          error: createResult.error?.message || 'Create operation failed',
        };
      }

      const testCustomer = createResult.data;
      const createTime = Date.now() - startTime;

      // Test READ operation
      const readResult = await customerService.getCustomerById(testCustomer.id);
      if (readResult.error || !readResult.data) {
        return {
          test: 'Customer Synchronization',
          status: 'fail',
          message: 'Failed to read created customer',
          duration: Date.now() - startTime,
          error: readResult.error?.message || 'Read operation failed',
        };
      }

      const readTime = Date.now() - startTime;

      // Test UPDATE operation
      const updateResult = await customerService.updateCustomer(testCustomer.id, {
        notes: `${testCustomer.notes}\nUpdated during sync test at ${new Date().toISOString()}`,
        phone: '+61 400 111 222',
      });

      const updateSuccess = !updateResult.error;
      const updateTime = Date.now() - startTime;

      // Test DELETE operation (cleanup)
      const deleteResult = await customerService.deleteCustomer(testCustomer.id);
      const deleteSuccess = !deleteResult.error;
      const deleteTime = Date.now() - startTime;

      // Record test results
      this.testRecords.push({
        id: testCustomer.id,
        type: 'customer',
        created: true,
        updated: updateSuccess,
        deleted: deleteSuccess,
        syncTime: deleteTime,
      });

      return {
        test: 'Customer Synchronization',
        status: updateSuccess && deleteSuccess ? 'pass' : 'warning',
        message: updateSuccess && deleteSuccess
          ? 'Customer CRUD operations synchronized successfully'
          : 'Customer created successfully, some operations had issues',
        duration: Date.now() - startTime,
        details: {
          customerId: testCustomer.id,
          createTime,
          readTime,
          updateTime,
          deleteTime,
          operations: {
            create: true,
            read: true,
            update: updateSuccess,
            delete: deleteSuccess,
          },
        },
      };

    } catch (error) {
      return {
        test: 'Customer Synchronization',
        status: 'fail',
        message: 'Customer synchronization test failed',
        duration: Date.now() - startTime,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Test quote data synchronization
   */
  async testQuoteSynchronization(): Promise<DataSyncValidationResult> {
    const startTime = Date.now();
    
    try {
      console.log('💰 Testing quote data synchronization...');

      // Create a test quote
      const testQuoteData: Partial<Quote> = {
        customer_id: 'test-customer-quote-' + Date.now(),
        staff_id: 'test-staff-quote-' + Date.now(),
        title: 'Sync Test Quote',
        description: `Test quote for synchronization validation created at ${new Date().toISOString()}`,
        total_amount: 150.00,
        status: 'draft',
        valid_until: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
        notes: 'Sync test quote',
      };

      // Test CREATE operation
      const createResult = await quoteService.createQuote(testQuoteData);
      if (createResult.error || !createResult.data) {
        return {
          test: 'Quote Synchronization',
          status: 'fail',
          message: 'Failed to create test quote',
          duration: Date.now() - startTime,
          error: createResult.error?.message || 'Create operation failed',
        };
      }

      const testQuote = createResult.data;
      const createTime = Date.now() - startTime;

      // Test READ operation
      const readResult = await quoteService.getQuoteById(testQuote.id);
      if (readResult.error || !readResult.data) {
        return {
          test: 'Quote Synchronization',
          status: 'fail',
          message: 'Failed to read created quote',
          duration: Date.now() - startTime,
          error: readResult.error?.message || 'Read operation failed',
        };
      }

      const readTime = Date.now() - startTime;

      // Test UPDATE operation
      const updateResult = await quoteService.updateQuote(testQuote.id, {
        status: 'sent',
        notes: `${testQuote.notes}\nUpdated during sync test at ${new Date().toISOString()}`,
        estimated_total: 175.00,
      });

      const updateSuccess = !updateResult.error;
      const updateTime = Date.now() - startTime;

      // Test DELETE operation (cleanup)
      const deleteResult = await quoteService.deleteQuote(testQuote.id);
      const deleteSuccess = !deleteResult.error;
      const deleteTime = Date.now() - startTime;

      // Record test results
      this.testRecords.push({
        id: testQuote.id,
        type: 'quote',
        created: true,
        updated: updateSuccess,
        deleted: deleteSuccess,
        syncTime: deleteTime,
      });

      return {
        test: 'Quote Synchronization',
        status: updateSuccess && deleteSuccess ? 'pass' : 'warning',
        message: updateSuccess && deleteSuccess
          ? 'Quote CRUD operations synchronized successfully'
          : 'Quote created successfully, some operations had issues',
        duration: Date.now() - startTime,
        details: {
          quoteId: testQuote.id,
          createTime,
          readTime,
          updateTime,
          deleteTime,
          operations: {
            create: true,
            read: true,
            update: updateSuccess,
            delete: deleteSuccess,
          },
        },
      };

    } catch (error) {
      return {
        test: 'Quote Synchronization',
        status: 'fail',
        message: 'Quote synchronization test failed',
        duration: Date.now() - startTime,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Test real-time subscription functionality
   */
  async testRealTimeSubscriptions(): Promise<DataSyncValidationResult> {
    const startTime = Date.now();
    
    try {
      console.log('⚡ Testing real-time subscription functionality...');

      let subscriptionWorking = false;
      let subscriptionError: string | null = null;

      // Test real-time subscription to bookings table
      const subscription = supabase
        .channel('sync-test-bookings')
        .on('postgres_changes', 
          { 
            event: '*', 
            schema: 'public', 
            table: 'bookings' 
          }, 
          (payload) => {
            console.log('📡 Real-time update received:', payload);
            subscriptionWorking = true;
          }
        )
        .subscribe((status) => {
          console.log('📡 Subscription status:', status);
          if (status === 'SUBSCRIBED') {
            subscriptionWorking = true;
          }
        });

      // Wait for subscription to establish
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Create a test record to trigger real-time update
      const testData = {
        customer_id: 'realtime-test-' + Date.now(),
        service_id: 'realtime-test-service',
        booking_date: new Date().toISOString().split('T')[0],
        start_time: '15:00',
        status: 'pending',
        notes: 'Real-time subscription test',
      };

      const createResult = await bookingService.createBooking(testData);
      
      // Wait for real-time notification
      await new Promise(resolve => setTimeout(resolve, 3000));

      // Cleanup
      if (createResult.data) {
        await bookingService.deleteBooking(createResult.data.id);
      }
      
      subscription.unsubscribe();

      return {
        test: 'Real-time Subscriptions',
        status: subscriptionWorking ? 'pass' : 'warning',
        message: subscriptionWorking 
          ? 'Real-time subscriptions working correctly'
          : 'Real-time subscriptions may not be functioning',
        duration: Date.now() - startTime,
        details: {
          subscriptionEstablished: subscriptionWorking,
          testRecordCreated: !!createResult.data,
          subscriptionError,
        },
      };

    } catch (error) {
      return {
        test: 'Real-time Subscriptions',
        status: 'fail',
        message: 'Real-time subscription test failed',
        duration: Date.now() - startTime,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Run comprehensive data synchronization validation
   */
  async runComprehensiveDataSyncValidation(): Promise<DataSyncValidationResult[]> {
    console.log('🧪 Running comprehensive data synchronization validation...');

    this.testRecords = []; // Reset test records

    const results = await Promise.all([
      this.testBookingSynchronization(),
      this.testCustomerSynchronization(),
      this.testQuoteSynchronization(),
      this.testRealTimeSubscriptions(),
    ]);

    const passedTests = results.filter(r => r.status === 'pass').length;
    const totalTests = results.length;

    console.log(`✅ Data sync validation completed: ${passedTests}/${totalTests} tests passed`);
    console.log(`📊 Test records created: ${this.testRecords.length}`);

    return results;
  }

  /**
   * Get test records summary
   */
  getTestRecordsSummary(): {
    totalRecords: number;
    byType: Record<string, number>;
    operationSuccess: {
      created: number;
      updated: number;
      deleted: number;
    };
    averageSyncTime: number;
  } {
    const byType = this.testRecords.reduce((acc, record) => {
      acc[record.type] = (acc[record.type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const operationSuccess = {
      created: this.testRecords.filter(r => r.created).length,
      updated: this.testRecords.filter(r => r.updated).length,
      deleted: this.testRecords.filter(r => r.deleted).length,
    };

    const averageSyncTime = this.testRecords.length > 0
      ? this.testRecords.reduce((sum, r) => sum + r.syncTime, 0) / this.testRecords.length
      : 0;

    return {
      totalRecords: this.testRecords.length,
      byType,
      operationSuccess,
      averageSyncTime,
    };
  }

  /**
   * Clear test records
   */
  clearTestRecords(): void {
    this.testRecords = [];
  }
}

// Export singleton instance
export const dataSyncValidator = DataSyncValidator.getInstance();
